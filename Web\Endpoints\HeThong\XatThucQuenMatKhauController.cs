﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.Commands;

namespace NHATTAMID2025.Web.Endpoints.HeThong;


public class XatThucQuenMatKhauController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(XatThucQuenMatKhau, "");
    }
    public async Task<Result<object>?> XatThucQuenMatKhau(ISender sender, [FromBody] XatThucQuenMatKhauCommand command)
    {
        return await sender.Send(command);
    }
}
