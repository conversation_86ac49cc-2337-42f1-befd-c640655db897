﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Domain.Entities;
public class <PERSON><PERSON><PERSON>oat<PERSON>ey
{
    [Key]
    public Guid KichHoatID { get; set; }
    public Guid? KeyID { get; set; }
    public Guid? UserID { get; set; }
    public string? TenThietBi { get; set; }
    public string? IP<PERSON>ich<PERSON>oat { get; set; }
    public DateTime? Ngay<PERSON><PERSON>Hoat { get; set; }
    public string? HeDieuHanh { get; set; }
    public string? TrangThai { get; set; }
}
