﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Authentication;

namespace NHATTAMID2025.Web.Endpoints;

public class Authentication : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        var group = app.MapGroup(this)
            .MapPost(Login, "login");
    }

    public async Task<TokenResponse> Login(ISender sender, [FromBody] LoginCommand query)
    {
        return await sender.Send(query);
    }
}
