﻿var ThaoTac = 'Them';
const danhSachLoaiUsers = [
  { phanMemID: "USER", tenPhanMem: "Tự đăng ký", icon: "fas fa-user-cog  text-green" },
  { phanMemID: "GUEST", tenPhanMem: "<PERSON><PERSON><PERSON><PERSON> cấp tài khoản", icon: "fas fa-user text-yellow" },
  { phanMemID: "ADMIN", tenPhanMem: "Quản trị viên", icon: "fas fa-shield-alt  text-red" }
];

const danhSachGioiTinh = [
  { ID: "Nam", ten: "Nam", icon: "fas fa-mars text-primary" },
  { ID: "Nu", ten: "Nữ", icon: "fas fa-venus text-pink" }
];
$(document).ready(async function () {

  tablerSelect("LoaiUsers", danhSachLoaiUsers, "tenPhanMem", "phanMemID","Chọn nhóm tài khoản");
 tablerSelect("GioiTinh", danhSachGioiTinh, "ten", "ID","Chọn giới tính");

  Load<PERSON>uo<PERSON>()
});
//rowGroup: {
//  dataSrc: 'loaiUsers',
//  startRender: function (rows, group) {
//    const loaiUsersMap = {
//      USER: {
//        text: "Người dùng nội bộ",
//        icon: "fas fa-user-cog text-green"
//      },
//      GUEST: {
//        text: "Người dùng bên ngoài",
//        icon: "fas fa-user text-yellow"
//      },
//      ADMIN: {
//        text: "Quản trị viên",
//        icon: "fas fa-shield-alt text-red"
//      }
//    };

//    const info = loaiUsersMap[group] || { text: group, icon: "" };
//    return `<strong><i class="${info.icon}"></i> ${info.text} (${rows.count()} người dùng)</strong>`;
//  }
//},

async function LoadLuoi() {
  debugger
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/NguoiDung/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#phanMemTable').DataTable().destroy();
    $('#phanMemTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },
 
      columns: [
        {
          data: 'loaiUsers',
          width: "90px",
          render: function (data, type, row) {
            const info = danhSachLoaiUsers.find(x => x.phanMemID === data) || { tenPhanMem: data, icon: "" };
            return `<span class="badge bg-green-lt text-green"><i class="${info.icon}"></i> ${ info.tenPhanMem }</span> `;
          }
        },
        { data: 'tenDangNhap', width: "90px" },
        {
          data: 'email',
          width: "120px",
          render: function (data, type, row) {
            if (!data) {
              return `<span class="text-danger" title="Chưa có email"><i class="fas fa-exclamation-circle" ></i> Chưa có email</span>`;
            }

            if (row.xatThucEmail == false) {
              return `<span class="text-warning" title="Chưa xát thực email người dùng"><i class="fas fa-exclamation-triangle" ></i> ${data}</span>`;
            }

            // Đã xác thực
            return `<span class="text-success" title="Đã xát thực email người dùng"><i class="fas fa-check-circle" ></i> ${data}</span>`;
          }
        },
        { data: 'soDienThoai', width: "90px" },
        { data: 'donViCongTac', width: "200px" },
        {
          data: 'dangSD',
          width: "100px",
          render: function (data, type, row) {
            if (data === false) {
              return `<span class="badge bg-red-lt text-red"><i class="fas fa-circle-xmark me-1"></i> Tài khoản bị khóa</span>`;
            }
            return `<span class="badge bg-green-lt text-green"><i class="fas fa-circle-check me-1"></i>Đang hoạt động</span>`;
          }
        },
        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "90px",
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.userID}')">
                                            <i class="fa fa-pencil text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.userID}')">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    //setTimeout(() => {
    //  table.columns.adjust().draw(false);
    //}, 300);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}


$('#userForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    UserID: document.getElementById("UserID").value,
    TenDangNhap: document.getElementById("TenDangNhap").value,
    MatMa: document.getElementById("MatMa").value,
    NhapLaiMatMa: document.getElementById("NhapLaiMatMa").value,
    Avatar: '',
    HoVaTen: document.getElementById("HoVaTen").value,
    NgaySinh: document.getElementById("NgaySinh").value,
    GioiTinh: document.getElementById("GioiTinh").value,
    SoDienThoai: document.getElementById("SoDienThoai").value,
    Email: document.getElementById("Email").value,
    DiaChi: document.getElementById("DiaChi").value,
    DonViCongTac: document.getElementById("DonViCongTac").value,
    DangSD: !document.getElementById("KhoaTaiKhoan").checked,
    LoaiUsers: document.getElementById("LoaiUsers").value,
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/NguoiDung/create', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Thêm mới dữ liệu thành công!");
        LoadLuoi()
      }
      $('#modal-user').modal('hide');
      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Thêm mới dữ liệu thất bại!");
  }

});

$('#userInfoModalForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    UserID: document.getElementById("UserID").value,
    DangSD: document.getElementById("KhoaTaiKhoan").checked,
  };

  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/NguoiDung/update', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Cập nhật dữ liệu thành công!");
        LoadLuoi()
      }
      $('#userInfoModal').modal('hide');
      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Cập nhật dữ liệu thất bại!");
  }
});
async function editRow(id) {
  ThaoTac = 'Sua'
  ResetPasswordVisibility()
  $('#userInfoModal').modal("show")
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/NguoiDung/getbyid/${id}`, {});
  if (response.succeeded) {
    var originalData = response.result;


    document.getElementById("loaiNguoiDung").textContent = originalData[0].loaiUsers || "Chưa có";
    document.getElementById("tenDangNhap").textContent = originalData[0].tenDangNhap || "Chưa có";
    document.getElementById("email").textContent = originalData[0].email || "Chưa có";
    document.getElementById("soDienThoai").textContent = originalData[0].soDienThoai || "Chưa có";
    document.getElementById("donViCongTac").textContent = originalData[0].donViCongTac || "Chưa có";
    document.getElementById("matKhau").textContent = "********"; // Luôn hiển thị ẩn mật khẩu


 
    $('#UserID').val(originalData[0].userID);
    $('#KhoaTaiKhoan').value(originalData[0].dangSD);

  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }

}
async function deleteRow(id) {
  // Icon SVG Tabler cảnh báo (triangle-exclamation)
  const tablerWarningSVG = `
    <svg class="tabler-icon-warning" xmlns="http://www.w3.org/2000/svg" 
         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
         stroke-linejoin="round" stroke-linecap="round" style="width:72px; height:72px; margin: 0 auto 15px auto; stroke:#f59e0b;">
      <path d="M12 9v4"></path>
      <path d="M12 17h.01"></path>
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    </svg>
  `;

  const confirmed = await Swal.fire({
    title: 'Bạn có chắc muốn xóa?',
    text: "Hành động này sẽ không thể hoàn tác!",
    icon: 'warning', // vẫn phải có icon để vùng icon xuất hiện
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    reverseButtons: true,
    didOpen: () => {
      // Thay thế icon mặc định bằng SVG Tabler
      const iconElem = document.querySelector('.swal2-icon.swal2-warning');
      if (iconElem) {
        iconElem.innerHTML = tablerWarningSVG;
        iconElem.style.background = 'none'; // bỏ nền mặc định
        iconElem.style.border = 'none'; // bỏ viền mặc định
      }
    }
  });

  if (confirmed.isConfirmed) {
    const data = {
      UserID: id
    };

    try {
      const response = await NTS.getAjaxAPIAsync(
        'POST',
        window.location.origin + '/api/NguoiDung/delete',
        data
      );
      if (response.succeeded == false && response.errors.length > 0) {
        NTS.canhbao(response.errors[0]);
      } else {
        if (response.succeeded == true) {
          await Swal.fire('Đã xóa!', 'Bản ghi đã được xóa thành công.', 'success');
          LoadLuoi();
        }
      }
    } catch (error) {
      Swal.fire('Lỗi!', 'Không thể xóa bản ghi.', 'error');
    }
  }
}

function targetonclick() {
ThaoTac = 'Them'
$('#NgaySinh').val('');
$('#TenDangNhap').value('');
  $('#MatMa').val('');
  $('#NhapLaiMatMa').val('');
  $('#HoVaTen').val('');
  $('#SoDienThoai').val('');
  $('#Email').val('');
  $('#DiaChi').val('');
  $('#DonViCongTac').val('');
  $('#LoaiUsers').val();
  $('#UserID').val('');
var tomSelectInstance = document.getElementById('GioiTinh').tomselect;
  tomSelectInstance.setValue('');
var tomSelectInstance = document.getElementById('LoaiUsers').tomselect;
tomSelectInstance.setValue('');
  ResetPasswordVisibility()
}
$('#KhoaTaiKhoan').on('change', function () {
  const isLocked = $(this).is(':checked');
  $('#modal-statusLabel').text(isLocked ? "Tài khoản đang bị khóa" : "Tài khoản đang hoạt động");
}); 

document.getElementById('KhoaTaiKhoan').addEventListener('change', function () {
  const statusLabel = document.getElementById('modal-statusLabel');
  if (this.checked) {
    statusLabel.textContent = 'Tài khoản đang hoạt động';
    statusLabel.style.color = '#0054a6'; // Xanh dương khi kích hoạt
  } else {
    statusLabel.textContent = 'Tài khoản bị khóa';
    statusLabel.style.color = '#d63939'; // Đỏ khi khóa
  }
});