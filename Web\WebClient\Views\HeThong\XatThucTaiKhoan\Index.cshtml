﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}
<style>
    body {
        background: linear-gradient(135deg, #ffffff 0%, #2baaff 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }

    .page.page-center {
        min-height: 90vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .password-rules {
        font-size: 13px;
        color: #4caf50;
        margin-top: 10px;
    }

        .password-rules li {
            margin-bottom: 4px;
        }
</style>
<div class="page page-center">
    <div class="container-tight py-4">
        <form id="registerForm" class="card card-md" autocomplete="off">
            <div class="card-body">
                <h2 style=" font-size: 18px;
                                    font-weight: bold;
                                    font-style: normal;
                                    font-stretch: normal;
                                    line-height: 1.21;
                                    letter-spacing: normal;
                                    text-align: center;
                                    color: #4d5156; ">
                    <PERSON><PERSON><PERSON> thực email
                </h2>
                <p class="text-muted mb-4 text-center">
                    <PERSON>hậ<PERSON> địa chỉ email của bạn và chúng tôi sẽ gửi liên kết đến tài khoản của bạn.
                </p>
                <div class="mb-3">
                    <input type="email" class="form-control" name="email" id="Email" placeholder="Nhập Email" required>
                </div>
                <ul class="password-rules list-unstyled">
                </ul>
                <div class="form-footer">
                    <button type="submit" class="btn btn-primary w-100">
                        Gửi liên kết tài khoản
                    </button>
                </div>
                <div class="text-center text-muted mt-3">
                    <a href="/hethong/dangnhap">Quay lại đăng nhập</a>
                </div>
            </div>
        </form>
    </div>
</div>
<script src="~/scripts/XatThucTaiKhoan.js"></script>