﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.DonVi.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonVi.Commands;
public record DeleteDonViCommand(
    string DonViID
    ) : IRequest<Result<DonViDto>?>;

public class DeleteDonViCommandValidator : AbstractValidator<DeleteDonViCommand>
{
    private readonly IApplicationDbContext _context;
    public DeleteDonViCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.DonViID)
            .NotEmpty().WithMessage("DonViID không được để trống.");
    }
}

public class DeleteDonViCommandHandler : IRequestHandler<DeleteDonViCommand, Result<DonViDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteDonViCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<DonViDto>?> Handle(DeleteDonViCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@DonViID", DungChung.NormalizationGuid(request.DonViID))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DonVi_Delete",
                   MapFromReader,
                   true,
                   parameters);
            return Result<DonViDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private DonViDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViDto
        {
            DonViID = reader.GetGuid(reader.GetOrdinal("DonViID")),
        };
    }
}

