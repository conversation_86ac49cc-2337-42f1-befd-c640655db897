﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.NguoiDung.Queries;
public record GetAllNguoiDungCommand : IRequest<Result<List<NguoiDungDto>>?>;

public class GetAllNguoiDungCommandValidator : AbstractValidator<GetAllNguoiDungCommand>
{
    public GetAllNguoiDungCommandValidator()
    {
    }
}

public class GetAllNguoiDungCommandHandler : IRequestHandler<GetAllNguoiDungCommand, Result<List<NguoiDungDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<NguoiDungDto>>?> Handle(GetAllNguoiDungCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_NguoiDung_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<NguoiDungDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new NguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
            TenDangNhap = reader["TenDangNhap"] as string,
            MatMa = reader["MatMa"] as string,
            NhapLaiMatMa = reader["NhapLaiMatMa"] as string,
            MaXacNhan = reader["MaXacNhan"] as string,
            UserGroupCode = reader["UserGroupCode"] as string,
            UserGroupID = reader["UserGroupID"] == DBNull.Value ? null : reader.GetGuid(reader.GetOrdinal("UserGroupID")),
            DonViID = reader["DonViID"] == DBNull.Value ? null : reader.GetGuid(reader.GetOrdinal("DonViID")),
            NgayDangNhap = reader["NgayDangNhap"] == DBNull.Value ? null : reader.GetDateTime(reader.GetOrdinal("NgayDangNhap")),
            NgayThaoTac = reader["NgayThaoTac"] == DBNull.Value ? null : reader.GetDateTime(reader.GetOrdinal("NgayThaoTac")),
            Avatar = reader["Avatar"] as string,
            HoVaTen = reader["HoVaTen"] as string,
            NgaySinh = reader["NgaySinh"] == DBNull.Value ? null : reader.GetDateTime(reader.GetOrdinal("NgaySinh")),
            GioiTinh = reader["GioiTinh"] as string,
            SoDienThoai = reader["SoDienThoai"] as string,
            Email = reader["Email"] as string,
            DiaChi = reader["DiaChi"] as string,
            DonViCongTac = reader["DonViCongTac"] as string,
            DangSD = reader["DangSD"] == DBNull.Value ? null : reader.GetBoolean(reader.GetOrdinal("DangSD")),
            LoaiUsers = reader["LoaiUsers"] as string,
            XatThucEmail = reader["XatThucEmail"] == DBNull.Value ? null : reader.GetBoolean(reader.GetOrdinal("XatThucEmail")),
        };

    }
}

