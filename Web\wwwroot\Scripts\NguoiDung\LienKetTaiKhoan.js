﻿$(document).ready(async function () {
  LoadDuLieuKichHoatKey()
});

async function LoadDuLieuKichHoatKey() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/LienKetTaiKhoan/getbyid/${localStorage.getItem("userID")}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    if (originalData.length > 0) {
      for (var i = 0; i < originalData.length; i++) {
        var item = originalData[i];
        var html = `  <div class="col-md-6 col-lg-4 ">
            <div class="card p-3">
                <div class="d-flex align-items-center mb-3">
                        <img src="/Images/9391705.png"
                         alt="Logo" class="rounded me-3" width="48" height="48">
                    <div>
                            <h4 class="m-0">${item.tenPhanMem}</h4>
                            <small class="text-muted">${item.moTa}</small>
                    </div>
                </div>
                <div class="d-flex justify-content-between">
                        <a href="https://nhattamsoft.vn/san-pham.html#" target="_blank" class="btn btn-outline-primary w-100 me-2">Chi tiết sản phẩm</a>
                        <a href="${item.link}" target="_blank"  class="btn btn-primary w-auto">Truy cập</a>
                </div>

            </div>
        </div> `;

        $('#DSSanPhamDaKichHoat').append(html);
      }
    } else {
      $('#noDataMsg').show()
    }
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}