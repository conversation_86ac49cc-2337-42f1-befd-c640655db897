﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.Commands;

namespace NHATTAMID2025.Web.Endpoints.HeThong;
public class QuyenMatKhauController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
           .MapPost(QuenMatKhau, "");
    }
    public async Task<Result<object>?> QuenMatKhau(ISender sender, [FromBody] CreateQuenMatKhauCommand command)
    {
        return await sender.Send(command);
    }
}
