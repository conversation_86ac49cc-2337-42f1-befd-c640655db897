﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}


    <link rel="stylesheet" href="https://cdn.datatables.net/rowgroup/1.3.0/css/rowGroup.bootstrap5.min.css"> <!-- Thêm CSS cho rowGroup -->
    <!-- Font Awesome cho icon -->
    <style>

    </style>
    <div class="card">
        <div class="card-header">
            <h3 class="card-title mb-0">QUẢN LÝ CẤP KEY</h3>
            <div class="card-actions">
                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#modal-phanmem" onclick="targetonclick()">
                    Thêm Key Mới
                </button>
            </div>
        </div>
        <div class="card-body">
            <table id="phanMemTable" class="table table-hover">
                <thead>
                    <tr>
                        <th><PERSON><PERSON><PERSON> người dùng</th>
                        <th>Tên đăng nhập</th>
                        <th>Email</th>
                        <th>Số điện thoại</th>
                        <th>Đơn vị công tác</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody id="table-body"></tbody>
            </table>
        </div>
    </div>

    <!-- Thư viện cần thiết -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>
    <script src="https://cdn.datatables.net/rowgroup/1.3.0/js/dataTables.rowGroup.min.js"></script> <!-- Thêm JS cho rowGroup -->
    <script>
        // Giả lập dữ liệu
        const NTS = {
            getAjaxAPIAsync: async function(method, url, data) {
                return {
                    succeeded: true,
                    result: [
                        { userID: "1", loaiUsers: "USER", tenDangNhap: "nguoi1", email: "<EMAIL>", xatThucEmail: true, soDienThoai: "0901234567", donViCongTac: "Bộ phận A" },
                        { userID: "2", loaiUsers: "GUEST", tenDangNhap: "khach1", email: "<EMAIL>", xatThucEmail: false, soDienThoai: "0912345678", donViCongTac: "Đối tác X" },
                        { userID: "3", loaiUsers: "ADMIN", tenDangNhap: "admin1", email: null, xatThucEmail: false, soDienThoai: "0923456789", donViCongTac: "Phòng IT" },
                        { userID: "4", loaiUsers: "USER", tenDangNhap: "nguoi2", email: "<EMAIL>", xatThucEmail: true, soDienThoai: "0934567890", donViCongTac: "Bộ phận B" },
                        { userID: "5", loaiUsers: "GUEST", tenDangNhap: "khach2", email: "<EMAIL>", xatThucEmail: false, soDienThoai: "0945678901", donViCongTac: "Đối tác Y" }
                    ]
                };
            },
            canhbao: function(message) {
                alert(message);
            }
        };

        async function LoadLuoi() {
            var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/TrienKhaiPhanMem/getall', null);
            if (response.succeeded) {
                var originalData = response.result;
                $('#phanMemTable').DataTable().destroy();
                $('#phanMemTable').DataTable({
                    data: originalData,
                    scrollX: false,
                    scrollCollapse: false,
                    autoWidth: false,
                    responsive: false,
                    fixedColumns: {
                        rightColumns: 1,
                        leftColumns: 0
                    },
                    rowGroup: {
                        dataSrc: 'loaiUsers',
                        startRender: function (rows, group) {
                            const loaiUsersMap = {
                                USER: { text: "Người dùng nội bộ", icon: "fas fa-user-cog text-green" },
                                GUEST: { text: "Người dùng bên ngoài", icon: "fas fa-user text-yellow" },
                                ADMIN: { text: "Quản trị viên", icon: "fas fa-shield-alt text-red" }
                            };

                            const info = loaiUsersMap[group] || { text: group, icon: "" };
                            return `<strong><i class="${info.icon}"></i> ${info.text} (${rows.count()} người dùng)</strong>`;
                        }
                    },
                    columns: [
                        {
                            data: 'loaiUsers',
                            width: "90px",
                            render: function (data, type, row) {
                                const loaiUsersMap = {
                                    USER: { text: "Người dùng nội bộ", icon: "fas fa-user-cog text-green" },
                                    GUEST: { text: "Người dùng bên ngoài", icon: "fas fa-user text-yellow" },
                                    ADMIN: { text: "Quản trị viên", icon: "fas fa-shield-alt text-red" }
                                };

                                const info = loaiUsersMap[data] || { text: data, icon: "" };
                                return `<span class="badge bg-green-lt text-green"><i class="${info.icon}"></i> ${info.text}</span>`;
                            }
                        },
                        { data: 'tenDangNhap', width: "90px" },
                        {
                            data: 'email',
                            width: "120px",
                            render: function (data, type, row) {
                                if (!data) {
                                    return `<span class="text-danger" title="Chưa nhập email"><i class="fas fa-exclamation-circle"></i> Chưa nhập email</span>`;
                                }

                                if (row.xatThucEmail === false) {
                                    return `<span class="text-warning" title="Chưa xác thực email người dùng"><i class="fas fa-exclamation-triangle"></i> ${data}</span>`;
                                }

                                return `<span class="text-success" title="Đã xác thực email người dùng"><i class="fas fa-check-circle"></i> ${data}</span>`;
                            }
                        },
                        { data: 'soDienThoai', width: "90px" },
                        { data: 'donViCongTac', width: "200px" },
                        {
                            data: null,
                            className: "text-center",
                            orderable: false,
                            width: "90px",
                            render: function (data, type, row) {
                                return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.userID}')">
                                            <i class="fa fa-pencil text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.userID}')">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
                            }
                        }
                    ],
                    pageLength: 15,
                    lengthMenu: [15, 50, 100, 200],
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
                        search: ""
                    }
                });
            } else {
                NTS.canhbao('Lỗi khi lấy dữ liệu: ' + response.errors);
            }
        }

        function editRow(id) {
            alert(`Sửa key với ID: ${id}`);
        }

        function deleteRow(id) {
            if (confirm(`Bạn có chắc chắn muốn xóa key với ID: ${id} không?`)) {
                alert(`Đã xóa key với ID: ${id}`);
            }
        }

        $(document).ready(function() {
            LoadLuoi();
        });

        function targetonclick() {
            console.log("Nút Thêm Key Mới được nhấn");
        }
    </script>
