﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Tinh.Commands;
public record CreateTinhCommand(
    string MaTinh,
    string TenTinh,
    string MoTa,
    string DangSD
    ) : IRequest<Result<TinhDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateTinhCommand>
{
    private readonly IApplicationDbContext _context;
    public CreateCommandValidator(IApplicationDbContext context)
    {
        _context = context;


        RuleFor(cmd => cmd.TenTinh)
            .NotEmpty().WithMessage("Tên tỉnh không được để trống.");
        RuleFor(cmd => cmd.MaTinh)
          .NotEmpty().WithMessage("Mã tỉnh không được để trống.")
          .MustAsync(BeUniquemaDonVi)
          .WithMessage(cmd => $"Mã tỉnh '{cmd.MaTinh}' đã được sử dụng.");
    }


    private async Task<bool> BeUniquemaDonVi(string MaTinh, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(MaTinh))
            return true;
        return !await _context.Tinh
            .AnyAsync(u => u.MaTinh == MaTinh, cancellationToken);
    }
}

public class CreateTinhCommandHandler : IRequestHandler<CreateTinhCommand, Result<TinhDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateTinhCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<TinhDto>?> Handle(CreateTinhCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@MaTinh", request.MaTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@TenTinh", request.TenTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@DangSD", DungChung.NormalizationBoolean(request.DangSD))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Tinh_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<TinhDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TinhDto
        {
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
        };
    }
}
