﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;
public class NguoiDungDto
{
    public Guid UserID { get; set; }
    public string? TenDangNhap { get; set; }
    public string? MatMa { get; set; }
    public string? NhapLaiMatMa { get; set; }
    public string? MaXacNhan { get; set; }
    public string? UserGroupCode { get; set; }
    public Guid? UserGroupID { get; set; }
    public Guid? DonViID { get; set; }
    public DateTime? NgayDangNhap { get; set; }
    public DateTime? NgayThaoTac { get; set; }
    public string? Avatar { get; set; }
    public string? HoVaTen { get; set; }
    public DateTime? NgaySinh { get; set; }
    public string? GioiTinh { get; set; }
    public string? SoDienThoai { get; set; }
    public string? Email { get; set; }
    public string? Dia<PERSON>hi { get; set; }
    public string? DonViCongTac { get; set; }
    public bool? DangSD { get; set; }
    public string? LoaiUsers { get; set; }
    public bool? XatThucEmail { get; set; }

}
