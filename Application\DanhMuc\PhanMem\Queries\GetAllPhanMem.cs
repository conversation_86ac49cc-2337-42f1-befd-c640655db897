﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.PhanMem.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.PhanMem.Queries;
public record GetAllPhanMemCommand : IRequest<Result<List<PhanMemDto>>?>;

public class GetAllPhanMemCommandValidator : AbstractValidator<GetAllPhanMemCommand>
{
    public GetAllPhanMemCommandValidator()
    {
    }
}

public class GetAllPhanMemCommandHandler : IRequestHandler<GetAllPhanMemCommand, Result<List<PhanMemDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllPhanMemCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<PhanMemDto>>?> Handle(GetAllPhanMemCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_PhanMem_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<PhanMemDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private PhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new PhanMemDto
        {
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            MaPhanMem = reader["MaPhanMem"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
