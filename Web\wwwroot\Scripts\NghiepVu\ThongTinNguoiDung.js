﻿var Load;

$(document).ready(async function () {
  LoadLuoiDonVi()
});
document.addEventListener('DOMContentLoaded', () => {
  const editableElements = document.querySelectorAll(".editable");

  editableElements.forEach((el) => {
    attachEditableBehavior(el);
  });

  function attachEditableBehavior(el) {
    el.addEventListener("click", function () {
      // Ngăn chồng input
      if (this.querySelector('input, textarea')) return;

      const originalText = this.textContent.trim();
      const isMultiline = this.dataset.multiline === "true";
      const isDateField = this.id === "NgaySinh";

      let input;

      if (isDateField) {
        input = document.createElement("input");
        input.type = "date";

        // Chuyển từ "dd/mm/yyyy" sang "yyyy-mm-dd"
        const parts = originalText.split('/');
        if (parts.length === 3) {
          const yyyyMmDd = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
          input.value = yyyyMmDd;
        }
      } else {
        input = isMultiline
          ? document.createElement("textarea")
          : document.createElement("input");
        input.value = originalText;
      }

      input.className = this.className;
      input.id = this.id;
      input.style.width = "100%";
      input.style.fontSize = "inherit";
      input.style.fontWeight = "inherit";

      this.replaceWith(input);
      input.focus();

      input.addEventListener("blur", function () {
        const newDiv = document.createElement("div");
        newDiv.className = input.className;
        newDiv.id = input.id;

        if (isDateField && input.value) {
          const [yyyy, mm, dd] = input.value.split('-');
          newDiv.textContent = `${dd}/${mm}/${yyyy}`;
        } else {
          newDiv.textContent = input.value || "Chưa có thông tin";
        }

        attachEditableBehavior(newDiv);
        input.replaceWith(newDiv);
        triggerSaveButton(newDiv);
      });
    });
  }

  function triggerSaveButton(element) {
    const parentCard = element.closest(".card");

    if (parentCard?.classList.contains("cardthongtincoban")) {
      const saveCoban = document.getElementById("save-coban");
      if (saveCoban) saveCoban.style.display = "inline-block";
    }

    if (parentCard?.classList.contains("cardthongtinlienhe")) {
      const saveLienhe = document.getElementById("save-lienhe");
      if (saveLienhe) saveLienhe.style.display = "inline-block";
    }
  }

  // ====== Nút Lưu ====== //
  document.getElementById("save-coban").addEventListener("click", async function (e) {
    e.preventDefault();

    const data = {
      UserID: $('#UserID').value(),
      Avatar: $('#imgsrcAvata').text(),
      HoVaTen: $('#HoVaTen').text(),
      GioiTinh: $('#GioiTinh').text(),
      NgaySinh: $('#NgaySinh').text(),
      SoDienThoai: $('#SoDienThoai').text(),
      Email: $('#Email').text(),
      DiaChi: $('#DiaChi').text(),
      DonViCongTac: $('#DonViCongTac').text(),
      CarNoiDung:'CoBan'
    };
    try {
      const res = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/ThongTinNguoiDung/update', data);
      if (res.succeeded == false && res.errors.length > 0) {
        NTS.canhbao(res.errors[0])
      } else {
        if (res.succeeded == true) {
          NTS.thanhcong("Cập nhật dữ liệu thành công!");
        }
      }
    } catch (error) {
      NTS.canhbao("Cập nhật dữ liệu thất bại!");
    }

    this.style.display = "none";
  });

  document.getElementById("save-lienhe").addEventListener("click", async function (e) {
    e.preventDefault();
  
    const data = {
      UserID: $('#UserID').value(),
      Avatar: $('#imgsrcAvata').text(),
      HoVaTen: $('#HoVaTen').text(),
      GioiTinh: $('#GioiTinh').text(),
      NgaySinh: $('#NgaySinh').text(),
      SoDienThoai: $('#SoDienThoai').text(),
      Email: $('#Email').text(),
      DiaChi: $('#DiaChi').text(),
      DonViCongTac: $('#DonViCongTac').text(),
      CarNoiDung: 'LienHe',
      DonViCongTacID: $('#hfdonViID').value(),
    };
    try {
      const res = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/ThongTinNguoiDung/update', data);
      if (res.succeeded == false && res.errors.length > 0) {
        NTS.canhbao(res.errors[0])
      } else {
        if (res.succeeded == true) {
          NTS.thanhcong("Cập nhật dữ liệu thành công!");
        }
      }
    } catch (error) {
      NTS.canhbao("Cập nhật dữ liệu thất bại!");
    }

    this.style.display = "none";
  });


  // ====== Avatar (Giữ nguyên) ====== //
  const avatarDataEl = document.getElementById("imgsrcAvata");

  if (avatarDataEl) {
  
    const observer = new MutationObserver(() => {
      debugger
      if (Load == true) {

        Load = false
        return false;
      }
      const parentCard = avatarDataEl.closest(".card");

      if (parentCard?.classList.contains("cardthongtincoban")) {
        const saveCoban = document.getElementById("save-coban");
        if (saveCoban) saveCoban.style.display = "inline-block";
      }

      if (parentCard?.classList.contains("cardthongtinlienhe")) {
        const saveLienhe = document.getElementById("save-lienhe");
        if (saveLienhe) saveLienhe.style.display = "inline-block";
      }
    });

    observer.observe(avatarDataEl, {
      childList: true,
      characterData: true,
      subtree: true
    });
  }

  const avatarInput = document.getElementById('avatar-input');
  const avatarPreview = document.getElementById('avatar-preview');
  const avatarUser = document.getElementById('avatarUser');

  function getBase64FromFile(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject("Lỗi khi đọc ảnh: " + error);
      reader.readAsDataURL(file);
    });
  }

  if (avatarInput) {
    avatarInput.addEventListener('change', async (event) => {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const base64 = await getBase64FromFile(file);
        if (avatarPreview) avatarPreview.src = base64;
        if (avatarUser) avatarUser.src = base64;

        const pureBase64 = base64.split(',')[1];
        $('#imgsrcAvata').text(base64)
      } catch (err) {
        console.error(err);
      }

      avatarInput.value = '';
    });
  }
});

$(document).ready(async function () {
  LoadDuLieu()
});

async function LoadDuLieu() {
  debugger
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/thongtinnguoidung/getbyid/${localStorage.getItem("userID") }`, {});
  if (response.succeeded) {
    var originalData = response.result;
    $('#HoVaTen').text(originalData[0].hoVaTen || "Chưa có thông tin")
    $('#NgaySinh').text(originalData[0].ngaySinh || "Chưa có thông tin")
    $('#GioiTinh').text(originalData[0].gioiTinh || "Chưa có thông tin")
    $('#SoDienThoai').text(originalData[0].soDienThoai || "Chưa có thông tin")
    $('#Email').text(originalData[0].email || "Chưa có thông tin")
    $('#DiaChi').text(originalData[0].diaChi || "Chưa có thông tin")
    $('#DonViCongTac').text(originalData[0].donViCongTac || "Chưa có thông tin")
    $('#UserID').value(originalData[0].userID)
    $('#hfdonViID').val(originalData[0].donViCongTacID);
    $('#hftendonVi').val(originalData[0].donViCongTac);
    document.getElementById('avatar-preview').src = originalData[0].avatar;
    $('#imgsrcAvata').text(originalData[0].avatar)
    $("#save-coban").hide();
    $("#save-lienhe").hide();
    Load = true
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}


$('#DonViCongTacmd').on('click', function () {
  $('#modalDonVi').modal('show')
  setSelectedDonViByID($('#hfdonViID').val())
  const saveLienhe = document.getElementById("save-lienhe");
  if (saveLienhe) saveLienhe.style.display = "inline-block";
});

async function LoadLuoiDonVi() {

  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/donvi/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#DonViTable').DataTable().destroy();
    $('#DonViTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },

      columns: [
        {
          data: null,
          orderable: false,
          className: 'text-center',
          width: '40px',
          render: function (data, type, row, meta) {
            return `<input type="checkbox" class="donvi-checkbox" value="${row.donViID}" />`;
          }
        },
        { data: 'tenDonVi', width: "200px" },
        { data: 'tenTinh', width: "90px" },
        { data: 'donViID', visible: false },
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}


$('#DonViTable tbody').on('click', 'tr', function () {
  const checkbox = $(this).find('.donvi-checkbox');
  const isChecked = checkbox.prop('checked');

  // Nếu đã được chọn → bỏ chọn
  if (isChecked) {
    checkbox.prop('checked', false);
    $(this).removeClass('selected');
    $('#hfdonViID').val('');
    $('#hftendonVi').val('');
    $('#DonViCongTac').text('Chưa có thông tin');
  }
  // Nếu chưa chọn → chọn mới
  else {
    // Bỏ chọn tất cả
    $('.donvi-checkbox').prop('checked', false);
    $('#DonViTable tbody tr').removeClass('selected');

    // Chọn dòng hiện tại
    checkbox.prop('checked', true);
    $(this).addClass('selected');

    // Gán giá trị
    const donViID = checkbox.val();
    const tenDonVi = $(this).find('td:eq(1)').text();

    $('#hfdonViID').val(donViID);
    $('#hftendonVi').val(tenDonVi);
    $('#DonViCongTac').text(tenDonVi);
  }
});


function setSelectedDonViByID(donViID) {
  // Reset tất cả checkbox và dòng
  $('.donvi-checkbox').prop('checked', false);
  $('#DonViTable tbody tr').removeClass('selected');

  // Tìm dòng có checkbox với giá trị tương ứng
  $('#DonViTable tbody tr').each(function () {
    const checkbox = $(this).find('.donvi-checkbox');
    if (checkbox.val() === donViID) {
      checkbox.prop('checked', true);
      $(this).addClass('selected');

      // Gán vào hidden
      const tenDonVi = $(this).find('td:eq(1)').text();
      $('#hfdonViID').val(donViID);
      $('#hftendonVi').val(tenDonVi);
    }
  });
}