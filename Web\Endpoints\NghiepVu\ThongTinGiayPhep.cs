﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ThongTinGiayPhep.DTOs;
using NHATTAMID2025.Application.NghiepVu.ThongTinGiayPhep.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class ThongTinGiayPhep : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetThongTinGiayPhepById, "/getbyid/{Id}")
        ;
    }


    public async Task<Result<List<ThongTinGiayPhepDto>>?> GetThongTinGiayPhepById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetThongTinGiayPhepByIDQuery(id));
}
