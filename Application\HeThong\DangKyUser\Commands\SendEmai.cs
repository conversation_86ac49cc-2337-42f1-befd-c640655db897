﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using NHATTAMID2025.Domain.Entities;
using WEB_DLL;

namespace NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
internal class SendEmai
{
}
public record CreateSendEmaiCommand(
    string TenDangNhap,
    string MatMa,
    string NhapLaiMatMa,
    string HoVaTen,
    string GioiTinh,
    string SoDienThoai,
    string Email,
    string DiaChi,
    string DonViCongTac
    ) : IRequest<Result<object>?>;
public class CreateSendEmaiCommandValidator : AbstractValidator<CreateSendEmaiCommand>
{
    private readonly IApplicationDbContext _context;
    public CreateSendEmaiCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(cmd => cmd.TenDangNhap)
            .NotEmpty().WithMessage("Tên đăng nhập không được để trống.")
            .MustAsync(BeUniqueTenDangNhap)
            .WithMessage(cmd => $"Tên đăng nhập '{cmd.TenDangNhap}' đã được sử dụng.");
        RuleFor(cmd => cmd.Email)
            .NotEmpty().WithMessage("Email không được để trống.")
            .MustAsync(BeUniquEmail)
            .WithMessage(cmd => $"Email '{cmd.Email}' đã được sử dụng.");
        RuleFor(cmd => cmd.SoDienThoai)
           .NotEmpty().WithMessage("Số điện thoại không được để trống.")
           .MustAsync(BeUniquSoDienThoai)
           .WithMessage(cmd => $"Số điện thoại '{cmd.SoDienThoai}' đã được sử dụng.");
        RuleFor(cmd => cmd.MatMa)
           .NotEmpty().WithMessage("Mật khẩu không được để trống.")
           .MinimumLength(8).WithMessage("Mật khẩu phải có ít nhất 8 ký tự.")
           .Matches("[A-Z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái in hoa.")
           .Matches("[a-z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái thường.")
           .Matches("[0-9]").WithMessage("Mật khẩu phải có ít nhất một chữ số.")
           .Matches("[^a-zA-Z0-9]").WithMessage("Mật khẩu phải có ít nhất một ký tự đặc biệt.");
        RuleFor(cmd => cmd.NhapLaiMatMa)
           .NotEmpty().WithMessage("Vui lòng nhập lại mật khẩu.")
           .Equal(cmd => cmd.MatMa).WithMessage("Mật khẩu nhập lại không trùng khớp.");
    }

    private async Task<bool> BeUniqueTenDangNhap(string tenDangNhap, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(tenDangNhap))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.TenDangNhap == tenDangNhap, cancellationToken);
    }

    private async Task<bool> BeUniquEmail(string Email, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(Email))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.Email == Email, cancellationToken);
    }

    private async Task<bool> BeUniquSoDienThoai(string SoDienThoai, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(SoDienThoai))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.SoDienThoai == SoDienThoai, cancellationToken);
    }
}

public class CreateSendEmaiCommandHandler : IRequestHandler<CreateSendEmaiCommand, Result<object>?>
{
    private readonly IApplicationDbContext _context;

    public CreateSendEmaiCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<object>?> Handle(CreateSendEmaiCommand request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Email))
        {
            throw new Exception("Email trong request đang bị null hoặc rỗng!");
        }
        var random = new Random();
        var maXacThuc = random.Next(100000, 1000000).ToString();
        var guiMail = await GuiEmailXacThuc(request.Email, maXacThuc);
        if (!guiMail)
        {
            // Bạn có thể throw exception hoặc return lỗi
            return null;
        }
        var ma = new MaXacThucEntity
            {
                Email = request.Email,
                MaXacThuc = maXacThuc,
                ThoiGianTao = DateTime.UtcNow,
                ThoiHan = DateTime.UtcNow.AddMinutes(10),
                TrangThai = "Pending"
            };
            _context.MaXacThuc.Add(ma);
            await _context.SaveChangesAsync(cancellationToken);
        return Result<object>.Success();
    }
    private async Task<bool> GuiEmailXacThuc(string email, string maXacThuc)
    {
        try
        {
            var fromAddress = new MailAddress("<EMAIL>", "Công ty TNHH Phát triển phần mềm Nhất Tâm");
            var toAddress = new MailAddress(email);
            const string fromPassword = "wcgo otfb revn nnhi";
            string subject = "Xác thực tài khoản NHATTAM";
            string body = $@"
                <!DOCTYPE html>
                <html lang='vi'>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body {{
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            background-color: #f4f6f8;
                            margin: 0;
                            padding: 0;
                        }}
                        .email-container {{
                            max-width: 600px;
                            margin: 40px auto;
                            background-color: #ffffff;
                            padding: 30px;
                            border-radius: 10px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                        }}
                        .header {{
                            text-align: center;
                            padding-bottom: 20px;
                        }}
                        .logo {{
                            width: 120px;
                        }}
                        .title {{
                            font-size: 24px;
                            color: #2c3e50;
                            margin-top: 10px;
                        }}
                        .content {{
                            font-size: 16px;
                            color: #444;
                            line-height: 1.6;
                        }}
                        .code-box {{
                            background-color: #eaf4ff;
                            border: 2px dashed #3498db;
                            font-size: 28px;
                            font-weight: bold;
                            color: #3498db;
                            padding: 15px;
                            text-align: center;
                            margin: 25px 0;
                            border-radius: 8px;
                            letter-spacing: 4px;
                        }}
                        .cta-button {{
                            display: inline-block;
                            background-color: #3498db;
                            color: #fff !important;
                            padding: 12px 24px;
                            border-radius: 6px;
                            text-decoration: none;
                            font-weight: 600;
                            font-size: 16px;
                        }}
                        .footer {{
                            text-align: center;
                            font-size: 13px;
                            color: #888;
                            margin-top: 30px;
                        }}
                    </style>
                </head>
                <body>
                    <div class='email-container'>
                        <div class='header'>
                            <img src='https://i.imgur.com/zIl8Bsl.png' alt='NHATTAMID2025 Logo' class='logo' />
                            <div class='title'>Mã xác thực tài khoản</div>
                        </div>
                        <div class='content'>
                            <p>Chào bạn,</p>
                            <p>Chúng tôi đã nhận được yêu cầu đăng ký tài khoản từ địa chỉ email này.</p>
                            <p>Vui lòng sử dụng mã xác thực bên dưới để tiếp tục:</p>

                            <div class='code-box'>{maXacThuc}</div>

                            <p>Mã xác thực này có hiệu lực trong vòng <strong>10 phút</strong>.</p>
                            <p>Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.</p>

                            <p style='text-align: center; margin-top: 20px;'>
                                <a href='#' class='cta-button'>Xác thực ngay</a>
                            </p>
                        </div>
                        <div class='footer'>
                            &copy; {DateTime.Now.Year} NHATTAMID2025. Mọi quyền được bảo lưu.
                        </div>
                    </div>
                </body>
                </html>
                ";

            var smtp = new SmtpClient
            {
                Host = "smtp.gmail.com", // ví dụ: smtp.gmail.com
                Port = 587,
                EnableSsl = true,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Credentials = new NetworkCredential(fromAddress.Address, fromPassword),
                Timeout = 20000,
            };

            using var message = new MailMessage(fromAddress, toAddress)
            {
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };
            await smtp.SendMailAsync(message);
            return true;
        }
        catch
        {
            return false;
        }
    }

}
