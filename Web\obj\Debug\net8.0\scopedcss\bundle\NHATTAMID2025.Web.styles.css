/* _content/NHATTAMID2025.Web/WebClient/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-bl5uika355] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-bl5uika355] {
  color: #0077cc;
}

.btn-primary[b-bl5uika355] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-bl5uika355], .nav-pills .show > .nav-link[b-bl5uika355] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-bl5uika355] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-bl5uika355] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-bl5uika355] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-bl5uika355] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-bl5uika355] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
/* _content/NHATTAMID2025.Web/WebClient/Views/Shared/_LayoutInfo.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-w6x9hphj0w] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-w6x9hphj0w] {
  color: #0077cc;
}

.btn-primary[b-w6x9hphj0w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-w6x9hphj0w], .nav-pills .show > .nav-link[b-w6x9hphj0w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-w6x9hphj0w] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-w6x9hphj0w] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-w6x9hphj0w] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-w6x9hphj0w] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-w6x9hphj0w] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
