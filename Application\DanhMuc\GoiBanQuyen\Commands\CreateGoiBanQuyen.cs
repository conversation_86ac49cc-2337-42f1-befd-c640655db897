﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;
using WEB_DLL;

namespace NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands;
public record CreateGoiBanQuyenCommand(
    string GoiBanQuyenID,
    string MaGoi,
    string TenGoi,
    string MoTa,
    string ThoiHanNgay,
    string SoThietBiToiDa,
    string LoaiNguoiDungID,
    string GiaTien
    ) : IRequest<Result<GoiBanQuyenDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateGoiBanQuyenCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateGoiBanQuyenCommandHandler : IRequestHandler<CreateGoiBanQuyenCommand, Result<GoiBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateGoiBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<GoiBanQuyenDto>?> Handle(CreateGoiBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@MaGoi", request.MaGoi),
            new Microsoft.Data.SqlClient.SqlParameter("@TenGoi", request.TenGoi),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@ThoiHanNgay", DungChung.NormalizationNumber(request.ThoiHanNgay)),
            new Microsoft.Data.SqlClient.SqlParameter("@SoThietBiToiDa", DungChung.NormalizationNumber(request.SoThietBiToiDa)),
            new Microsoft.Data.SqlClient.SqlParameter("@LoaiNguoiDungID", DungChung.NormalizationGuid(request.LoaiNguoiDungID)),
            new Microsoft.Data.SqlClient.SqlParameter("@GiaTien", DungChung.NormalizationNumber(request.GiaTien))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_GoiBanQuyen_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<GoiBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private GoiBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new GoiBanQuyenDto
        {
            GoiBanQuyenID = reader.GetGuid(reader.GetOrdinal("GoiBanQuyenID")),
        };
    }
}
