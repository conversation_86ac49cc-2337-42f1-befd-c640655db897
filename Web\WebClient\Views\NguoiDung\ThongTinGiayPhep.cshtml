﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}
<style>
    .left-section {
        position: relative;
        transition: transform 0.1s ease, box-shadow 0.1s ease;
        transform-style: preserve-3d;
    }

        .left-section:hover {
            transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
            box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #50c1cc 0%, #ffffff 50%);
        }


    .badge-status-active {
        background: linear-gradient(145deg, #e3f8ee, #c7ebd8);
        color: #2e7d32;
        font-weight: 500;
        border-radius: 1rem;
        padding: 0.35rem 0.85rem;
        font-size: 0.875rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(46, 125, 50, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transition: transform 0.15s ease, box-shadow 0.15s ease;
        text-shadow: none;
    }

        .badge-status-active:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(46, 125, 50, 0.2);
        }


    .badge-status-inactive {
        background: linear-gradient(145deg, #fce8e6, #f5c6c4);
        color: #c62828;
        font-weight: 500;
        border-radius: 1rem;
        padding: 0.35rem 0.85rem;
        font-size: 0.875rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(198, 40, 40, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.6);
        transition: transform 0.15s ease, box-shadow 0.15s ease;
        text-shadow: none;
    }

        .badge-status-inactive:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(198, 40, 40, 0.2);
        }

   

   
    .card-body {
        flex-grow: 1;
        overflow-y: auto;
    }

    #DanhSachKey {
        flex-grow: 1;
    }

    .row .dt-row .col-sm-12 {
        overflow-x: auto;
        height: 300px !important;
    }

    .header-card {
        background-color: #f0f4ff;
        border-radius: 8px;
        border: 1px solid #d0d7e2;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    }

    .project-card {
        background-color: transparent;
        padding: 12px 16px;
    }

        .project-card .fw-bold {
            color: #1c3faa;
            font-size: 1.125rem; /* fs-5 */
        }

    .avatar {
        font-size: 1.5rem;
    }

    .btn-icon {
        background-color: #f1f5f9;
        border-radius: 8px;
        padding: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #206bc4;
        position: relative;
        transition: background 0.2s ease;
        width: 31px;
        height: 31px;
    }

        .btn-icon:hover {
            background-color: #e2e8f0;
            stroke: #1b4f91;
        }

        .btn-icon .icon {
            width: 28px;
            height: 28px;
        }

        .btn-icon .badge {
            font-size: 10px;
            padding: 2px 6px;
        }

    .cardview {
        min-height: 100vh; /* Luôn cao bằng chiều cao màn hình */
        display: flex;
        flex-direction: column;
        border-radius: 0 !important;
    }

  
</style>
<div class="card cardview" style="border-radius: 0 !important;">
    <div class="card-header" style="">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">THÔNG TIN GIẤY PHÉP</h2>
        </div>
        <div class="card-actions d-flex align-items-center gap-2">
           
  
        </div>
    </div>
    <div class="">
        <div class="card-body">
            <div class="row align-items-center">
                <!-- Thông tin bên trái -->
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3" style="margin:16px">
                        <h2 class="ms-1 mb-0">Giấy phép sử dụng phần mềm trên hệ thống NTSOFT</h2>
                    </div>
                    <p class="text-muted" style="margin:16px">
                        Hệ thống cho phép bạn quản lý toàn bộ thông tin giấy phép phần mềm, bao gồm tra cứu trạng thái, thời hạn sử dụng, và thông tin chi tiết của gói bản quyền. Bạn cũng có thể cập nhật thông tin liên quan và đảm bảo việc sử dụng phần mềm luôn đúng quy định, hợp lệ và minh bạch.bản quyền, đảm bảo phần mềm được sử dụng đúng đối tượng, minh bạch và đúng thời hạn.
                    </p>
                </div>

                <!-- Hình ảnh bên phải -->
                <div class="col-md-6 text-center">
                    <img src="/Images/banner1.png" alt="Minh họa hồ sơ" class="profile-image">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body d-flex flex-column">
        <div id="noDataMsg" class="no-data-message " style="display: none;">
            <img src="/Images/nodata.png" alt="Không có dữ liệu" class="nodata-img">
            <p>Không có dữ liệu.</p>
        </div>
        <div class="row flex-grow-1" id="DanhSachKey">
        
        </div>
    </div>
  

    <style>
        .no-data-message {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 2rem;
            margin-top: 4rem;
            border-radius: 0.75rem;
            transition: all 0.3s ease-in-out;
        }

            .no-data-message .nodata-img {
                width: 300px;
                height: auto;
                max-width: 100%;
                margin-bottom: 1rem;
                opacity: 0.8;
            }

            .no-data-message p {
                font-size: 1.1rem;
                font-style: italic;
                margin: 0;
            }
    </style>
   
  
</div>
<div class="modal modal-blur fade" id="userInfoModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="userInfoModalForm">
                <div class="modal-header ">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div id="alertcanhbao">
                  
                    </div>


                    <div class="mb-3">
                        <label class="form-label">Gói dịch vụ</label>
                        <p id="TenGoiBanQuyen" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Phần mềm</label>
                        <p id="TenPhanMem" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ngày cấp</label>
                        <p id="NgayTao" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label"> Ngày hết hạn</label>
                        <p id="NgayHetHan" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số ngày còn lại</label>
                        <p id="SoNgayConLai" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số người dùng kích hoạt</label>
                        <p id="SoLanKichHoat" class="text-muted">********</p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Trạng thái</label>
                        <p id="TrangThai" class="text-muted">********</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Đóng
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<div class="modal  modal-blur fade" id="modalNguoiDungKey" tabindex="-1" aria-labelledby="modalNguoiDungKeyLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable" style="max-width:70%">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalNguoiDungKeyLabel">Người dùng đã kích hoạt key</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Đóng"></button>
            </div>
            <div class="modal-body">
                <div class="list-group list-group-flush header-card" style="border-radius: 4px; margin-bottom:4px;">
                    <div class="list-group-item d-flex align-items-center project-card">
                        <span class="avatar bg-blue-lt text-blue me-3"><i class="fa-solid fa-mobile-alt"></i></span>
                        <div>
                            <div class="fw-bold" id="TenUngDungmd"></div>
                            <small class="text-muted" id="MoTaUngDungmd"></small>
                        </div>
                    </div>
                </div>
                <table id="DSKichHoatKeyTable" class="table table-bordered table-hover custom-table" style="width: 100%">
                    <thead>
                        <tr>
                            <th>Người kích hoạt</th>
                            <th>Đơn vị công tác</th>
                            <th>Email</th>
                            <th>Link kích hoạt</th>
                            <th>Ngày kích hoạt</th>
                            <th>Thiết bị kích hoạt</th>
                            <th>IP kích hoạt</th>
                        </tr>
                    </thead>
                    <tbody id="table-body"></tbody>
                </table>

               
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="hfKeyID" />
@* <div class="modal modal-blur fade" id="DangKyModal" tabindex="-1" aria-labelledby="DangKyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="DangKyModalForm">
                <div class="modal-header">
                    <h5 class="modal-title">
                        Quản lý gói phần mềm <span class="text-primary" id="mdTenPhanMem"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
              
                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Họ và tên:</div>
                        <div class="col-7" id="mdHoTen">Nguyễn Văn A</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Số điện thoại:</div>
                        <div class="col-7" id="mdSDT">0912 345 678</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Email:</div>
                        <div class="col-7" id="mdEmail"><EMAIL></div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nội dung gia hạn hoặc năng cấp gói</label>
                        <textarea class="form-control" rows="3" id="noidung" placeholder="Nhập nội dung gia hạn hoặc yêu cầu thêm, nhu cầu sử dụng..."></textarea>
                    </div>
                    <div class="custom-alert" role="alert">
                        Sau khi gửi yêu cầu, nhân viên của <strong style=" font-weight: 600;color: #0d6efd;">NTSOFT</strong> sẽ liên hệ lại với bạn để hỗ trợ.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="ti ti-device-floppy me-1"></i> Gửi yêu cầu
                    </button>
                </div>
            </form>

        </div>
    </div>
</div> *@
<div class="modal modal-blur fade" id="DangKyModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="DangKyModalForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="alert alert-warning d-flex align-items-start" role="alert">
                        <i class="fas fa-info-circle me-2 mt-1 text-warning" style="font-size: 20px;"></i>
                        <div>
                            <h4 class="alert-title mb-1 text-warning">Thông báo hỗ trợ khách hàng</h4>
                            <div class="text-secondary">
                                Bạn đang xem thông tin yêu cầu <strong>đăng ký</strong> hoặc <strong>nâng cấp gói phần mềm</strong> từ khách hàng.<br>
                                Hãy kiểm tra thông tin và <strong>liên hệ khách hàng</strong> để xác nhận và hỗ trợ theo đúng quy trình.
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Họ và tên</label>
                        <p id="mdHoTen" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <p id="mdEmail" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện thoại</label>
                        <p id="mdSDT" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đơn vị công tác</label>
                        <p id="mddonViCongTac" class="text-muted"></p>
                    </div>

                    <div class="mb-3 highlight-section">
                        <div class="">

                            <div class="mb-3" style="margin-top:4px">
                                <label class="form-label">Bạn đang quan tâm đến giải pháp nào hoặc có nhu cầu cụ thể gì? </label>
                                <p id="mdSDT" class="text-muted">Vui lòng chia sẻ với chúng tôi!</p>
                                <textarea class="form-control" rows="3" id="noidung" placeholder="Nhập nội dung cần hỗ trợ"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Gửi yêu cầu
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<div class="modal modal-blur fade" id="modalDanhSachKey" tabindex="-1" aria-labelledby="modalDanhSachKeyLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="min-width:80% !important">
        <div class="modal-content">
            <form id="modalDonViForm">
                <div class="modal-header">
                    <h5 class="modal-title">Chia sẻ giấy phép sử dụng phần mềm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="card-body">

                        <table id="DanhSachKeyTable" class="table table-bordered table-hover custom-table">
                            <thead>
                                <tr>
                                    <th>Trạng thái</th>
                                    <th>Link kích hoạt</th>
                                    <th>Họ và tên</th>
                                    <th>Email</th>
                                    <th>Đơn vị công tác</th>
                                    <th class="text-center align-middle" style="min-width: 140px;">
                                        <button class="btn btn-outline-primary btn-sm w-100" style="white-space: nowrap;"
                                                onclick="momthemmoidanhsachkey(); return false;">
                                            <i class="fa fa-user-plus me-1"></i> Chia sẻ
                                        </button>
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="table-body"></tbody>
                        </table>
                    </div>


                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Chọn và đóng
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<div class="modal modal-blur fade" id="modalChiaSeKey" tabindex="-1" aria-labelledby="modalChiaSeKeyLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 500px;">
        <div class="modal-content">
            <form id="modalChiaSeKeyForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalChiaSeKeyLabel">Thông tin chia sẻ giấy phép sử dụng phần mềm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>

                <div class="modal-body">
                  
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="recipientName" class="form-label">Họ tên người nhận</label>
                            <input type="text" class="form-control" id="recipientName" name="recipientName" placeholder="Họ tên người nhận key (Nếu có)">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="recipientEmail" class="form-label">Email người nhận</label>
                            <input type="email" class="form-control" id="recipientEmail" name="recipientEmail" placeholder="<EMAIL> (Nếu có)">
                        </div>
                    </div>

            

                    <div class="mb-3">
                        <label for="donViNhanKey" class="form-label">Đơn vị nhận</label>
                        <input type="text" class="form-control" id="donViNhanKey" name="donViNhanKey" placeholder="Tên đơn vị được chia sẽ key (Nếu có)">
                    </div>

                    <div class="mb-3" style="display:none">
                        <label for="linkKichHoat" class="form-label">Đường link kích hoạt</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="linkKichHoat" name="linkKichHoat"
                                   placeholder="https://example.com/kich-hoat" disabled>
                            <button class="btn btn-outline-secondary" type="button" id="btnCopyLink">
                                <i class="ti ti-copy me-1"></i> Copy
                            </button>
                        </div>
                        <div class="form-text text-muted mt-1" style="font-size:11px !important">
                            Nếu đã nhập <strong>email người nhận</strong>, link kích hoạt sẽ được <strong>gửi qua email</strong>.
                            Bạn cũng có thể <strong>sao chép và gửi thủ công</strong> đường link này cho người nhận.
                        </div>
                    </div>


                    <div class="mb-3">
                        <label for="noiDungKey" class="form-label">Ghi chú thêm (nếu có)</label>
                        <textarea class="form-control" id="noiDungKey" name="noiDungKey" rows="2"></textarea>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-send me-1"></i> Chia sẻ key
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<div class="modal modal-blur fade" id="editInfoModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="editInfoModalForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin chia sẻ giấy phép</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="alert alert-warning d-flex align-items-center" role="alert">
                        <div class="flex-shrink-0 me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #fff3cd; border-radius: 50%;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock text-warning" width="24" height="24"
                                 viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                                 stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <rect x="5" y="11" width="14" height="10" rx="2" />
                                <circle cx="12" cy="16" r="1" />
                                <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="alert-title mb-1">Cảnh báo bảo mật</h4>
                            <div class="text-secondary">
                                Bạn không được phép chỉnh sửa thông tin cá nhân của người dùng. Tuy nhiên, bạn vẫn có thể thay đổi trạng thái khóa tài khoản nếu cần.
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Họ tên</label>
                        <p id="hovaten" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <p id="email" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đơn vị công tác</label>
                        <p id="donViCongTac" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Link kích hoạt</label>
                        <p id="linkkichhoat" class="text-muted" style=" word-wrap: break-word; overflow-wrap: break-word;"></p>
                    </div>
                 
                    <div class="mb-3 highlight-section">
                        <label class="form-label">Trạng thái</label>
                        <div class="">
                            <label class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="KhoaTaiKhoan" name="KhoaTaiKhoan" checked>
                                <span class="status-label" id="modal-statusLabel">Key hoạt động</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Lưu
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<style>


    .form-switch .form-check-input {
        transition: all 0.2s ease-in-out;
    }

        .form-switch .form-check-input:checked {
            background-color: #0054a6; /* Màu xanh dương đậm khi bật */
        }

        .form-switch .form-check-input:not(:checked) {
            background-color: #d63939; /* Màu đỏ khi tắt */
        }



    .highlight-section {
        background-color: #f8f9fa; /* Nền sáng nhẹ */
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #0054a6; /* Viền trái xanh dương nổi bật */
    }
</style>

<input type="hidden" id="ChiaSeKeyID" />

<script src="~/scripts/nguoidung/thongtingiayphep.js"></script>