﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Queries;
public record GetAllTrienKhaiPhanMemCommand : IRequest<Result<List<TrienKhaiPhanMemDto>>?>;

public class GetAllTrienKhaiPhanMemCommandValidator : AbstractValidator<GetAllTrienKhaiPhanMemCommand>
{
    public GetAllTrienKhaiPhanMemCommandValidator()
    {
    }
}

public class GetAllTrienKhaiPhanMemCommandHandler : IRequestHandler<GetAllTrienKhaiPhanMemCommand, Result<List<TrienKhaiPhanMemDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllTrienKhaiPhanMemCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<TrienKhaiPhanMemDto>>?> Handle(GetAllTrienKhaiPhanMemCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_TrienKhaiPhanMem_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<TrienKhaiPhanMemDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TrienKhaiPhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TrienKhaiPhanMemDto
        {
            TrienKhaiPhanMemID = reader.GetGuid(reader.GetOrdinal("TrienKhaiPhanMemID")),
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            TenTinh = reader["TenTinh"] as string,
            UrlTruyCap = reader["UrlTruyCap"] as string,
            MoTa = reader["MoTa"] as string,
            NgayTrienKhai = reader["NgayTrienKhai"] as string,
            TrangThai = reader["TrangThai"] as string
        };
    }
}
