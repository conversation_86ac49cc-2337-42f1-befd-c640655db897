﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace NHATTAMID2025.Web.WebClient.Controllers.NghiepVu;
[ApiExplorerSettings(IgnoreApi = true)]
[Authorize]
public class NghiepVuController : Controller
{
    [Route("{controller}/{action}")]
    [Authorize(Policy = "AdminOnly")]
    public IActionResult KeyBanQuyen()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    [Authorize(Policy = "AdminOnly")]
    public IActionResult XetDuyetYeuCau()
    {
        return View();
    }
}
