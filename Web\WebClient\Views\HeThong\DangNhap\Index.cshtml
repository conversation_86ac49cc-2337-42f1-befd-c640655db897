﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}
<style>
        body {
        background: linear-gradient(135deg, #ffffff 0%, #2baaff 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }
        /* Tổng thể trang đăng nhập */
    .page.page-center {
        min-height: 90vh;
        display: flex;
        align-items: center;
        justify-content: center;
    
        padding: 20px;
    }

    /* Card chính */
    .card.card-md {
        border: none;
        border-radius: 10px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        background-color: #ffffff;
    }

    /* Tiêu đề trang */
    .card-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1a237e;
    }

    /* Logo */
    .text-center img {
        max-height: 50px;
    }

    /* Ô input */
    .form-control {
        border-radius: 8px;
        border: 1px solid #ccc;
        transition: border-color 0.3s, box-shadow 0.3s;
    }

    .form-control:focus {
        border-color: #1e88e5;
        box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
    }

    /* Nút đăng nhập */
    .btn-primary {
        background-color: #1e88e5;
        border-color: #1e88e5;
        border-radius: 8px;
        font-weight: 600;
        font-size: 16px;
        padding: 5px;
        transition: background-color 0.3s, transform 0.2s;
    }

    .btn-primary:hover {
        background-color: #1565c0;
        transform: translateY(-2px);
    }

    /* Nhỏ gọn liên kết */
    a.small, .text-muted a {
        color: #1e88e5;
        text-decoration: none;
        font-weight: 500;
    }

    a.small:hover, .text-muted a:hover {
        text-decoration: underline;
    }

    /* Tooltip icon mắt mật khẩu */
    .input-group-text a {
        color: #888;
        transition: color 0.2s;
    }

    .input-group-text a:hover {
        color: #1e88e5;
    }

</style>
<div class="page page-center">
    <div class="container-tight py-4">

      

        <form id="registerForm" class="card card-md" action="#" method="post">
            <div class="card-body">
                <div class="text-center mb-4">
                    <img src="/Images/logoxoanen.png" height="40" alt="MISA Logo" />
                    <h2  style=" font-size: 18px;
                                    font-weight: bold;
                                    font-style: normal;
                                    font-stretch: normal;
                                    line-height: 1.21;
                                    letter-spacing: normal;
                                    text-align: center;
                                    color: #4d5156; ">
                        Đăng nhập NTSOFT ID
                    </h2>
                </div>
                <p class="text-center text-muted">Một tài khoản sử dụng tất cả ứng dụng NTSOFT</p>
                <div class="mb-3">
                    <input type="text" id="TenDangNhap" class="form-control" placeholder="Email hoặc số điện thoại" required />
                </div>

                <div class="mb-3">
                    <div class="input-group input-group-flat">
                        <input type="password" id="MatMa" class="form-control" placeholder="Nhập mật khẩu" required />
                        <span class="input-group-text">
                            <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                    <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0" />
                                    <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6" />
                                </svg>
                            </a>
                        </span>
                    </div>
                </div>
                <div id="loginAlert" class="alert alert-warning d-none" role="alert" style="font-size: 1rem; line-height: 1.4;"> </div>
                <div class="form-footer">
                    <button type="submit" class="btn btn-primary w-100">Đăng nhập</button>
                </div>

                <div class="text-center mt-3">
                    <a href="/hethong/quenmatkhau" class="small">Quên mật khẩu?</a>
                </div>
                <div class="text-center text-muted mt-3">
                    Bạn chưa có tài khoản? <a href="/hethong/dangkyuser">Đăng ký</a>
                </div>
            </div>
        </form>
    </div>
</div>

<script src="~/scripts/dangnhap.js"></script>