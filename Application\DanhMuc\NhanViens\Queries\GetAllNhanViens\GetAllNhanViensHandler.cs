﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetAllNhanViens;
public class GetAllNhanViensHandler : IRequestHandler<GetAllNhanViensQuery, List<NhanVienDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllNhanViensHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<NhanVienDto>> Handle(GetAllNhanViensQuery request, CancellationToken cancellationToken)
    {
        return await _context.NhanVien.Select(x => new NhanVienDto
        {
            NhanVienID = x.<PERSON>hanVienID,
            MaNhan<PERSON>ien = x.<PERSON><PERSON><PERSON><PERSON><PERSON>,
            TenNhanVien = x.<PERSON><PERSON><PERSON><PERSON>,
            <PERSON><PERSON><PERSON><PERSON> = x.<PERSON>,
            <PERSON>n<PERSON><PERSON><PERSON> = x.<PERSON>,
            NgungTD = x.NgungTD
        }).ToListAsync(cancellationToken);
    }
}
