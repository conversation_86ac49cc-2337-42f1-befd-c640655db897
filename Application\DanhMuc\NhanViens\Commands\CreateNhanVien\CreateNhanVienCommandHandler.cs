﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Domain.Entities;

namespace NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.CreateNhanVien;
public class CreateNhanVienCommandHandler : IRequestHandler<CreateNhanVienCommand, Guid>
{
    private readonly IApplicationDbContext _context;

    public CreateNhanVienCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Guid> Handle(CreateNhanVienCommand request, CancellationToken cancellationToken)
    {
        var entity = new NhanVien
        {
            NhanVienID = Guid.NewGuid(),
            MaNhanVien = request.MaNhanVien,
            TenNhanVien = request.TenNhanVien,
            ChucVu = request.ChucVu,
            DienGiai = request.DienGiai,
            NgungTD = request.NgungTD
        };

        _context.NhanVien.Add(entity);
        await _context.SaveChangesAsync(cancellationToken);

        return entity.NhanVienID;
    }
}
