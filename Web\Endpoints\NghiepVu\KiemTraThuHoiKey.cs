﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class KiemTraThuHoiKey : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapGet(KiemTraThuHoiKeyID, "/KiemTra/{Id}");
    }
  
    public async Task<Result<List<ChiaSeKeyDto>>?> KiemTraThuHoiKeyID(ISender sender, [FromRoute] string id)
    => await sender.Send(new KiemTraThuHoiKeyQuery(id));
    
}
