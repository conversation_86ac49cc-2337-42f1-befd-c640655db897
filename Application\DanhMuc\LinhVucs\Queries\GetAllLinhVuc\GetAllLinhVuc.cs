﻿using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.LinhVucs.DTOs;
using MediatR;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace NHATTAMID2025.Application.LinhVucs.Queries.GetAllLinhVuc;

public record GetAllLinhVucQuery(int? PageNumber, int? PageSize) : IRequest<PaginatedList<LinhVucDto>>;

public class GetAllLinhVucQueryValidator : AbstractValidator<GetAllLinhVucQuery>
{
    public GetAllLinhVucQueryValidator()
    {
        RuleFor(x => x.PageNumber).GreaterThanOrEqualTo(0);
        RuleFor(x => x.PageSize).GreaterThanOrEqualTo(0).LessThanOrEqualTo(1000);
    }
}

public class GetAllLinhVucQueryHandler : IRequestHandler<GetAllLinhVucQuery, PaginatedList<LinhVucDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllLinhVucQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedList<LinhVucDto>> Handle(GetAllLinhVucQuery request, CancellationToken cancellationToken)
    {
        var query = _context.LinhVuc.AsNoTracking();
        var totalCount = await query.CountAsync(cancellationToken);

        bool noPaging = !request.PageNumber.HasValue || !request.PageSize.HasValue
                        || request.PageNumber <= 0 || request.PageSize <= 0;

        if (noPaging)
        {
            var allItems = await query
                .Select(x => new LinhVucDto
                {
                    MaLinhVuc = x.MaLinhVuc ?? "",
                    TenLinhVuc = x.TenLinhVuc ?? "",
                    MaNganh = x.MaNganh ?? "",
                    MoTa = x.MoTa ?? ""
                })
                .ToListAsync(cancellationToken);

            return new PaginatedList<LinhVucDto>(allItems, allItems.Count, 1, allItems.Count);
        }

        var items = await query
            .Skip((request.PageNumber!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize.Value)
            .Select(x => new LinhVucDto
            {
                MaLinhVuc = x.MaLinhVuc ?? "",
                TenLinhVuc = x.TenLinhVuc ?? "",
                MaNganh = x.MaNganh ?? "",
                MoTa = x.MoTa ?? ""
            })
            .ToListAsync(cancellationToken);

        return new PaginatedList<LinhVucDto>(items, totalCount, request.PageNumber.Value, request.PageSize.Value);
    }
}

public class PaginatedList<T>
{
    public List<T> Items { get; }
    public int TotalCount { get; }
    public int PageNumber { get; }
    public int PageSize { get; }
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling(TotalCount / (double)PageSize) : 1;

    public PaginatedList(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items;
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}
