﻿using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Domain.Entities;
using Microsoft.IdentityModel.Tokens;
using NHATTAMID2025.Application.Common.Interfaces;
using WEB_DLL;
using Microsoft.Extensions.Configuration;


namespace NHATTAMID2025.Application.Authentication;
public class TokenResponse
{
    public string UserId { get; set; } = "";
    public string TokenType { get; set; } = "Bearer";
    public string AccessToken { get; set; } = "";
    public int ExpiresIn { get; set; } // Tính bằng giây.
    public string RefreshToken { get; set; } = "";
    public string? ErrorMessage { get; set; }
}

public record LoginCommand : IRequest<TokenResponse>
{
    //Username
    //Mat khau
    public string TenDangNhap { get; set; } = string.Empty;
    public string MatKhau { get; set; } = string.Empty;
}

public class LoginCommandValidator : AbstractValidator<LoginCommand>
{
    public LoginCommandValidator()
    {
    }
}

public class LoginCommandHandler : IRequestHandler<LoginCommand, TokenResponse>
{
    private readonly IApplicationDbContext _context;
    private readonly IConfiguration _configuration;

    public LoginCommandHandler(IApplicationDbContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
    }
    public async Task<TokenResponse> Handle(LoginCommand request, CancellationToken cancellationToken)
    {
        string keyMaHoaMatKhau = "rateAnd2012";
        string encryptedPassword = ntsSecurity._mEncrypt(request.MatKhau.Trim(), keyMaHoaMatKhau,true );
        Users? user = await _context.Users
        .Where(u => (u.TenDangNhap == request.TenDangNhap.Trim()
                     || u.Email == request.TenDangNhap.Trim()
                     || u.SoDienThoai == request.TenDangNhap.Trim())
                    && u.MatMa == encryptedPassword
                    && u.DangSD == true)
        .FirstOrDefaultAsync(cancellationToken);

        if (user == null)
        {
            return new TokenResponse
            {
                ErrorMessage = "1_Tên đăng nhập hoặc mật khẩu không chính xác. Vui lòng kiểm tra và thử lại!"
            };
        }

        if (user.XatThucEmail == false)
        {
            return new TokenResponse
            {
                ErrorMessage = "2_Vui lòng xác minh địa chỉ email của bạn để tiếp tục!"
            };
        }

        if (user.DangSD == false)
        {
            return new TokenResponse
            {
                ErrorMessage = "3_Tài khoản của bạn hiện đang bị khóa. Vui lòng liên hệ quản trị viên để biết thêm chi tiết!"
            };
        }

        //var donvi = await _context.DonVi.FirstOrDefaultAsync(x => x.DonViId == user.DonViId, cancellationToken);


        var claims = new List<Claim>
        {
            new("UserId", user.UserID.ToString()),
            new ("TenDangNhap", user.TenDangNhap ?? ""),
            //new ("DonViID", user.DonViId.ToString()),
            //new ("TenDonVi", donvi?.TenDonVi ?? ""),
        };

        // Tạo access token
        string accessToken = GenerateJwtToken(user, _configuration, claims);

        // Tạo refresh token
        string refreshToken = GenerateRefreshToken(_configuration);
        int expiresIn = 60 * 60; // 1 giờ


        return new TokenResponse
        {
            UserId = user.UserID.ToString(),
            AccessToken = accessToken,
            RefreshToken = refreshToken,
            ExpiresIn = expiresIn,
            ErrorMessage=""
        };
    }


    #region Helper Methods
    /// <summary>
    /// Tạo JWT token cho người dùng đã xác thực, bao gồm các claim được cung cấp.
    /// </summary>
    /// <param name="user">Người dùng đã xác thực.</param>
    /// <param name="configuration">Cấu hình chứa thông tin JWT.</param>
    /// <param name="claims">Danh sách các claim cần gắn vào token.</param>
    /// <returns>Chuỗi JWT token.</returns>
    private static string GenerateJwtToken(Users? users, IConfiguration configuration, IEnumerable<Claim> claims)
    {
        // bind the entire "Jwt" section
        var jwtSection = configuration.GetSection("Jwt");
        var key = jwtSection["Key"]
                             ?? throw new InvalidOperationException("Jwt:Key not configured.");
        var issuer = jwtSection["Issuer"]
                             ?? throw new InvalidOperationException("Jwt:Issuer not configured.");
        var audience = jwtSection["Audience"]
                             ?? throw new InvalidOperationException("Jwt:Audience not configured.");
        var expiryMinutes = int.TryParse(jwtSection["ExpiryMinutes"], out var mins)
                             ? mins
                             : throw new InvalidOperationException("Jwt:ExpiryMinutes invalid or not configured.");

        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(key));
        var signingCreds = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var token = new JwtSecurityToken(
            issuer: configuration["Jwt:Issuer"],
            audience: configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.Now.AddDays(30),
            signingCredentials: signingCreds);

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// Tạo refresh token dưới dạng JWT với thời gian sống lâu hơn (ví dụ: 7 ngày).
    /// </summary>
    /// <param name="configuration">Cấu hình chứa thông tin JWT.</param>
    /// <returns>Chuỗi refresh token.</returns>
    private static string GenerateRefreshToken(IConfiguration configuration)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:Key"] ?? ""));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var refreshToken = new JwtSecurityToken(
            issuer: configuration["Jwt:Issuer"],
            audience: configuration["Jwt:Audience"],
            claims: new List<Claim>
            {
                    // Chỉ định đây là refresh token.
                    new Claim("token_type", "refresh")
            },
            expires: DateTime.UtcNow.AddDays(7),
            signingCredentials: creds);

        return new JwtSecurityTokenHandler().WriteToken(refreshToken);
    }

    #endregion
}
