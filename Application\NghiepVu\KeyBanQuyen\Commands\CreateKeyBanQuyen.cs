﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;
using NHATTAMID2025.Domain.Entities;
using WEB_DLL;

namespace NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands;

public record CreateKeyBanQuyenCommand(
    string KeyID,
    string MaKey,
    string TrienKhaiPhanMemID,
    string GoiBanQuyenID,
    string NgayTao,
    string NgayHetHan,
    string SoLanKichHoat,
    string UserID,
    bool Khoa
    ) : IRequest<Result<KeyBanQuyenDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateKeyBanQuyenCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateKeyBanQuyenCommandHandler : IRequestHandler<CreateKeyBanQuyenCommand, Result<KeyBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateKeyBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<KeyBanQuyenDto>?> Handle(CreateKeyBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var idKey = Guid.NewGuid();

        //string keyMaHoaMatKhau = "rateAnd2012";

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@KeyID", idKey),
            new Microsoft.Data.SqlClient.SqlParameter("@MaKey", idKey.ToString()),
            new Microsoft.Data.SqlClient.SqlParameter("@TrienKhaiPhanMemID", request.TrienKhaiPhanMemID),
            new Microsoft.Data.SqlClient.SqlParameter("@GoiBanQuyenID", DungChung.NormalizationGuid(request.GoiBanQuyenID)),
            new Microsoft.Data.SqlClient.SqlParameter("@NgayTao", request.NgayTao),
            new Microsoft.Data.SqlClient.SqlParameter("@NgayHetHan", string.IsNullOrWhiteSpace(request.NgayHetHan) ? DBNull.Value : request.NgayHetHan),
            new Microsoft.Data.SqlClient.SqlParameter("@SoLanKichHoat", DungChung.NormalizationNumber(request.SoLanKichHoat)),
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", DungChung.NormalizationGuid(request.UserID)),
            new Microsoft.Data.SqlClient.SqlParameter("@Khoa", request.Khoa)
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KeyBanQuyen_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<KeyBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private KeyBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KeyBanQuyenDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
        };
    }
}
