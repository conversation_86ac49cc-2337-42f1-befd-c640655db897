﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.PhanQuyen.DTOs;

namespace NHATTAMID2025.Application.HeThong.PhanQuyen.Queries;
internal class GetPhanQuyenByNhomID
{
}
public record GetPhanQuyenByNhomIDQuery(
    string NhomTaiKhoan
    ) : IRequest<Result<List<PhanQuyenDto>>?>;


public class GetPhanQuyenByNhomIDQueryValidator : AbstractValidator<GetPhanQuyenByNhomIDQuery>
{
    public GetPhanQuyenByNhomIDQueryValidator()
    {
    }
}
public class GetPhanQuyenByNhomIDQueryHandler : IRequestHandler<GetPhanQuyenByNhomIDQuery, Result<List<PhanQuyenDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetPhanQuyenByNhomIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<PhanQuyenDto>>?> Handle(GetPhanQuyenByNhomIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                 new Microsoft.Data.SqlClient.SqlParameter("@NhomTaiKhoan", request.NhomTaiKhoan)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_PhanQuyen_GetByNhomTaiKhoan",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<PhanQuyenDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private PhanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new PhanQuyenDto
        {
            PhanQuyenID = reader.GetGuid(reader.GetOrdinal("PhanQuyenID")),
            TenChucNang = reader["TenChucNang"] as string,
            DuongDan = reader["DuongDan"] as string,
            UrlTruyCap = reader["UrlTruyCap"] as string,
            Icon = reader["Icon"] as string
        };
    }
}
