﻿/* ======= Form ======= */
.form-label {
    font-weight: 600;
    color: #2c3e50;
}

.form-control:focus, .form-select:focus {
    border-color: #206bc4;
    box-shadow: 0 0 0 0.2rem rgba(32, 107, 196, 0.25);
}

/* ======= Nút chính ======= */
.btn-primary {
    background-color: #206bc4;
    border-color: #1a64b7;
}

    .btn-primary:hover {
        background-color: #1a64b7;
        border-color: #155aad;
    }

/* ======= Table dùng chung ======= */
table.custom-table {
    width: 100% !important;
    border-collapse: collapse !important;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    background-color: #ffffff;
}

    table.custom-table th {
        background: linear-gradient(90deg, #f9fafb 0%, #ffffff 100%) !important;
        font-weight: 600 !important;
        text-transform: math-auto !important;
        letter-spacing: 0.5px !important;
        color: #2c3e50 !important;
        border-bottom: 1px solid #1a64b7 !important;
        border-style: hidden;
        text-align: center !important;
        font-size: 12px;
    }

    table.custom-table td {
        text-align: left;
        border-bottom: 1px solid #b1d1e3;
        color: #34495e;
        font-size: 12px;
        white-space: nowrap;
        transition: background-color 0.3s ease;
    }

    table.custom-table tbody tr {
        background-color: #ffffff;
    }

        table.custom-table tbody tr:hover {
            background-color: #f7fafc;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            color: #2d3748;
        }

        table.custom-table tbody tr:nth-child(even) {
            background-color: #ffffff;
        }

/* ======= DataTables bổ sung ======= */
.dataTables_wrapper .dataTables_filter input {
    padding: 6px 10px 6px 30px !important;
    border: 1px solid #e0e7eb;
    border-radius: 4px;
    font-size: 13px;
    width: 180px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%237f8c8d' viewBox='0 0 16 16'%3E%3Cpath d='M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z'%3E%3C/path%3E%3C/svg%3E") no-repeat left 8px center;
    background-size: 16px 16px;
}

    .dataTables_wrapper .dataTables_filter input:focus {
        border-color: #2ecc71;
        box-shadow: 0 0 5px rgba(46, 204, 113, 0.2);
    }

/* DataTables info, length */
.dataTables_info,
.dataTables_length {
    color: #34495e;
    font-size: 11px !important;
    white-space: nowrap;
    transition: background-color 0.3s ease;
    margin-top: 15px;
}

/* Paginate */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0;
    margin: 0 2px;
    border: 1px solid #d1d9e0;
    border-radius: 8px;
    color: #007bff;
    background: #fff;
    cursor: pointer;
    font-size: 10px;
    transition: border-color 0.3s ease;
}

    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        color: #bdc3c7;
        cursor: not-allowed;
    }

/* ======= Action buttons ======= */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.btn-sm {
    padding: 2px 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background-color: transparent;
    color: #4a5568;
    transition: background-color 0.2s ease, color 0.2s ease;
}

    .btn-sm i {
        font-size: 14px;
    }

    .btn-sm:hover {
        background-color: #e2e8f0;
        color: #2d3748;
    }

/* ======= Badge styles ======= */
.badge {
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.badge-success {
    background-color: #c6f6d5;
    color: #22543d;
}

.badge-danger {
    background-color: #fed7d7;
    color: #742a2a;
}

.badge-warning {
    background-color: #fefcbf;
    color: #744210;
}

/* ======= DataTable fixed column fix ======= */
.dtfc-right-top-blocker,
.dtfc-right-bottom-blocker,
table.dataTable tbody tr > .dtfc-fixed-left,
table.dataTable tbody tr > .dtfc-fixed-right {
    background-color: white !important;
    z-index: 1;
    right: -1px !important;
}

/* ======= Container fix ======= */
.row .dt-row {
    overflow-x: auto;
    --tblr-gutter-x: none !important;
}

    .row .dt-row .col-sm-12 {
        overflow-x: auto;
        height: 500px;
    }

.card-body {
    flex: 1 1 auto;
    padding: -1px;
    color: var(--tblr-card-color);
}



/* Giống với Bootstrap placeholder mặc định */
.ts-wrapper .ts-control input::placeholder {
    font-size: 11.5px;
    opacity: 1; /* đảm bảo không bị mờ */
}

/* Ghi đè font và padding cho input */
.ts-wrapper .ts-control input {
    font-size: 11.5px;
    font-family: inherit;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5;
}

    /* Giống với Bootstrap placeholder mặc định */
    .ts-wrapper .ts-control input::placeholder {
        font-size: 11.5px;
        font-style: normal;
        opacity: 1; /* đảm bảo không bị mờ */
    }

/* Ghi đè font và padding cho input */
.ts-wrapper .ts-control input {
    font-size: 11.5px;
    font-family: inherit;
    padding: 0 !important;
    margin: 0 !important;
    line-height: 1.5;
}


.ts-dropdown .active {
    background-color: #e3f2fd !important;
    color: #0d6efd;
}

/* Ghi đè TomSelect khi kết hợp Bootstrap */
.ts-wrapper.form-select.single {
    height: 30px !important;
    min-height: 30px !important;
    padding: 0 !important;
    border-radius: 4px;
    display: flex;
    align-items: center;
    border: 1px solid #ced4da;
    background-color: #fff;
    font-size: 13px;
}

    /* Vùng hiển thị đã chọn */
    .ts-wrapper.form-select.single .ts-control {
        height: 30px !important;
        min-height: 30px !important;
        padding: 0 8px !important;
        line-height: 30px !important;
        display: flex;
        align-items: center;
        background-color: transparent;
        border: none !important;
        box-shadow: none !important;
    }

        /* Input text */
        .ts-wrapper.form-select.single .ts-control input {
            height: 100% !important;
            padding: 0 !important;
            margin: 0 !important;
        }

/* Tùy chọn trong dropdown */
.ts-dropdown .ts-dropdown-content .option {
    padding: 6px 8px;
    font-size: 13px;
}