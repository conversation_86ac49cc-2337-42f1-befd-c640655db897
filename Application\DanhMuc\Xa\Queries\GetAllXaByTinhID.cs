﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Xa.Queries;

public record GetAllXaByTinhIDQuery(
    string TinhID
    ) : IRequest<Result<List<XaDto>>?>;
public class GetAllXaByTinhIDQueryValidator : AbstractValidator<GetAllXaByTinhIDQuery>
{
    public GetAllXaByTinhIDQueryValidator()
    {
    }
}
public class GetAllXaByTinhIDQueryHandler : IRequestHandler<GetAllXaByTinhIDQuery, Result<List<XaDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllXaByTinhIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<XaDto>>?> Handle(GetAllXaByTinhIDQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@TinhID", request.TinhID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Xa_GetByTinh",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<List<XaDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private XaDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new XaDto
        {
            XaID = reader.GetGuid(reader.GetOrdinal("XaID")),
            TinhID = reader.IsDBNull(reader.GetOrdinal("TinhID"))
                     ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            MaXa = reader["MaXa"] as string,
            TenXa = reader["TenXa"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
