﻿var ThaoTac;

const danh<PERSON>achThoiHanSD = [
  {
    ID: "7",
    ten: "1 tuần",
    icon: "fas fa-clock " // Biểu tượng tháng, màu xanh lam
  },
  {
    ID: "30",
    ten: "1 tháng",
    icon: "fas fa-calendar-minus text-primary" // <PERSON>iể<PERSON> tượng tháng, màu xanh lam
  },
  {
    ID: "90",
    ten: "3 tháng",
    icon: "fas fa-calendar-alt text-info" // Biểu tượng lịch nâng cao, màu xanh nhạt
  },
  {
    ID: "180",
    ten: "6 tháng",
    icon: "fas fa-calendar-check text-teal" // Lịch kiểm tra, màu teal/xanh cyan
  },
  {
    ID: "365",
    ten: "1 năm",
    icon: "fas fa-calendar text-warning" // Biểu tượng lịch đầy đủ, màu vàng cam
  },
  {
    ID: "0",
    ten: "Vĩ<PERSON> viễn",
    icon: "fas fa-infinity text-success" // <PERSON>i<PERSON><PERSON> tượng vô cực, màu xanh lá
  },
  {
    ID: "1",
    ten: "Ngày",
    icon: "fas fa-edit text-secondary" // Biểu tượng cho chỉnh sửa số ngày, màu xám
  }
];


$(document).ready(async function () {
  tablerSelect("ThoiHanLoai", danhSachThoiHanSD, "ten", "ID", "");
  var danhsachLoaiNguoiDung = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/LoaiNguoiDung/getall', null)
  if (danhsachLoaiNguoiDung.succeeded) {
    tablerSelect("LoaiNguoiDungID", danhsachLoaiNguoiDung.result, "tenLoaiNguoiDung", "loaiNguoiDungID",  "");
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + Tinh.errors)
  }

  LoadLuoi()
});
document.addEventListener("DOMContentLoaded", function () {
  const loaiSelect = document.getElementById("ThoiHanLoai");
  const ngayInput = document.getElementById("ThoiHanNgay");

  function handleChange() {
    const selectedValue = loaiSelect.value;

    if (selectedValue === "1") {
      // Tùy chọn: cho nhập tay
      ngayInput.value = "";
      ngayInput.disabled = false;
      ngayInput.required = true;
      ngayInput.classList.remove("bg-light");
    } else {
      // Còn lại: tự động set và khóa input
      ngayInput.value = selectedValue;
      ngayInput.disabled = true;
      ngayInput.required = false;
      ngayInput.classList.add("bg-light");
    }
  }

  loaiSelect.addEventListener("change", handleChange);
  handleChange(); // Gọi lần đầu để áp dụng nếu có sẵn
});
async function LoadLuoi() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/GoiBanQuyen/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#phanMemTable').DataTable().destroy();
    $('#phanMemTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },

      columns: [
        {
          data: 'thoiHanNgay',
          width: "70px",
          render: function (data, type, row) {
            const item = danhSachThoiHanSD.find(i => i.ID === String(data));
            if (item) {
              return `
                      <span class="badge bg-${item.badge}" style="font-size: 0.9rem;" title="${item.ten}">
                        <i class="${item.icon} me-1"></i> ${item.ten}
                      </span>
                    `;
            }
            const item1 = danhSachThoiHanSD.find(i => i.ID === String(1));
            if (item1) {
              return `
                      <span class="badge bg-${item1.badge}" style="font-size: 0.9rem;" title="${item1.ten}">
                        <i class="${item1.icon} me-1"></i> ${data+' '+item1.ten}
                      </span>
                    `;
            }
          }
        },
        {
          data: null,
          width: "500px",
          className: 'align-middle text-start',
          render: function (data, type, row) {

            const matched = danhSachThoiHanSD.find(x => x.ID === String(row.thoiHanNgay));
          
            return `
          <div>
            <div style="font-size: 12px !important;  color: #0d6efd;">
              <i class="fas fa-fire text-danger me-1"></i> ${row.tenGoi}
              <span style="font-size: 0.8rem; color: #6c757d;">(${row.maGoi})</span>
            </div>
       
          </div>
        `;
          }
        },


       
        {
          data: 'soThietBiToiDa',
          width: "50px",
          className: 'align-middle text-start',
          render: function (data, type, row) {
            const icon = `<i class="fas fa-laptop-code text-primary me-1"></i>`;
            const soLuong = isNaN(data) || data == 0
              ? `<span class="text-muted fst-italic">Không giới hạn</span>`
              : `<span style="font-weight: 500; color: #0d6efd;">${data}</span>`;
            return `${icon} ${soLuong} <span style="color: #6c757d; font-size: 0.8rem;">thiết bị</span>`;
          }
        },

        {
          data: 'giaTien',
          width: "80px",
          className: 'align-middle text-end',
          render: function (data, type, row) {
            if (!data || isNaN(data)) return "0 ₫";
            return Number(data).toLocaleString('vi-VN', { style: 'currency', currency: 'VND' });
          }
        },
        { data: 'tenLoaiNguoiDung', width: "150px" },
        {
          data: 'moTa',
          width: "300px",
          render: function (data, type, row, meta) {
            if (!data) return '';

            const maxLength = 50;
            const shortText = data.length > maxLength ? data.substring(0, maxLength) + '...' : data;

            if (data.length <= maxLength) {
              return `<span>${data}</span>`;
            }

            return `
            <span class="short-text">${shortText}</span>
            <span class="full-text d-none">${data}</span>
            <a href="javascript:void(0)" class="toggle-moTa" style="color:#3182ce; font-size:12px; margin-left:6px;">Xem thêm</a>
        `;
          }
        },
        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "90px",
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.goiBanQuyenID}')">
                                            <i class="fa fa-pencil text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.goiBanQuyenID}')">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    //setTimeout(() => {
    //  table.columns.adjust().draw(false);
    //}, 300);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

$('#goiBanQuyenForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    GoiBanQuyenID: document.getElementById("GoiBanQuyenID").value,
    MaGoi: document.getElementById("MaGoi").value,
    TenGoi: document.getElementById("TenGoi").value,
    MoTa: document.getElementById("MoTa").value,
    ThoiHanNgay: document.getElementById("ThoiHanNgay").value,
    SoThietBiToiDa: document.getElementById("SoThietBiToiDa").value,
    LoaiNguoiDungID: document.getElementById("LoaiNguoiDungID").value,
    GiaTien: document.getElementById("GiaTien").value
  };

  if (ThaoTac == 'Them') {
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/goibanquyen/create', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Thêm mới dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-goiBanQuyen').modal('hide');
        setTimeout(() => {
          $('body').removeClass('modal-open');
          $('.modal-backdrop').remove();
        }, 100); // đợi hiệu ứng fade
      }
    } catch (error) {
      NTS.canhbao("Thêm mới dữ liệu thất bại!");
    }
  }
  if (ThaoTac == 'Sua') {
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/goibanquyen/update', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Cập nhật dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-goiBanQuyen').modal('hide');
        setTimeout(() => {
          $('body').removeClass('modal-open');
          $('.modal-backdrop').remove();
        }, 100); // đợi hiệu ứng fade
      }
    } catch (error) {
      NTS.canhbao("Cập nhật dữ liệu thất bại!");
    }
  }
});

function targetonclick() {
  ThaoTac = 'Them'
  resetForm("goiBanQuyenForm")
}

async function deleteRow(id) {
  // Icon SVG Tabler cảnh báo (triangle-exclamation)
  const tablerWarningSVG = `
    <svg class="tabler-icon-warning" xmlns="http://www.w3.org/2000/svg" 
         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
         stroke-linejoin="round" stroke-linecap="round" style="width:72px; height:72px; margin: 0 auto 15px auto; stroke:#f59e0b;">
      <path d="M12 9v4"></path>
      <path d="M12 17h.01"></path>
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    </svg>
  `;

  const confirmed = await Swal.fire({
    title: 'Bạn có chắc muốn xóa?',
    text: "Hành động này sẽ không thể hoàn tác!",
    icon: 'warning', // vẫn phải có icon để vùng icon xuất hiện
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    reverseButtons: true,
    didOpen: () => {
      // Thay thế icon mặc định bằng SVG Tabler
      const iconElem = document.querySelector('.swal2-icon.swal2-warning');
      if (iconElem) {
        iconElem.innerHTML = tablerWarningSVG;
        iconElem.style.background = 'none'; // bỏ nền mặc định
        iconElem.style.border = 'none'; // bỏ viền mặc định
      }
    }
  });

  if (confirmed.isConfirmed) {
    const data = {
      GoiBanQuyenID: id
    };

    try {
      const response = await NTS.getAjaxAPIAsync(
        'POST',
        window.location.origin + '/api/goibanquyen/delete',
        data
      );
      if (response.succeeded == false && response.errors.length > 0) {
        NTS.canhbao(response.errors[0]);
      } else {
        if (response.succeeded == true) {
          await Swal.fire('Đã xóa!', 'Bản ghi đã được xóa thành công.', 'success');
          LoadLuoi();
        }
      }
    } catch (error) {
      Swal.fire('Lỗi!', 'Không thể xóa bản ghi.', 'error');
    }
  }
}

async function editRow(id) {
  ThaoTac = 'Sua'
  ResetPasswordVisibility()
  $('#modal-goiBanQuyen').modal("show")
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/goibanquyen/getbyid/${id}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    document.getElementById("MaGoi").value = originalData[0].maGoi
    document.getElementById("TenGoi").value = originalData[0].tenGoi
    document.getElementById("MoTa").value = originalData[0].moTa

    const soNgay = String(originalData[0].thoiHanNgay);
    const matched = danhSachThoiHanSD.some(item => item.ID === soNgay);
    document.getElementById("ThoiHanLoai").tomselect.setValue(matched ? soNgay : "1");

    document.getElementById("ThoiHanNgay").value = originalData[0].thoiHanNgay
    document.getElementById("SoThietBiToiDa").value = originalData[0].soThietBiToiDa
    document.getElementById("LoaiNguoiDungID").tomselect.setValue(originalData[0].loaiNguoiDungID);
    document.getElementById("GiaTien").value = originalData[0].giaTien
    $('#GoiBanQuyenID').value(originalData[0].goiBanQuyenID)
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }

}

document.getElementById('GiaTien').addEventListener('input', function (e) {
  let value = e.target.value;

  // Xóa tất cả ký tự không phải số
  value = value.replace(/\D/g, '');

  // Định dạng lại với dấu chấm ngăn cách hàng nghìn
  value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  e.target.value = value;
});

$('#phanMemTable').on('click', '.toggle-moTa', function () {
  const $row = $(this).closest('td');
  const $short = $row.find('.short-text');
  const $full = $row.find('.full-text');

  if ($full.hasClass('d-none')) {
    $short.addClass('d-none');
    $full.removeClass('d-none');
    $(this).text('Ẩn bớt');
  } else {
    $short.removeClass('d-none');
    $full.addClass('d-none');
    $(this).text('Xem thêm');
  }
});

