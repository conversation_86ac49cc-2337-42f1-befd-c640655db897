﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Queries;
internal class GetDangKyGiayPhepByUserID
{
}

public record GetDangKyGiayPhepByUserIDQuery(
    string UserID
    ) : IRequest<Result<List<DangKyGiayPhepDto>>?>;


public class GetDangKyGiayPhepByUserIDQueryValidator : AbstractValidator<GetDangKyGiayPhepByUserIDQuery>
{
    public GetDangKyGiayPhepByUserIDQueryValidator()
    {
    }
}
public class GetDangKyGiayPhepByUserIDQueryHandler : IRequestHandler<GetDangKyGiayPhepByUserIDQuery, Result<List<DangKyGiayPhepDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetDangKyGiayPhepByUserIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DangKyGiayPhepDto>>?> Handle(GetDangKyGiayPhepByUserIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyGiayPhep_GetByUserID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<DangKyGiayPhepDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private DangKyGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DangKyGiayPhepDto
        {
            DangKyGiayPhepID = reader.GetGuid(reader.GetOrdinal("DangKyGiayPhepID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            HoTen = reader["HoTen"] as string,
            Email = reader["Email"] as string,
            DonViCongTac = reader["DonViCongTac"] as string,
            NoiDung = reader["NoiDung"] as string,
            LoaiDangKy = reader["LoaiDangKy"] as string,
            NgayDangKy = reader["NgayDangKy"] as string,
            TrangThai = reader["TrangThai"] as string
        };
    }
}

