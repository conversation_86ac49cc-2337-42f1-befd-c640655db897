﻿using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;

namespace NHATTAMID2025.Application.ChucVus.Queries.GetAllChucVu;
public record GetAllChucVuQuery : IRequest<List<ChucVuDto>>;

public class GetAllChucVuQueryValidator : AbstractValidator<GetAllChucVuQuery>
{
    public GetAllChucVuQueryValidator()
    {
    }
}

public class GetAllChucVuQueryHandler : IRequestHandler<GetAllChucVuQuery, List<ChucVuDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllChucVuQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<ChucVuDto>> Handle(GetAllChucVuQuery request, CancellationToken cancellationToken)
    {
        const string spGetAll = "sp_ChucVu_GetAll"; // tên store procedure

        return await _context.ExecuteSqlQueryRawAsync(
            spGetAll,
            MapFromReader,
            true,
            Array.Empty<System.Data.Common.DbParameter>()
        );
    }

    private ChucVuDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChucVuDto
        {
            ChucVuID = reader.GetGuid(reader.GetOrdinal("ChucVuID")),
            MaChucVu = reader["MaChucVu"] as string,
            TenChucVu = reader["TenChucVu"] as string,
            DienGiai = reader["DienGiai"] as string,
            NgungTD = reader.IsDBNull(reader.GetOrdinal("NgungTD")) ? null : reader.GetBoolean(reader.GetOrdinal("NgungTD"))
        };
    }
}
