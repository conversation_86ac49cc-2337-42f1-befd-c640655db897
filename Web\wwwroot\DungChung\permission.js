﻿var ntspermiss = { xem: !1, them: 1, xoa: 1, sua: 1, in: 1, xuatExcel: 1 }, keyper, chuoi<PERSON><PERSON><PERSON><PERSON><PERSON>;
$(async () => {
  var chuoiPhanQuyen = await fetch('/api/PhanQuyenClient/get-quyen-from-request')
    .then(response => {
      if (!response.ok) throw new Error(response.statusText);
      return response.json();
    })
    .then(data => {
      ntspermiss = {
        xem: data.xem,
        them: data.them,
        xoa: data.xoa,
        sua: data.sua,
        in: data.in,
        xuatExcel: data.xuatExcel
      }
    })
    .catch(err => console.error('Fetch error:', err));

    $('[permiss]').each(function () {
        switch ($(this).attr('permiss')) {
            case 'xem':
                ('xem' in ntspermiss) && $(this).prop('disabled', !ntspermiss.xem);
                break;
            case 'them':
                ('them' in ntspermiss) && $(this).prop('disabled', !ntspermiss.them);
                break;
            case 'xoa':
                ('xoa' in ntspermiss) && $(this).prop('disabled', !ntspermiss.xoa);
                break;
            case 'sua':
                ('sua' in ntspermiss) && $(this).prop('disabled', !ntspermiss.sua);
                break;
            case 'in':
                ('in' in ntspermiss) && $(this).prop('disabled', !ntspermiss.in);
                break;
            case 'xuat':
                ('xuat' in ntspermiss) && $(this).prop('disabled', !ntspermiss.xuatExcel);
                break;
            default:
        }
    })
});