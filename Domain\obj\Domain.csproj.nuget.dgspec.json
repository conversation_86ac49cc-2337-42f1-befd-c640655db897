{"format": 1, "restore": {"E:\\NTS\\svn\\APINhatTamID\\NHATTAMID2025\\src\\Domain\\Domain.csproj": {}}, "projects": {"E:\\NTS\\svn\\APINhatTamID\\NHATTAMID2025\\src\\Domain\\Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\NTS\\svn\\APINhatTamID\\NHATTAMID2025\\src\\Domain\\Domain.csproj", "projectName": "NHATTAMID2025.Domain", "projectPath": "E:\\NTS\\svn\\APINhatTamID\\NHATTAMID2025\\src\\Domain\\Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\NTS\\svn\\APINhatTamID\\NHATTAMID2025\\src\\Domain\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MediatR": {"target": "Package", "version": "[12.4.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"Ardalis.GuardClauses": "4.6.0", "AutoMapper": "13.0.1", "Azure.Extensions.AspNetCore.Configuration.Secrets": "1.3.2", "Azure.Identity": "1.12.0", "coverlet.collector": "6.0.2", "FluentAssertions": "6.12.0", "FluentValidation.AspNetCore": "11.3.0", "FluentValidation.DependencyInjectionExtensions": "11.9.2", "JsonConverter.Newtonsoft.Json": "0.7.1", "MediatR": "12.4.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.8", "Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore": "8.0.8", "Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.8", "Microsoft.AspNetCore.Identity.UI": "8.0.8", "Microsoft.AspNetCore.Mvc.Testing": "8.0.8", "Microsoft.AspNetCore.OpenApi": "8.0.8", "Microsoft.Data.SqlClient": "5.1.5", "Microsoft.EntityFrameworkCore": "8.0.8", "Microsoft.EntityFrameworkCore.Design": "8.0.8", "Microsoft.EntityFrameworkCore.Relational": "8.0.8", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.8", "Microsoft.EntityFrameworkCore.Tools": "8.0.8", "Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": "8.0.8", "Microsoft.NET.Test.Sdk": "17.11.0", "Moq": "4.20.70", "NSwag.AspNetCore": "14.1.0", "NSwag.MSBuild": "14.1.0", "nunit": "3.14.0", "NUnit.Analyzers": "3.9.0", "NUnit3TestAdapter": "4.5.0", "Respawn": "6.2.1", "Testcontainers.MsSql": "3.9.0"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}