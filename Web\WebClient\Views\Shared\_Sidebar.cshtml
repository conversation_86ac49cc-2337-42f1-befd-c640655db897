<div class="position-sticky pt-3">
    <div class="text-center mb-4">
        <h4>Permission Management</h4>
    </div>
    <ul class="nav flex-column">
        <li class="nav-item">
            <a class="nav-link @( (ViewContext.RouteData.Values["Controller"]?.ToString() == "Home" &&
                                    ViewContext.RouteData.Values["Action"]?.ToString() == "Index") ? "active" : "")"
               asp-controller="Home" asp-action="Index">
                <i class="bi bi-house-door me-1"></i>
                Dashboard
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @( (ViewContext.RouteData.Values["Controller"]?.ToString() == "RolePermission") ? "active" : "")"
               asp-controller="RolePermission" asp-action="Index">
                <i class="bi bi-people me-1"></i>
                Role Permissions
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @( (ViewContext.RouteData.Values["Controller"]?.ToString() == "UserPermission") ? "active" : "")"
               asp-controller="UserPermission" asp-action="Index">
                <i class="bi bi-person-lock me-1"></i>
                User Permissions
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link @( (ViewContext.RouteData.Values["Controller"]?.ToString() == "FeatureManagement") ? "active" : "")"
               asp-controller="FeatureManagement" asp-action="Index">
                <i class="bi bi-toggles me-1"></i>
                Feature Management
            </a>
        </li>
    </ul>
</div>
