﻿namespace NHATTAMID2025.Application.Common.Models;

public class Result<T> where T : class
{
    internal Result(bool succeeded, IEnumerable<string> errors)
    {
        this.result = null;
        Succeeded = succeeded;
        Errors = errors.ToArray();
    }

    internal Result(T? result, bool succeeded, IEnumerable<string> errors)
    {
        this.result = result;
        Succeeded = succeeded;
        Errors = errors.ToArray();
    }

    public bool Succeeded { get; init; }

    public string[] Errors { get; init; }
    public T? result { get; init; }
    public static Result<T> Success(T? result)
    {
        return new Result<T>(result,true, Array.Empty<string>());
    }

    public static Result<T> Success()
    {
        return new Result<T>(true, Array.Empty<string>());
    }

    public static Result<T> Failure(IEnumerable<string> errors)
    {
        return new Result<T>(false, errors);
    }
}
