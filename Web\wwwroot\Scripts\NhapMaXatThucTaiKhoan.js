﻿const inputs = document.querySelectorAll('.code-input');

inputs.forEach((input, index) => {
  input.addEventListener('input', () => {
    const value = input.value;
    if (value.length === 1 && index < inputs.length - 1) {
      inputs[index + 1].focus();
    }
  });

  input.addEventListener('keydown', (e) => {
    if (e.key === 'Backspace' && !input.value && index > 0) {
      inputs[index - 1].focus();
    }
  });
});

document.getElementById('codeInputs').addEventListener('paste', function (e) {
  const paste = (e.clipboardData || window.clipboardData).getData('text');
  if (/^\d{6}$/.test(paste)) {
    e.preventDefault();
    paste.split('').forEach((char, i) => {
      if (inputs[i]) inputs[i].value = char;
    });
    inputs[5].focus();
  }
});


$('#verifyForm').on('submit', async function (e) {
  e.preventDefault();
  NTS.loadding()
  const email = sessionStorage.getItem('resetEmail');
  const code = Array.from(inputs).map(input => input.value).join('');
  const data = {
    tenDangNhap: sessionStorage.getItem('resetTenDangNhap'),
    matMa: sessionStorage.getItem('resetMatMa'),
    email: sessionStorage.getItem('resetEmail'),
    MaXacThuc: Array.from(inputs).map(input => input.value).join('')
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/SendEmaiController/update', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Xát thực tài khoản thành công");
        const domain = window.location.origin;
        window.location.href = domain + '/hethong/DangNhap';  // Thay '/dashboard' bằng URL bạn muốn
       
      }
    }
  } catch (error) {

  }

  NTS.unloadding()
});
