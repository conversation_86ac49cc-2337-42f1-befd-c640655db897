//using System.ComponentModel.DataAnnotations;

//namespace Web.WebClient.Models;

//// DTO for roles returned by the API.
//public class RoleDto
//{
//    public string Id { get; set; } = "";
//    public string? Name { get; set; }
//}

//// Request model for creating a role.
//public class CreateRoleRequest
//{
//    [Required]
//    public string Name { get; set; } = "";
//}

//// Request model for updating a role.
//public class UpdateRoleRequest
//{
//    [Required]
//    public string Name { get; set; } = "";
//}

//// Matches the permission structure from the API.
//public class Permissions
//{
//    public bool Them { get; set; }
//    public bool Sua { get; set; }
//    public bool Xoa { get; set; }
//    public bool Xem { get; set; }
//    public bool In { get; set; }
//    public bool XuatExcel { get; set; }
//    public bool Them_Deny { get; set; }
//    public bool Sua_Deny { get; set; }
//    public bool Xoa_Deny { get; set; }
//    public bool Xem_Deny { get; set; }
//    public bool In_Deny { get; set; }
//    public bool XuatExcel_Deny { get; set; }
//}

//// View model for managing role permissions.
//public class RolePermissionsViewModel
//{
//    public string RoleName { get; set; } = "";
//    public List<FeaturePermissionViewModel> Features { get; set; } = new();
//}

//public class FeaturePermissionViewModel
//{
//    public string FeatureName { get; set; } = "";
//    public Permissions Permissions { get; set; } = new();
//    public Permissions DenyPermissions { get; set; } = new();
//    public Permissions EffectivePermissions { get; set; } = new();
//}


//public class UserDto
//{
//    public string Id { get; set; } = string.Empty;
//    public string? UserName { get; set; }
//    public string? Email { get; set; }
//    public string? HoTen { get; set; }
//    public bool EmailConfirmed { get; set; }
//    public string? PhoneNumber { get; set; }
//}
//public class CreateUserRequest
//{
//    public string Email { get; set; } = string.Empty;
//    public string Password { get; set; } = string.Empty;
//}

//// Request model to update an existing user.
//public class UpdateUserRequest
//{
//    public string? UserName { get; set; }
//    public string? Email { get; set; }
//    public string? HoTen { get; set; }
//    public string? PhoneNumber { get; set; }
//    public string[]? Roles { get; set; }
//}


//// View model for the ManagePermissions page for a user.
//public class UserPermissionsViewModel
//{
//    public string UserId { get; set; } = string.Empty;
//    public List<FeaturePermissionViewModel> Features { get; set; } = new();
//}
