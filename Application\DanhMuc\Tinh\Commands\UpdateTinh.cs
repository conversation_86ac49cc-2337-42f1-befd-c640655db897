﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FluentValidation;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Tinh.Commands;
public record UpdateTinhCommand(
    string TinhID,
    string MaTinh,
    string TenTinh,
    string MoTa,
    string DangSD
    ) : IRequest<Result<TinhDto>?>;

public class UpdateCommandValidator : AbstractValidator<UpdateTinhCommand>
{
    private readonly IApplicationDbContext _context;
    public UpdateCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.TinhID)
             .NotEmpty().WithMessage("TinhID không được để trống.");
        RuleFor(cmd => cmd.MaTinh)
            .NotEmpty().WithMessage("Mã tỉnh không được để trống.");
        RuleFor(cmd => cmd.TenTinh)
            .NotEmpty().WithMessage("Tên tỉnh không được để trống.");
        RuleFor(cmd => cmd)
              .MustAsync(BeUniqueMaTinh)
              .WithMessage(cmd => $"Mã tỉnh '{cmd.MaTinh}' đã được sử dụng.");
    }


    private async Task<bool> BeUniqueMaTinh(UpdateTinhCommand cmd, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(cmd.MaTinh))
            return true;

        return !await _context.Tinh
            .AnyAsync(u => u.MaTinh == cmd.MaTinh && u.TinhID != DungChung.NormalizationGuid(cmd.TinhID), cancellationToken);
    }
}

public class UpdateTinhCommandHandler : IRequestHandler<UpdateTinhCommand, Result<TinhDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateTinhCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<TinhDto>?> Handle(UpdateTinhCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TinhID", DungChung.NormalizationGuid(request.TinhID)),
            new Microsoft.Data.SqlClient.SqlParameter("@MaTinh", request.MaTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@TenTinh", request.TenTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@DangSD", DungChung.NormalizationBoolean(request.DangSD))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Tinh_Update",
                   MapFromReader,
                   true,
                   parameters);
            return Result<TinhDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TinhDto
        {
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
        };
    }
}
