﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands;
public record UpdateChiaSeKeyCommand(
    string ChiaSeKeyID,
    string TrangThai,
    string Loai
    ) : IRequest<Result<ChiaSeKeyDto>?>;

public class UpdateChiaSeKeyCommandValidator : AbstractValidator<UpdateChiaSeKeyCommand>
{
    public UpdateChiaSeKeyCommandValidator()
    {
    }
}

public class UpdateChiaSeKeyCommandHandler : IRequestHandler<UpdateChiaSeKeyCommand, Result<ChiaSeKeyDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateChiaSeKeyCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<ChiaSeKeyDto>?> Handle(UpdateChiaSeKeyCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@ChiaSeKeyID", DungChung.NormalizationGuid(request.ChiaSeKeyID)),
            new Microsoft.Data.SqlClient.SqlParameter("@TrangThai", request.TrangThai),
             new Microsoft.Data.SqlClient.SqlParameter("@Loai", request.Loai),
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ChiaSeKey_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<ChiaSeKeyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            ChiaSeKeyID = reader.GetGuid(reader.GetOrdinal("ChiaSeKeyID")),
        };
    }
}
