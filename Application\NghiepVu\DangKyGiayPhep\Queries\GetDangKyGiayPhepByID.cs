﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Queries;
internal class GetDangKyGiayPhepByID
{
}
public record GetDangKyGiayPhepByIDQuery(
    string DangKyGiayPhepID
    ) : IRequest<Result<List<DangKyGiayPhepDto>>?>;


public class GetDangKyGiayPhepByIDQueryValidator : AbstractValidator<GetDangKyGiayPhepByIDQuery>
{
    public GetDangKyGiayPhepByIDQueryValidator()
    {
    }
}
public class GetDangKyGiayPhepByIDQueryHandler : IRequestHandler<GetDangKyGiayPhepByIDQuery, Result<List<DangKyGiayPhepDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetDangKyGiayPhepByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DangKyGiayPhepDto>>?> Handle(GetDangKyGiayPhepByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@DangKyGiayPhepID", request.DangKyGiayPhepID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyGiayPhep_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<DangKyGiayPhepDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private DangKyGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DangKyGiayPhepDto
        {
            DangKyGiayPhepID = reader.GetGuid(reader.GetOrdinal("DangKyGiayPhepID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            HoTen = reader["HoTen"] as string,
            Email = reader["Email"] as string,
            DonViCongTac = reader["DonViCongTac"] as string,
            NoiDung = reader["NoiDung"] as string,
            LoaiDangKy = reader["LoaiDangKy"] as string,
            NgayDangKy = reader["NgayDangKy"] as string,
            TrangThai = reader["TrangThai"] as string
        };
    }
}

