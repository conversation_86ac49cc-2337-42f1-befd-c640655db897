{"x-generator": "NSwag v14.1.0.0 (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "NHATTAMID2025 API", "version": "1.0.0"}, "paths": {"/api/Authentication/login": {"post": {"tags": ["Authentication"], "operationId": "<PERSON><PERSON>", "requestBody": {"x-name": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}}}}, "/api/ChiaSeKey/create": {"post": {"tags": ["ChiaSeKey"], "operationId": "CreateChiaSeKey", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChiaSeKeyCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfChiaSeKeyDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ChiaSeKey/getByKeyID/{Id}": {"get": {"tags": ["ChiaSeKey"], "operationId": "GetChiaSeKeyByKeyID", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfChiaSeKeyDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ChiaSeKey/getByID/{KeyID}/{ChiaSeKeyID}": {"get": {"tags": ["ChiaSeKey"], "operationId": "GetChiaSeKeyByID", "parameters": [{"name": "KeyID", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "ChiaSeKeyID", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfChiaSeKeyDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ChiaSeKey/delete": {"post": {"tags": ["ChiaSeKey"], "operationId": "DeleteChiaSeKey", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteChiaSeKeyCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfChiaSeKeyDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ChiaSeKey/update": {"post": {"tags": ["ChiaSeKey"], "operationId": "UpdateChiaSeKey", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChiaSeKeyCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfChiaSeKeyDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyGiayPhep/getbyuserid/{Id}": {"get": {"tags": ["DangKyGiayPhep"], "operationId": "GetDangKyGiayPhepByUserID", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDangKyGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyGiayPhep/update": {"post": {"tags": ["DangKyGiayPhep"], "operationId": "UpdateDangKyGiayPhep", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDangKyGiayPhepCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDangKyGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyGiayPhep/create": {"post": {"tags": ["DangKyGiayPhep"], "operationId": "CreateDangKyGiayPhep", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDangKyGiayPhepCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDangKyGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyGiayPhep/getall": {"get": {"tags": ["DangKyGiayPhep"], "operationId": "GetAllDangKyGiayPhep", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDangKyGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyGiayPhep/getbyid/{Id}": {"get": {"tags": ["DangKyGiayPhep"], "operationId": "GetDangKyGiayPhepById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDangKyGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KeyBanQuyen/delete": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "DeleteKey<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteKeyBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KeyBanQuyen/update": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateKeyBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KeyBanQuyen/create": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateKeyBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKeyBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KeyBanQuyen/getall": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetAllKeyB<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfKeyBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KeyBanQuyen/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetKeyBanQuyenById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfKeyBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/KichHoatKey/create": {"post": {"tags": ["KichHoatKey"], "operationId": "CreateKichHoatKey", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateKichHoatKeyCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfKichHoatKeyDto"}}}}}}}, "/api/KichHoatKey/getbyid/{Id}/{ten}": {"get": {"tags": ["KichHoatKey"], "operationId": "GetKichHoatKeyById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}, {"name": "ten", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfKichHoatKeyDto"}}}}}}}, "/api/KichHoatKey/getbyemail/{Id}": {"get": {"tags": ["KichHoatKey"], "operationId": "GetKichHoatKeyByEmail", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfKichHoatKeyDto"}}}}}}}, "/api/KiemTraThuHoiKey/KiemTra/{Id}": {"get": {"tags": ["KiemTraThuHoi<PERSON>"], "operationId": "KiemTraThuHoiKeyID", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfChiaSeKeyDto"}}}}}}}, "/api/LienKetTaiKhoan/create": {"post": {"tags": ["LienKetTaiKhoan"], "operationId": "CreateLienKetTaiKhoan", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLienKetTaiKhoanCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfLienKetTaiKhoanDto"}}}}}}}, "/api/LienKetTaiKhoan/SendEmailLienKetTaiKhoan": {"post": {"tags": ["LienKetTaiKhoan"], "operationId": "SendEmailLienKetTaiKhoan", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailLienKetTaiKhoanCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/LienKetTaiKhoan/getbyid/{Id}": {"get": {"tags": ["LienKetTaiKhoan"], "operationId": "GetLienKetTaiKhoanByUserID", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfLienKetTaiKhoanDto"}}}}}}}, "/api/ThongTinGiayPhep/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON>hep"], "operationId": "GetThongTinGiayPhepById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfThongTinGiayPhepDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ThongTinNguoiDung/update": {"post": {"tags": ["Thong<PERSON><PERSON><PERSON>gu<PERSON>"], "operationId": "UpdateThongTinNguoiDung", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateThongTinNguoiDungCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfThongTinNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ThongTinNguoiDung/getbyid/{Id}": {"get": {"tags": ["Thong<PERSON><PERSON><PERSON>gu<PERSON>"], "operationId": "GetThongTinNguoiDungById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfThongTinNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DangKyUsersController": {"post": {"tags": ["DangKyUsersController"], "operationId": "CreateDangKyUsers", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDangKyUsersCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfUsersDangKyDto"}}}}}}}, "/api/PhanQuyen/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetPhanQuyenById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfPhanQuyenDto"}}}}}}}, "/api/QuyenMatKhauController": {"post": {"tags": ["QuyenMatKhauController"], "operationId": "QuenMatKhau", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateQuenMatKhauCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/SendEmaiController": {"post": {"tags": ["SendEmaiController"], "operationId": "SendEmai", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSendEmaiCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/SendEmaiController/SendEmailXatThuc": {"post": {"tags": ["SendEmaiController"], "operationId": "SendEmailXatThuc", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailXatThucCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/SendEmaiController/update": {"post": {"tags": ["SendEmaiController"], "operationId": "UpdateEmai", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUpdateEmailCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfUsersDangKyDto"}}}}}}}, "/api/TaoMatKhauMoiController": {"post": {"tags": ["TaoMatKhauMoiController"], "operationId": "TaoMatKhauMoi", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaoMatKhauMoiCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfUsersDangKyDto"}}}}}}}, "/api/XatThucQuenMatKhauController": {"post": {"tags": ["XatThucQuenMatKhauController"], "operationId": "XatThucQuenMatKhau", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XatThucQuenMatKhauCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}}}, "/api/ChucVuController": {"post": {"tags": ["ChucVuController"], "operationId": "CreateChucVu", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateChucVuCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}, "security": [{"JWT": []}]}, "get": {"tags": ["ChucVuController"], "operationId": "GetAllChucVu", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ChucVuDto"}}}}}}, "security": [{"JWT": []}]}}, "/api/ChucVuController/{id}": {"put": {"tags": ["ChucVuController"], "operationId": "UpdateChucVu", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateChucVuCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}, "security": [{"JWT": []}]}, "delete": {"tags": ["ChucVuController"], "operationId": "DeleteChucVu", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}, "security": [{"JWT": []}]}, "get": {"tags": ["ChucVuController"], "operationId": "GetChucVuById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChucVuDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/getall": {"get": {"tags": ["DonVi"], "operationId": "GetAllDonVi", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/getbytinhid/{TinhID}": {"get": {"tags": ["DonVi"], "operationId": "GetAllDonViByTinhID", "parameters": [{"name": "TinhID", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/getbymatinh/{MaTinh}": {"get": {"tags": ["DonVi"], "operationId": "GetAllDonViByMaTinh", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/getbyxaid/{XaID}": {"get": {"tags": ["DonVi"], "operationId": "GetAllDonViByXaID", "parameters": [{"name": "XaID", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/getbymaxa/{MaXa}": {"get": {"tags": ["DonVi"], "operationId": "GetAllDonViByMaXa", "parameters": [{"name": "MaXa", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/create": {"post": {"tags": ["DonVi"], "operationId": "CreateDonVi", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDonViCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/update": {"post": {"tags": ["DonVi"], "operationId": "UpdateDonVi", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDonViCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonVi/delete": {"post": {"tags": ["DonVi"], "operationId": "DeleteDonVi", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDonViCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfDonViDto"}}}}}, "security": [{"JWT": []}]}}, "/api/DonViTinhController": {"post": {"tags": ["DonViTinhController"], "operationId": "CreateDonViTinh", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDonViTinhCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DonViTinhDto"}}}}}, "security": [{"JWT": []}]}, "get": {"tags": ["DonViTinhController"], "operationId": "GetAllDonViTinh", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DonViTinhDto"}}}}}}, "security": [{"JWT": []}]}}, "/api/GoiBanQuyen/delete": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "DeleteGoi<PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteGoiBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfGoiBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/GoiBanQuyen/update": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateGoiBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfGoiBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/GoiBanQuyen/create": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateGoiBanQuyenCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfGoiBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/GoiBanQuyen/getall": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetAllGoi<PERSON><PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfGoiBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/GoiBanQuyen/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetGoiBanQuyenById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfGoiBanQuyenDto"}}}}}, "security": [{"JWT": []}]}}, "/api/LinhVuc/getall": {"get": {"tags": ["LinhVuc"], "operationId": "GetAllLinhVuc", "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfLinhVucDto"}}}}}, "security": [{"JWT": []}]}}, "/api/LoaiNguoiDung/getall": {"get": {"tags": ["LoaiNguoiDung"], "operationId": "GetAllLoaiNguoiDung", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfLoaiNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NguoiDung/delete": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "DeleteNguoiDung", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteNguoiDungCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NguoiDung/update": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "UpdateNguoiDung", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNguoiDungCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NguoiDung/create": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "CreateNguoiDung", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNguoiDungCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NguoiDung/getall": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetAllNguoiDung", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NguoiDung/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetNguoiDungById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfNguoiDungDto"}}}}}, "security": [{"JWT": []}]}}, "/api/NhanViens": {"post": {"tags": ["NhanViens"], "operationId": "Create", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateNhanVienCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string", "format": "guid"}}}}}, "security": [{"JWT": []}]}, "get": {"tags": ["NhanViens"], "operationId": "GetAll", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NhanVienDto"}}}}}}, "security": [{"JWT": []}]}}, "/api/NhanViens/{id}": {"put": {"tags": ["NhanViens"], "operationId": "Update", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateNhanVienCommand"}}}, "required": true, "x-position": 2}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Unit"}}}}}, "security": [{"JWT": []}]}, "delete": {"tags": ["NhanViens"], "operationId": "Delete", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfObject"}}}}}, "security": [{"JWT": []}]}, "get": {"tags": ["NhanViens"], "operationId": "GetById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NhanVienDto"}}}}}, "security": [{"JWT": []}]}}, "/api/PhanMem/getall": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetAllPhanMem", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/PhanMem/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "operationId": "GetPhanMemById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ThanhPhanHoSoTTHC/getall": {"get": {"tags": ["ThanhPhanHoSoTTHC"], "operationId": "GetAllThanhPhanHoSoTTHC", "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 2}, {"name": "maThuTucHanhChinh", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfThanhPhanHoSoTTHCDto"}}}}}, "security": [{"JWT": []}]}}, "/api/ThuTucHanhChinh/getall": {"get": {"tags": ["ThuTucHanhChinh"], "operationId": "GetAllThuTucHanhChinh", "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 2}, {"name": "maLinhVuc", "in": "query", "schema": {"type": "string", "nullable": true}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedListOfThuTucHanhChinhDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Tinh/getall": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "GetAllTinh", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfTinhDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Tinh/getbyid/{Id}": {"get": {"tags": ["<PERSON><PERSON>"], "operationId": "GetTinhById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfTinhDto"}}}}}, "security": [{"JWT": []}]}}, "/api/TrienKhaiPhanMem/delete": {"post": {"tags": ["TrienKhaiPhan<PERSON><PERSON>"], "operationId": "DeleteTrienKhaiPhanMem", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteTrienKhaiPhanMemCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfTrienKhaiPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/TrienKhaiPhanMem/update": {"post": {"tags": ["TrienKhaiPhan<PERSON><PERSON>"], "operationId": "UpdateTrienKhaiPhanMem", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTrienKhaiPhanMemCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfTrienKhaiPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/TrienKhaiPhanMem/create": {"post": {"tags": ["TrienKhaiPhan<PERSON><PERSON>"], "operationId": "Create<PERSON><PERSON><PERSON>haiPhan<PERSON>em", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTrienKhaiPhanMemCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfTrienKhaiPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/TrienKhaiPhanMem/getall": {"get": {"tags": ["TrienKhaiPhan<PERSON><PERSON>"], "operationId": "GetAllTrienKhaiPhanMem", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfTrienKhaiPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/TrienKhaiPhanMem/getbyid/{Id}": {"get": {"tags": ["TrienKhaiPhan<PERSON><PERSON>"], "operationId": "GetTrienKhaiPhanMemById", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfTrienKhaiPhanMemDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/getall": {"get": {"tags": ["Xa"], "operationId": "GetAllXa", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/getbyid/{Id}": {"get": {"tags": ["Xa"], "operationId": "GetAllXaByID", "parameters": [{"name": "Id", "x-originalName": "id", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/getbymaxa/{MaXa}": {"get": {"tags": ["Xa"], "operationId": "GetAllXaByMa", "parameters": [{"name": "MaXa", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/getbymatinh/{MaTinh}": {"get": {"tags": ["Xa"], "operationId": "GetAllXaByMaTinh", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/getbytinhid/{TinhID}": {"get": {"tags": ["Xa"], "operationId": "GetAllXaByTinhID", "parameters": [{"name": "TinhID", "in": "path", "required": true, "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfListOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/create": {"post": {"tags": ["Xa"], "operationId": "CreateXa", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateXaCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/update": {"post": {"tags": ["Xa"], "operationId": "UpdateXa", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateXaCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/api/Xa/delete": {"post": {"tags": ["Xa"], "operationId": "DeleteXa", "requestBody": {"x-name": "command", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteXaCommand"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResultOfXaDto"}}}}}, "security": [{"JWT": []}]}}, "/hethong/dangnhap": {"get": {"tags": ["Dang<PERSON>hap"], "operationId": "DangNhap_Index", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/hethong/dangnhap/login": {"post": {"tags": ["Dang<PERSON>hap"], "operationId": "DangNhap_Login", "requestBody": {"x-name": "data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}}, "components": {"schemas": {"TokenResponse": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string"}, "tokenType": {"type": "string"}, "accessToken": {"type": "string"}, "expiresIn": {"type": "integer", "format": "int32"}, "refreshToken": {"type": "string"}, "errorMessage": {"type": "string", "nullable": true}}}, "LoginCommand": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string"}, "matKhau": {"type": "string"}}}, "ResultOfChiaSeKeyDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ChiaSeKeyDto"}]}}}, "ChiaSeKeyDto": {"type": "object", "additionalProperties": false, "properties": {"chiaSeKeyID": {"type": "string", "format": "guid"}, "keyID": {"type": "string", "format": "guid", "nullable": true}, "hoTen": {"type": "string", "nullable": true}, "tenDonViCongTac": {"type": "string", "nullable": true}, "donViCongTacID": {"type": "string", "format": "guid", "nullable": true}, "email": {"type": "string", "nullable": true}, "linkKichHoat": {"type": "string", "nullable": true}, "trangThai": {"type": "string", "nullable": true}, "khoaKey": {"type": "string", "nullable": true}}}, "CreateChiaSeKeyCommand": {"type": "object", "additionalProperties": false, "properties": {"chiaSeKeyID": {"type": "string"}, "keyID": {"type": "string"}, "hoTen": {"type": "string"}, "tenDonViCongTac": {"type": "string"}, "donViCongTacID": {"type": "string"}, "email": {"type": "string"}, "linkKichHoat": {"type": "string"}, "trangThai": {"type": "string"}}}, "ResultOfListOfChiaSeKeyDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ChiaSeKeyDto"}}}}, "DeleteChiaSeKeyCommand": {"type": "object", "additionalProperties": false, "properties": {"chiaSeKeyID": {"type": "string"}}}, "UpdateChiaSeKeyCommand": {"type": "object", "additionalProperties": false, "properties": {"chiaSeKeyID": {"type": "string"}, "trangThai": {"type": "string"}, "loai": {"type": "string"}}}, "ResultOfListOfDangKyGiayPhepDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/DangKyGiayPhepDto"}}}}, "DangKyGiayPhepDto": {"type": "object", "additionalProperties": false, "properties": {"dangKyGiayPhepID": {"type": "string", "format": "guid"}, "phanMemID": {"type": "string", "format": "guid", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "userID": {"type": "string", "format": "guid", "nullable": true}, "keyID": {"type": "string", "format": "guid", "nullable": true}, "hoTen": {"type": "string", "nullable": true}, "tenTaiKhoan": {"type": "string", "nullable": true}, "tenGoi": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "donViCongTac": {"type": "string", "nullable": true}, "noiDung": {"type": "string", "nullable": true}, "loaiDangKy": {"type": "string", "nullable": true}, "trangThai": {"type": "string", "nullable": true}, "ngayDangKy": {"type": "string", "nullable": true}, "noiDungLienHe": {"type": "string", "nullable": true}, "ngayLienHe": {"type": "string", "nullable": true}}}, "ResultOfDangKyGiayPhepDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/DangKyGiayPhepDto"}]}}}, "UpdateDangKyGiayPhepCommand": {"type": "object", "additionalProperties": false, "properties": {"dangKyGiayPhepID": {"type": "string"}, "trangThai": {"type": "string"}, "noiDungLienHe": {"type": "string"}}}, "CreateDangKyGiayPhepCommand": {"type": "object", "additionalProperties": false, "properties": {"dangKyGiayPhepID": {"type": "string"}, "phanMemID": {"type": "string"}, "userID": {"type": "string"}, "noiDung": {"type": "string"}, "loaiDangKy": {"type": "string"}, "keyID": {"type": "string"}}}, "ResultOfKeyBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/KeyBanQuyenDto"}]}}}, "KeyBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"keyID": {"type": "string", "format": "guid"}, "maKey": {"type": "string", "nullable": true}, "trienKhaiPhanMemID": {"type": "string", "nullable": true}, "goiBanQuyenID": {"type": "string", "format": "guid", "nullable": true}, "ngayTao": {"type": "string", "nullable": true}, "ngayHetHan": {"type": "string", "nullable": true}, "soLanKichHoat": {"type": "integer", "format": "int32", "nullable": true}, "khoa": {"type": "boolean", "nullable": true}, "trangThai": {"type": "string", "nullable": true}, "userID": {"type": "string", "format": "guid"}, "tenUser": {"type": "string", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "tenGoi": {"type": "string", "nullable": true}}}, "DeleteKeyBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"keyID": {"type": "string"}}}, "UpdateKeyBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"keyID": {"type": "string"}, "maKey": {"type": "string"}, "trienKhaiPhanMemID": {"type": "string"}, "goiBanQuyenID": {"type": "string"}, "ngayTao": {"type": "string"}, "ngayHetHan": {"type": "string"}, "soLanKichHoat": {"type": "string"}, "userID": {"type": "string"}, "khoa": {"type": "boolean"}}}, "CreateKeyBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"keyID": {"type": "string"}, "maKey": {"type": "string"}, "trienKhaiPhanMemID": {"type": "string"}, "goiBanQuyenID": {"type": "string"}, "ngayTao": {"type": "string"}, "ngayHetHan": {"type": "string"}, "soLanKichHoat": {"type": "string"}, "userID": {"type": "string"}, "khoa": {"type": "boolean"}}}, "ResultOfListOfKeyBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/KeyBanQuyenDto"}}}}, "ResultOfKichHoatKeyDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/KichHoatKeyDto"}]}}}, "KichHoatKeyDto": {"type": "object", "additionalProperties": false, "properties": {"kichHoatID": {"type": "string", "format": "guid"}, "keyID": {"type": "string", "format": "guid", "nullable": true}, "userID": {"type": "string", "format": "guid", "nullable": true}, "tenThietBi": {"type": "string", "nullable": true}, "ipKichHoat": {"type": "string", "nullable": true}, "ngayKichHoat": {"type": "string", "format": "date-time", "nullable": true}, "heDieuHanh": {"type": "string", "nullable": true}, "trangThai": {"type": "string", "nullable": true}, "ngayhetHan": {"type": "string", "format": "date-time", "nullable": true}, "nguoiKichHoat": {"type": "string", "nullable": true}, "emailKichHoat": {"type": "string", "nullable": true}, "donViKichHoat": {"type": "string", "nullable": true}, "link": {"type": "string", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}}}, "CreateKichHoatKeyCommand": {"type": "object", "additionalProperties": false, "properties": {"code": {"type": "string"}, "maKey": {"type": "string"}, "link": {"type": "string"}, "email": {"type": "string"}, "hoTenKichHoat": {"type": "string"}, "donViCongTac": {"type": "string"}}}, "ResultOfListOfKichHoatKeyDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/KichHoatKeyDto"}}}}, "ResultOfLienKetTaiKhoanDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/LienKetTaiKhoanDto"}]}}}, "LienKetTaiKhoanDto": {"type": "object", "additionalProperties": false, "properties": {"lienKetTaiKhoanID": {"type": "string", "format": "guid"}, "userIDNtsoft": {"type": "string", "format": "guid", "nullable": true}, "userIDLienKet": {"type": "string", "nullable": true}, "tenDNLienKet": {"type": "string", "nullable": true}, "ngayLienKet": {"type": "string", "nullable": true}, "trienKhaiPhanMemID": {"type": "string", "format": "guid", "nullable": true}, "tenThietBiLienKet": {"type": "string", "nullable": true}, "ipLienKet": {"type": "string", "nullable": true}, "heDieuHanh": {"type": "string", "nullable": true}, "link": {"type": "string", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}}}, "CreateLienKetTaiKhoanCommand": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string"}, "userIDLienKet": {"type": "string"}, "tenDNLienKet": {"type": "string"}, "link": {"type": "string"}, "code": {"type": "string"}}}, "ResultOfObject": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true}}}, "SendEmailLienKetTaiKhoanCommand": {"type": "object", "additionalProperties": false, "properties": {"userIDLienKet": {"type": "string"}, "email": {"type": "string"}, "link": {"type": "string"}}}, "ResultOfListOfLienKetTaiKhoanDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/LienKetTaiKhoanDto"}}}}, "ResultOfListOfThongTinGiayPhepDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ThongTinGiayPhepDto"}}}}, "ThongTinGiayPhepDto": {"type": "object", "additionalProperties": false, "properties": {"keyID": {"type": "string", "format": "guid"}, "tenGoiBanQuyen": {"type": "string", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "ngayTao": {"type": "string", "nullable": true}, "ngayHetHan": {"type": "string", "nullable": true}, "soLanKichHoat": {"type": "integer", "format": "int32", "nullable": true}, "soThietBiToiDa": {"type": "integer", "format": "int32", "nullable": true}, "trangThai": {"type": "string", "nullable": true}, "maKey": {"type": "string", "nullable": true}, "soNgayConLai": {"type": "string", "nullable": true}, "giaHan": {"type": "string", "nullable": true}, "chiaSeBoi": {"type": "string", "nullable": true}, "loai": {"type": "string", "nullable": true}}}, "ResultOfThongTinNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ThongTinNguoiDungDto"}]}}}, "ThongTinNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string", "format": "guid"}, "tenDangNhap": {"type": "string", "nullable": true}, "hoVaTen": {"type": "string", "nullable": true}, "ngaySinh": {"type": "string", "nullable": true}, "gioiTinh": {"type": "string", "nullable": true}, "soDienThoai": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "diaChi": {"type": "string", "nullable": true}, "donViCongTac": {"type": "string", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "donViCongTacID": {"type": "string", "format": "guid", "nullable": true}}}, "UpdateThongTinNguoiDungCommand": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string"}, "avatar": {"type": "string"}, "hoVaTen": {"type": "string"}, "gioiTinh": {"type": "string"}, "ngaySinh": {"type": "string"}, "soDienThoai": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "donViCongTac": {"type": "string"}, "carNoiDung": {"type": "string"}, "donViCongTacID": {"type": "string"}}}, "ResultOfListOfThongTinNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/ThongTinNguoiDungDto"}}}}, "ResultOfUsersDangKyDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/UsersDangKyDto"}]}}}, "UsersDangKyDto": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string", "format": "guid"}, "tenDangNhap": {"type": "string", "nullable": true}, "matMa": {"type": "string", "nullable": true}, "nhapLaiMatMa": {"type": "string", "nullable": true}, "hoVaTen": {"type": "string", "nullable": true}, "gioiTinh": {"type": "string", "nullable": true}, "soDienThoai": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "diaChi": {"type": "string", "nullable": true}, "donViCongTac": {"type": "string", "nullable": true}}}, "CreateDangKyUsersCommand": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string"}, "matMa": {"type": "string"}, "nhapLaiMatMa": {"type": "string"}, "hoVaTen": {"type": "string"}, "gioiTinh": {"type": "string"}, "soDienThoai": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "donViCongTac": {"type": "string"}, "maXacThuc": {"type": "string"}}}, "ResultOfListOfPhanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Phan<PERSON>uyenDto"}}}}, "PhanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"phanQuyenID": {"type": "string", "format": "guid"}, "sapXep": {"type": "string", "nullable": true}, "tenChucNang": {"type": "string", "nullable": true}, "duongDan": {"type": "string", "nullable": true}, "urlTruyCap": {"type": "string", "nullable": true}, "nhomTaiKhoan": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}}}, "CreateQuenMatKhauCommand": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string"}}}, "CreateSendEmaiCommand": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string"}, "matMa": {"type": "string"}, "nhapLaiMatMa": {"type": "string"}, "hoVaTen": {"type": "string"}, "gioiTinh": {"type": "string"}, "soDienThoai": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "donViCongTac": {"type": "string"}}}, "SendEmailXatThucCommand": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string"}, "matMa": {"type": "string"}, "email": {"type": "string"}}}, "CreateUpdateEmailCommand": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string"}, "matMa": {"type": "string"}, "email": {"type": "string"}, "maXacThuc": {"type": "string"}}}, "TaoMatKhauMoiCommand": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string"}, "matMaMoi": {"type": "string"}, "nhapLaiMatMaMoi": {"type": "string"}, "maXatNhan": {"type": "string"}}}, "XatThucQuenMatKhauCommand": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string"}, "maXacThuc": {"type": "string"}}}, "CreateChucVuCommand": {"type": "object", "additionalProperties": false, "properties": {"maChucVu": {"type": "string"}, "tenChucVu": {"type": "string"}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean"}}}, "UpdateChucVuCommand": {"type": "object", "additionalProperties": false, "properties": {"chucVuID": {"type": "string", "format": "guid"}, "maChucVu": {"type": "string"}, "tenChucVu": {"type": "string"}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean"}}}, "ChucVuDto": {"type": "object", "additionalProperties": false, "properties": {"chucVuID": {"type": "string", "format": "guid"}, "maChucVu": {"type": "string", "nullable": true}, "tenChucVu": {"type": "string", "nullable": true}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean", "nullable": true}}}, "ResultOfListOfDonViDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/DonViDto"}}}}, "DonViDto": {"type": "object", "additionalProperties": false, "properties": {"donViID": {"type": "string", "format": "guid"}, "maDonVi": {"type": "string", "nullable": true}, "maQHNS": {"type": "string", "nullable": true}, "tenDonVi": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "diaChi": {"type": "string", "nullable": true}, "dienThoai": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "maSoThue": {"type": "string", "nullable": true}, "tinhID": {"type": "string", "format": "guid", "nullable": true}, "tenTinh": {"type": "string", "nullable": true}, "maTinh": {"type": "string", "nullable": true}, "xaID": {"type": "string", "format": "guid", "nullable": true}, "tenXa": {"type": "string", "nullable": true}, "maXa": {"type": "string", "nullable": true}, "donViID_Cha": {"type": "string", "format": "guid", "nullable": true}, "maDonViCha": {"type": "string", "nullable": true}, "tenDonVi_Cha": {"type": "string", "nullable": true}}}, "ResultOfDonViDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/DonViDto"}]}}}, "CreateDonViCommand": {"type": "object", "additionalProperties": false, "properties": {"maDonVi": {"type": "string"}, "maQHNS": {"type": "string"}, "tenDonVi": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "dienThoai": {"type": "string"}, "website": {"type": "string"}, "maSoThue": {"type": "string"}, "donViID_Cha": {"type": "string"}, "tinhID": {"type": "string"}, "xaID": {"type": "string"}, "dangSD": {"type": "string"}}}, "UpdateDonViCommand": {"type": "object", "additionalProperties": false, "properties": {"donViID": {"type": "string"}, "maDonVi": {"type": "string"}, "maQHNS": {"type": "string"}, "tenDonVi": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "dienThoai": {"type": "string"}, "website": {"type": "string"}, "maSoThue": {"type": "string"}, "donViID_Cha": {"type": "string"}, "tinhID": {"type": "string"}, "xaID": {"type": "string"}, "dangSD": {"type": "string"}}}, "DeleteDonViCommand": {"type": "object", "additionalProperties": false, "properties": {"donViID": {"type": "string"}}}, "DonViTinhDto": {"type": "object", "additionalProperties": false, "properties": {"donViTinhID": {"type": "string", "format": "guid"}, "tenDonViTinh": {"type": "string", "nullable": true}, "maDonViTinh": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean", "nullable": true}}}, "CreateDonViTinhCommand": {"type": "object", "additionalProperties": false, "properties": {"maDonViTinh": {"type": "string"}, "tenDonViTinh": {"type": "string"}, "ngungTD": {"type": "boolean"}}}, "ResultOfGoiBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/GoiBanQuyenDto"}]}}}, "GoiBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"goiBanQuyenID": {"type": "string", "format": "guid"}, "maGoi": {"type": "string", "nullable": true}, "tenGoi": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "thoiHanNgay": {"type": "integer", "format": "int32", "nullable": true}, "soThietBiToiDa": {"type": "integer", "format": "int32", "nullable": true}, "loaiNguoiDungID": {"type": "string", "format": "guid", "nullable": true}, "tenLoaiNguoiDung": {"type": "string", "nullable": true}, "giaTien": {"type": "number", "format": "decimal", "nullable": true}, "kichHoat": {"type": "boolean", "nullable": true}}}, "DeleteGoiBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"goiBanQuyenID": {"type": "string"}}}, "UpdateGoiBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"goiBanQuyenID": {"type": "string"}, "maGoi": {"type": "string"}, "tenGoi": {"type": "string"}, "moTa": {"type": "string"}, "thoiHanNgay": {"type": "string"}, "soThietBiToiDa": {"type": "string"}, "loaiNguoiDungID": {"type": "string"}, "giaTien": {"type": "string"}}}, "CreateGoiBanQuyenCommand": {"type": "object", "additionalProperties": false, "properties": {"goiBanQuyenID": {"type": "string"}, "maGoi": {"type": "string"}, "tenGoi": {"type": "string"}, "moTa": {"type": "string"}, "thoiHanNgay": {"type": "string"}, "soThietBiToiDa": {"type": "string"}, "loaiNguoiDungID": {"type": "string"}, "giaTien": {"type": "string"}}}, "ResultOfListOfGoiBanQuyenDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/GoiBanQuyenDto"}}}}, "PaginatedListOfLinhVucDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/LinhVucDto"}}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}}, "LinhVucDto": {"type": "object", "additionalProperties": false, "properties": {"maLinhVuc": {"type": "string", "nullable": true}, "tenLinhVuc": {"type": "string", "nullable": true}, "maNganh": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}}}, "ResultOfListOfLoaiNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/LoaiNguoiDungDto"}}}}, "LoaiNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"loaiNguoiDungID": {"type": "string", "format": "guid"}, "maLoaiNguoiDung": {"type": "string", "nullable": true}, "tenLoaiNguoiDung": {"type": "string", "nullable": true}}}, "ResultOfNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/NguoiDungDto"}]}}}, "NguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string", "format": "guid"}, "tenDangNhap": {"type": "string", "nullable": true}, "matMa": {"type": "string", "nullable": true}, "nhapLaiMatMa": {"type": "string", "nullable": true}, "maXacNhan": {"type": "string", "nullable": true}, "userGroupCode": {"type": "string", "nullable": true}, "userGroupID": {"type": "string", "format": "guid", "nullable": true}, "donViID": {"type": "string", "format": "guid", "nullable": true}, "ngayDangNhap": {"type": "string", "format": "date-time", "nullable": true}, "ngayThaoTac": {"type": "string", "format": "date-time", "nullable": true}, "avatar": {"type": "string", "nullable": true}, "hoVaTen": {"type": "string", "nullable": true}, "ngaySinh": {"type": "string", "format": "date-time", "nullable": true}, "gioiTinh": {"type": "string", "nullable": true}, "soDienThoai": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "diaChi": {"type": "string", "nullable": true}, "donViCongTac": {"type": "string", "nullable": true}, "dangSD": {"type": "boolean", "nullable": true}, "loaiUsers": {"type": "string", "nullable": true}, "xatThucEmail": {"type": "boolean", "nullable": true}}}, "DeleteNguoiDungCommand": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string"}}}, "UpdateNguoiDungCommand": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string"}, "dangSD": {"type": "boolean"}}}, "CreateNguoiDungCommand": {"type": "object", "additionalProperties": false, "properties": {"userID": {"type": "string"}, "tenDangNhap": {"type": "string"}, "matMa": {"type": "string"}, "nhapLaiMatMa": {"type": "string"}, "avatar": {"type": "string"}, "hoVaTen": {"type": "string"}, "ngaySinh": {"type": "string"}, "gioiTinh": {"type": "string"}, "soDienThoai": {"type": "string"}, "email": {"type": "string"}, "diaChi": {"type": "string"}, "donViCongTac": {"type": "string"}, "dangSD": {"type": "boolean"}, "loaiUsers": {"type": "string"}}}, "ResultOfListOfNguoiDungDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/NguoiDungDto"}}}}, "CreateNhanVienCommand": {"type": "object", "additionalProperties": false, "properties": {"maNhanVien": {"type": "string", "nullable": true}, "tenNhanVien": {"type": "string", "nullable": true}, "chucVu": {"type": "string", "nullable": true}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean", "nullable": true}}}, "Unit": {"type": "object", "description": "Represents a void type, since Void is not a valid return type in C#.", "additionalProperties": false}, "UpdateNhanVienCommand": {"type": "object", "additionalProperties": false, "properties": {"nhanVienID": {"type": "string", "format": "guid"}, "maNhanVien": {"type": "string", "nullable": true}, "tenNhanVien": {"type": "string", "nullable": true}, "chucVu": {"type": "string", "nullable": true}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean", "nullable": true}}}, "NhanVienDto": {"type": "object", "additionalProperties": false, "properties": {"nhanVienID": {"type": "string", "format": "guid"}, "maNhanVien": {"type": "string", "nullable": true}, "tenNhanVien": {"type": "string", "nullable": true}, "chucVu": {"type": "string", "nullable": true}, "dienGiai": {"type": "string", "nullable": true}, "ngungTD": {"type": "boolean", "nullable": true}}}, "ResultOfListOfPhanMemDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/PhanMemDto"}}}}, "PhanMemDto": {"type": "object", "additionalProperties": false, "properties": {"phanMemID": {"type": "string", "format": "guid"}, "tenPhanMem": {"type": "string", "nullable": true}, "maPhanMem": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "dangSD": {"type": "boolean", "nullable": true}, "trangThai": {"type": "string", "nullable": true}}}, "PaginatedListOfThanhPhanHoSoTTHCDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ThanhPhanHoSoTTHCDto"}}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}}, "ThanhPhanHoSoTTHCDto": {"type": "object", "additionalProperties": false, "properties": {"thanhPhanHoSoTTHCID": {"type": "string", "format": "guid"}, "thuTucHanhChinhID": {"type": "string", "format": "guid"}, "tenThanhPhanHoSoTTHC": {"type": "string", "nullable": true}, "tenTepDinhKem": {"type": "string", "nullable": true}, "duongDanTepDinhKem": {"type": "string", "nullable": true}, "soBanChinh": {"type": "string", "nullable": true}, "soBanSao": {"type": "string", "nullable": true}, "ghiChu": {"type": "string", "nullable": true}}}, "PaginatedListOfThuTucHanhChinhDto": {"type": "object", "additionalProperties": false, "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ThuTucHanhChinhDto"}}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}}}, "ThuTucHanhChinhDto": {"type": "object", "additionalProperties": false, "properties": {"thuTucHanhChinhID": {"type": "string", "format": "guid"}, "maThuTucHanhChinh": {"type": "string", "nullable": true}, "tenThuTucHanhChinh": {"type": "string", "nullable": true}, "maCapHanhChinh": {"type": "string", "nullable": true}, "loaiThuTucHanhChinh": {"type": "integer", "format": "int32"}, "maLinhVuc": {"type": "string", "nullable": true}, "trinhTuThucHien": {"type": "string", "nullable": true}, "cachThucHien": {"type": "string", "nullable": true}, "doiTuongThucHien": {"type": "string", "nullable": true}, "diaChiTiepNhan": {"type": "string", "nullable": true}, "yeuCau": {"type": "string", "nullable": true}, "ketQuaThucHien": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "canCuPhapLy": {"type": "string", "nullable": true}, "vanBanID": {"type": "string", "format": "guid", "nullable": true}}}, "ResultOfListOfTinhDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/TinhDto"}}}}, "TinhDto": {"type": "object", "additionalProperties": false, "properties": {"tinhID": {"type": "string", "format": "guid"}, "tenTinh": {"type": "string", "nullable": true}, "maTinh": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "dangSD": {"type": "boolean", "nullable": true}}}, "ResultOfTrienKhaiPhanMemDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/TrienKhaiPhanMemDto"}]}}}, "TrienKhaiPhanMemDto": {"type": "object", "additionalProperties": false, "properties": {"trienKhaiPhanMemID": {"type": "string", "format": "guid"}, "phanMemID": {"type": "string", "format": "guid", "nullable": true}, "tinhID": {"type": "string", "format": "guid", "nullable": true}, "tenPhanMem": {"type": "string", "nullable": true}, "tenTinh": {"type": "string", "nullable": true}, "urlTruyCap": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "ngayTrienKhai": {"type": "string", "nullable": true}, "trangThai": {"type": "string", "nullable": true}}}, "DeleteTrienKhaiPhanMemCommand": {"type": "object", "additionalProperties": false, "properties": {"trienKhaiPhanMemID": {"type": "string"}}}, "UpdateTrienKhaiPhanMemCommand": {"type": "object", "additionalProperties": false, "properties": {"trienKhaiPhanMemID": {"type": "string"}, "phanMemID": {"type": "string"}, "tinhID": {"type": "string"}, "urlTruyCap": {"type": "string"}, "trangThai": {"type": "string"}, "moTa": {"type": "string"}, "ngayTrienKhai": {"type": "string"}}}, "CreateTrienKhaiPhanMemCommand": {"type": "object", "additionalProperties": false, "properties": {"phanMemID": {"type": "string"}, "tinhID": {"type": "string"}, "urlTruyCap": {"type": "string"}, "trangThai": {"type": "string"}, "moTa": {"type": "string"}, "ngayTrienKhai": {"type": "string"}}}, "ResultOfListOfTrienKhaiPhanMemDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/TrienKhaiPhanMemDto"}}}}, "ResultOfListOfXaDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/XaDto"}}}}, "XaDto": {"type": "object", "additionalProperties": false, "properties": {"xaID": {"type": "string", "format": "guid"}, "tinhID": {"type": "string", "format": "guid", "nullable": true}, "tenTinh": {"type": "string", "nullable": true}, "maTinh": {"type": "string", "nullable": true}, "maXa": {"type": "string", "nullable": true}, "tenXa": {"type": "string", "nullable": true}, "moTa": {"type": "string", "nullable": true}, "dangSD": {"type": "boolean", "nullable": true}}}, "ResultOfXaDto": {"type": "object", "additionalProperties": false, "properties": {"succeeded": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "result": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/XaDto"}]}}}, "CreateXaCommand": {"type": "object", "additionalProperties": false, "properties": {"tinhID": {"type": "string"}, "maXa": {"type": "string"}, "tenXa": {"type": "string"}, "moTa": {"type": "string"}, "dangSD": {"type": "string"}}}, "UpdateXaCommand": {"type": "object", "additionalProperties": false, "properties": {"xaID": {"type": "string"}, "tinhID": {"type": "string"}, "maXa": {"type": "string"}, "tenXa": {"type": "string"}, "moTa": {"type": "string"}, "dangSD": {"type": "string"}}}, "DeleteXaCommand": {"type": "object", "additionalProperties": false, "properties": {"xaID": {"type": "string"}}}, "LoginRequest": {"type": "object", "additionalProperties": false, "properties": {"tenDangNhap": {"type": "string", "nullable": true}, "matKhau": {"type": "string", "nullable": true}}}}, "securitySchemes": {"JWT": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Type into the textbox: Bearer {your JWT token}.", "name": "Authorization", "in": "header"}}}, "security": [{"JWT": []}]}