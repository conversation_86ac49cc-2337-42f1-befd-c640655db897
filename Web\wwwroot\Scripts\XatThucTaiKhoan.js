﻿
$('#registerForm').on('submit', async function (e) {
  e.preventDefault();
  NTS.loadding()
  const Email = $('#Email').val();
  // Validate đơn giản
  const data = {
    tenDangNhap: sessionStorage.getItem('resetTenDangNhap'),
    matMa: sessionStorage.getItem('resetMatMa'),
    email: $('#Email').val()
  };
  try {
    const responseJSON = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/SendEmaiController/SendEmailXatThuc', data);
    const email = $('#Email').val();
    sessionStorage.setItem('resetEmail', email);
    const domain = window.location.origin;
    window.location.href = domain + '/hethong/NhapMaXatThucTaiKhoan';  // Thay '/dashboard' bằng URL bạn muốn

  } catch (error) {

    if (error && error.responseJSON && error.responseJSON.errors) {
      const errors = error.responseJSON.errors;

      // Lấy phần tử UL chứa các quy tắc mật khẩu
      const $rulesList = $('.password-rules');
      $rulesList.empty(); // Xóa các quy tắc cũ (mặc định)

      // Duyệt tất cả các lỗi
      for (let field in errors) {
        const fieldErrors = errors[field];

        fieldErrors.forEach(msg => {
          const $li = $('<li></li>').text('❌ ' + msg).css('color', 'red');
          $rulesList.append($li);
        });
      }
    } else {
      alert('Đăng ký thất bại. Lỗi không xác định.');
      console.error(error);
    }

  }
  NTS.unloadding()
});