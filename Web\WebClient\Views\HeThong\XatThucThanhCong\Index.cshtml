﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}
<style>
    *, *::before, *::after {
        box-sizing: border-box;
    }

    body {
        background: linear-gradient(135deg, #ffffff 0%, #45c97c 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }


    .page {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    }

    .container-tight {
        max-width: 480px;
        width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-success {
        background: #fff;
        border-radius: 1rem;
        box-shadow: 0 12px 40px rgb(0 0 0 / 0.1);
        padding: 2rem;
        text-align: center;
    }
    /* Icon đẹp hơn - vòng tròn đầy và check */
    .icon-tabler-circle-check {
        color: #28a745;
        margin-bottom: 1rem;
        stroke-width: 2.5;
        width: 64px;
        height: 64px;
    }

    .card-title {
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
        color: #28a745;
    }

    p {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }

    .btn {
        display: inline-block;
        font-weight: 600;
        text-align: center;
        cursor: pointer;
        border: 1.5px solid transparent;
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.375rem;
        transition: all 0.3s ease;
        user-select: none;
        text-decoration: none;
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
    }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

    .btn-outline-success {
        background-color: transparent;
        border-color: #28a745;
        color: #28a745;
    }

        .btn-outline-success:hover {
            background-color: #28a745;
            color: #fff;
        }

    .btn-group {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

</style>


<div class="page">
    <div class="container-tight">
        <div class="card-success">
            <!-- Icon Tabler Circle Check -->
            <svg xmlns="http://www.w3.org/2000/svg" class="icon-tabler-circle-check" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="9" />
                <path d="M9 12l2 2l4 -4" />
            </svg>

            <h2 class="card-title">Xác thực thành công!</h2>
            <p>Tài khoản ID NTSOFT của bạn đã được xác thực. Bạn có thể đăng nhập và sử dụng đầy đủ các chức năng của hệ thống.</p>
            <div class="btn-group">
                <a href="/" class="btn btn-success">Về trang chủ</a>
                <a href="/hethong/dangnhap" class="btn btn-outline-success">Vào hệ thống</a>
            </div>
        </div>
    </div>
</div>