﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Queries;
internal class GetChiaSeKeyByKeyID
{
}
public record GetChiaSeKeyByKeyIDQuery(
    string KeyID
    ) : IRequest<Result<List<ChiaSeKeyDto>>?>;


public class GetChiaSeKeyByKeyIDQueryValidator : AbstractValidator<GetChiaSeKeyByKeyIDQuery>
{
    public GetChiaSeKeyByKeyIDQueryValidator()
    {
    }
}
public class GetChiaSeKeyByKeyIDQueryHandler : IRequestHandler<GetChiaSeKeyByKeyIDQuery, Result<List<ChiaSeKeyDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetChiaSeKeyByKeyIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<ChiaSeKeyDto>>?> Handle(GetChiaSeKeyByKeyIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@KeyID", DungChung.NormalizationGuid(request.KeyID))
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ChiaSeKey_GetByKeyID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<ChiaSeKeyDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            ChiaSeKeyID = reader.GetGuid(reader.GetOrdinal("ChiaSeKeyID")),
            HoTen = reader["HoTen"] as string,
            TenDonViCongTac = reader["TenDonViCongTac"] as string,
            Email = reader["Email"] as string,
            LinkKichHoat = reader["LinkKichHoat"] as string,
            TrangThai = reader["TrangThai"] as string
        };
    }
}

