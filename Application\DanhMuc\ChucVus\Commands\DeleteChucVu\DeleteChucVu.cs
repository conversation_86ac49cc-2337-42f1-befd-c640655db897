﻿using Microsoft.Data.SqlClient;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;

namespace NHATTAMID2025.Application.ChucVus.Commands.DeleteChucVu;

public record DeleteChucVuCommand(Guid ChucVuID) : IRequest<Result<object>>;


public class DeleteChucVuCommandValidator : AbstractValidator<DeleteChucVuCommand>
{
    public DeleteChucVuCommandValidator()
    {
    }
}

public class DeleteChucVuCommandHandler : IRequestHandler<DeleteChucVuCommand, Result<object>>
{
    private readonly IApplicationDbContext _context;

    public DeleteChucVuCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<object>> Handle(DeleteChucVuCommand request, CancellationToken cancellationToken)
    {
        var sql = "EXEC sp_ChucVu_Delete @ChucVuID";

        var parameters = new[]
        {
            new SqlParameter("@ChucVuID", request.ChucVuID)
        };

        try
        {
            await _context.ExecuteSqlRawAsync(sql, parameters);
            return Result<object>.Success();
        }
        catch (Exception ex)
        {
            return Result<object>.Failure(new[] { ex.Message });
        }
    }
}
