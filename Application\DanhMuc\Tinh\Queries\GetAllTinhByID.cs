﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Tinh.Queries;


public record GetAllTinhByIDQuery(
    string TinhID
    ) : IRequest<Result<List<TinhDto>>?>;


public class GetAllTinhByIDQueryValidator : AbstractValidator<GetAllTinhByIDQuery>
{
    public GetAllTinhByIDQueryValidator()
    {
    }
}
public class GetAllTinhByIDQueryHandler : IRequestHandler<GetAllTinhByIDQuery, Result<List<TinhDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllTinhByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<TinhDto>>?> Handle(GetAllTinhByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@TinhID", request.TinhID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Tinh_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<TinhDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private TinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TinhDto
        {
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
