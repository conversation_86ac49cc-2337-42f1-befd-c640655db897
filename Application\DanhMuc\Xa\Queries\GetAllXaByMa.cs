﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Xa.Queries;

public record GetAllXaByMaIDQuery(
    string MaXa
    ) : IRequest<Result<List<XaDto>>?>;
public class GetAllXaByMaIDQueryValidator : AbstractValidator<GetAllXaByMaIDQuery>
{
    public GetAllXaByMaIDQueryValidator()
    {
    }
}
public class GetAllXaByMaIDQueryHandler : IRequestHandler<GetAllXaByMaIDQuery, Result<List<XaDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllXaByMaIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<XaDto>>?> Handle(GetAllXaByMaIDQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@MaXa", request.MaXa)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Xa_GetByMaXa",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<List<XaDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private XaDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new XaDto
        {
            XaID = reader.GetGuid(reader.GetOrdinal("XaID")),
            TinhID = reader.IsDBNull(reader.GetOrdinal("TinhID"))
                     ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            MaXa = reader["MaXa"] as string,
            TenXa = reader["TenXa"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
