﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}
<style>
    *, *::before, *::after {
        box-sizing: border-box;
    }

    body {
        background: linear-gradient(135deg, #ffffff 0%, #45c97c 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }


    .page {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
    }

    .container-tight {
        max-width: 400px;
        width: 100%;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card-success {
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 12px 40px rgb(0 0 0 / 0.1);
        padding: 2rem;
        text-align: center;
    }
    /* Icon đẹp hơn - vòng tròn đầy và check */
    .icon-tabler-circle-check {
        color: #28a745;
        margin-bottom: 1rem;
        stroke-width: 2.5;
        width: 64px;
        height: 64px;
    }

    .card-title {
        font-weight: 700;
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
        color: #28a745;
    }

    p {
        color: #6c757d;
        margin-bottom: 1.5rem;
    }

    .btn {
        display: inline-block;
        font-weight: 600;
        text-align: center;
        cursor: pointer;
        border: 1.5px solid transparent;
        padding: 0.5rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.375rem;
        transition: all 0.3s ease;
        user-select: none;
        text-decoration: none;
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
        color: #fff;
    }

        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }

    .btn-outline-success {
        background-color: transparent;
        border-color: #28a745;
        color: #28a745;
    }

        .btn-outline-success:hover {
            background-color: #28a745;
            color: #fff;
        }

    .btn-group {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }
      .btn-primary {
        background-color: #1e88e5;
        border-color: #1e88e5;
        border-radius: 5px;
    }


    .password-rules {
        font-size: 13px;
        color: #4caf50;
        margin-top: 10px;
    }

        .password-rules li {
            margin-bottom: 4px;
            text-align: left
        }

</style>

<form id="registerForm" >
<div class="page">
    <div class="container-tight">
        <div class="card-success">
            <!-- Icon Tabler Circle Check -->
            <svg xmlns="http://www.w3.org/2000/svg" class="icon-tabler-circle-check" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="9" />
                <path d="M9 12l2 2l4 -4" />
            </svg>

            <h2 class="card-title"> Email của bạn đã được xác thực thành công!</h2>
            <p>Bây giờ bạn có thể tạo mật khẩu mới để bắt đầu sử dụng đầy đủ các chức năng của hệ thống ID NTSOFT.</p>

         


            <div class="mb-3">
                <div class="input-group input-group-flat">
                    <input type="password" class="form-control" name="email" id="passwordmoi" placeholder="Nhập mật khẩu mới" required>
                    <span class="input-group-text">
                        <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0" />
                                <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6" />
                            </svg>
                        </a>
                    </span>
                </div>
            </div>

            <div class="mb-3">
                <div class="input-group input-group-flat">
                    <input type="password" class="form-control" name="email" id="passwordnhaplai" placeholder="Nhập lại mật khẩu mới" required>
                    <span class="input-group-text">
                        <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0" />
                                <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6" />
                            </svg>
                        </a>
                    </span>
                </div>
            </div>
            <ul class="password-rules list-unstyled">
            </ul>
            <div class="form-footer">
                <button type="submit" class="btn btn-primary w-100" >Tạo mật khẩu</button>
            </div>
            <div class="text-center text-muted mt-3"><a href="/hethong/dangnhap">Quay lại đăng nhập</a>
            </div>
        </div>
    </div>
</div>
</form>

<div class="verify-container d-none card-success" id="verifyFormContainer" style="width:400px">
  
    <form id="verifyForm">
        <svg xmlns="http://www.w3.org/2000/svg" class="icon-tabler-circle-check" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="9" />
            <path d="M9 12l2 2l4 -4" />
        </svg>

        <h2 class="card-title">Mật khẩu của bạn đã được thay đổi thành công!</h2>
        <p>Bạn có thể đăng nhập và sử dụng đầy đủ các chức năng của hệ thống ID NTSOFT.</p>
         <div class="text-center text-muted mt-3"><a href="/hethong/dangnhap">Quay lại đăng nhập</a>
            </div>
    </form>
</div>
<script src="~/scripts/taomatkhaumoi.js"></script>