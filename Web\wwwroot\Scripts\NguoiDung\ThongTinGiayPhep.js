﻿$(document).ready(async function () {
   LoadDuLieu()

});

async function LoadDuLieu() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/ThongTinGiayPhep/getbyid/${localStorage.getItem("userID")}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    debugger
    if (originalData.length > 0) {
      for (var i = 0; i < originalData.length; i++) {
        var item = originalData[i];
        var htmlTenPhanMem = ``;
        var tenPhanMemList = item.tenPhanMem.split('|');
        for (var j = 0; j < tenPhanMemList.length; j++) {
          var ten = tenPhanMemList[j].trim(); // loại bỏ khoảng trắng thừa nếu có

          // Tạo HTML và append vào #DanhSachKey
          var htmlTenPhanMem = htmlTenPhanMem + `   <div class="list-group list-group-flush" >
                            <div class="list-group-item d-flex align-items-center" ${item.loai == "ChiaSe" ? `` : `onclick="MomodalNguoiDungKey('${item.keyID}','${tenPhanMemList[j].trim().split('_')[0]}','${tenPhanMemList[j].trim().split('_')[1]}')"` } >
                                <span class="avatar bg-blue-lt text-blue me-3"><i class="fa-solid ${tenPhanMemList[j].trim().split('_')[2]}"></i></span>
                                <div>
                                    <div class="fw-bold">${tenPhanMemList[j].trim().split('_')[0]}</div>
                                 
                                    <small class="text-muted">${tenPhanMemList[j].trim().split('_')[1]}</small>
                                </div>
                            </div>
                       
                        </div> `;
        }
        var html = `     <div class="col-md-6 license-section">
                <div class="card mb-4">
                    <div class="card-header" style="background: linear-gradient(177deg, #ffffff 44%, #ffffff 127%);">
                        <div class="d-flex justify-content-between w-100">
                            <h3 class="card-title"><i class="fas fa-crown me-1 text-warning"></i>${item.tenGoiBanQuyen}</h3>

                               ${hienThiBadgeTrangThai(item.trangThai, item.soNgayConLai)}
                        </div>
                    </div>
                    <div class="card-body">
                        <h4 class="text-muted mb-3"> ${item.chiaSeBoi == '' ? 'Phần mềm được cấp phép' : item.chiaSeBoi }</h4>
                        ${htmlTenPhanMem}
                        <p style=" display: none;" id="TenGoiBanQuyen${item.keyID}" class="text-muted">${item.tenGoiBanQuyen}</p>
                        <p style=" display: none;" id="TenPhanMem${item.keyID}" class="text-muted">${item.tenPhanMem}</p>
                        <p style=" display: none;" id="NgayTao${item.keyID}" class="text-muted">${item.ngayTao}</p>
                        <p style=" display: none;" id="NgayHetHan${item.keyID}" class="text-muted">${item.ngayHetHan}</p>
                        <p style=" display: none;" id="SoNgayConLai${item.keyID}" class="text-muted">${item.soNgayConLai}</p>
                        <p style=" display: none;" id="SoLanKichHoat${item.keyID}" class="text-muted">${item.soLanKichHoat}</p>
                        <p style=" display: none;" id="TrangThai${item.keyID}" class="text-muted">${item.trangThai}</p>

                        ${(item.loai == "ChiaSe" ? `<label class="form-label">Link kích hoạt</label>` : `<label class="form-label">Mã kích hoạt (đã kích hoạt ${item.soLanKichHoat + ''}/${item.soThietBiToiDa})</label>`)}
                  
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" readonly value="${item.maKey}">
                            <button class="btn btn-outline-secondary" ${( item.loai == "ChiaSe" ? "disabled" : "")} type="button" onclick="momdanhsachkey('${item.maKey}', '${item.keyID}')">Chia sẻ</button>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="#" class="btn btn-primary w-100" onclick="XemChiTietKey('${item.keyID}')">Xem chi tiết</a>
                            <a href="#" id="bnt${item.maKey}"  class="btn btn-outline-secondary ${(item.giaHan == "1" || item.loai == "ChiaSe" ? "disabled" : "")}" ${(item.giaHan == "1" ? `aria-disabled="true""` : "")}  onclick="DangKyPhanMem('${item.maKey}')">
                                <i class="ti ti-refresh me-1"></i> Gia hạn & Nâng cấp
                            </a>
                        </div>
                    </div>
                </div>
            </div>`;
        $('#DanhSachKey').append(html);
      }
    } else {
      $('#noDataMsg').show()

    }
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

async function XemChiTietKey(id) {
  $('#userInfoModal').modal("show")
  var tenPhanMemList = $('#TenPhanMem' + id).text().split('|');
  $('#TenPhanMem').html(``)
  for (var j = 0; j < tenPhanMemList.length; j++) {
    $('#TenPhanMem').append(`${tenPhanMemList[j].trim().split('_')[0]}</br>`)
  }
  $('#TenGoiBanQuyen').text($('#TenGoiBanQuyen' + id).text())
  $('#NgayTao').text($('#NgayTao' + id).text())
  $('#NgayHetHan').text(($('#NgayHetHan' + id).text() == 'null' ? 'Vĩnh viễn' : $('#NgayHetHan' + id).text()))
  $('#SoNgayConLai').text($('#SoNgayConLai' + id).text())
  $('#SoLanKichHoat').text($('#SoLanKichHoat' + id).text())
  $('#TrangThai').text($('#TrangThai' + id).text())
  $('#alertcanhbao').html(hienThiCanhBao($('#TrangThai' + id).text(), $('#SoNgayConLai' + id).text()))
}
function hienThiCanhBao(trangThai, soNgayConLai) {
  let alertType = "";
  let title = "";
  let message = "";
  let backgroundColor = "";
  debugger
  if (trangThai === "Đã khóa") {
    alertType = "danger";
    title = "Key đã bị khóa";
    message = "Key của bạn đã bị khóa. Vui lòng liên hệ quản trị viên để biết thêm chi tiết.";
    backgroundColor = "#f8d7da";
  } else if (trangThai === "Hoạt động") {
    if (soNgayConLai === "Hết hạn") {
      alertType = "danger";
      title = "Key đã hết hạn";
      message = "Key của bạn đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng dịch vụ.";
      backgroundColor = "#f8d7da";
    } else if (soNgayConLai === "Vĩnh viễn") {
      return ""; // Không hiển thị cảnh báo
    } else if ( soNgayConLai < 7) {
      alertType = "warning";
      title = "Cảnh báo sắp hết hạn";
      message = `Key của bạn sẽ hết hạn sau <strong>${soNgayConLai} ngày</strong>. Vui lòng gia hạn để tránh gián đoạn dịch vụ.`;
      backgroundColor = "#fff3cd";
    } else {
      alertType = "info";
      title = "Thông báo thời hạn key";
      message = `Key của bạn còn <strong>${soNgayConLai} ngày</strong>. Hãy đảm bảo gia hạn trước khi hết hạn.`;
      backgroundColor = "#d1ecf1";
    }
  }

  return `
    <div class="alert alert-${alertType} d-flex align-items-center" role="alert">
        <div class="flex-shrink-0 me-3 d-flex align-items-center justify-content-center" 
             style="width: 40px; height: 40px; background-color: ${backgroundColor}; border-radius: 50%;">
            <!-- icon SVG có thể thay theo alertType -->
            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock text-${alertType}" width="24" height="24"
                 viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                 stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <rect x="5" y="11" width="14" height="10" rx="2" />
                <circle cx="12" cy="16" r="1" />
                <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
            </svg>
        </div>
        <div>
            <h4 class="alert-title mb-1">${title}</h4>
            <div class="text-secondary">${message}</div>
        </div>
    </div>
    `;
}
function hienThiTrangThai(trangThai, soNgayConLai) {
  let alertType = "";
  let title = "";
  let message = "";
  let backgroundColor = "";
  if (trangThai === "Đã khóa") {
    title = "Key đã bị khóa";
    message = "Key của bạn đã bị khóa. Vui lòng liên hệ quản trị viên để biết thêm chi tiết.";
    backgroundColor = "#f8d7da";
  } else if (trangThai === "Hoạt động") {
    if (soNgayConLai === "Hết hạn") {
      alertType = "danger";
      title = "Key đã hết hạn";
      message = "Key của bạn đã hết hạn. Vui lòng gia hạn để tiếp tục sử dụng dịch vụ.";
      backgroundColor = "#f8d7da";
    } else if (soNgayConLai === "Vĩnh viễn") {
      return ""; // Không hiển thị cảnh báo
    } else if (soNgayConLai < 7) {
      alertType = "warning";
      title = "Cảnh báo sắp hết hạn";
      message = `Key của bạn sẽ hết hạn sau <strong>${soNgayConLai} ngày</strong>. Vui lòng gia hạn để tránh gián đoạn dịch vụ.`;
      backgroundColor = "#fff3cd";
    } else {
      alertType = "info";
      title = "Thông báo thời hạn key";
      message = `Key của bạn còn <strong>${soNgayConLai} ngày</strong>. Hãy đảm bảo gia hạn trước khi hết hạn.`;
      backgroundColor = "#d1ecf1";
    }
  }

  return `
    <div class="alert alert-${alertType} d-flex align-items-center" role="alert">
        <div class="flex-shrink-0 me-3 d-flex align-items-center justify-content-center" 
             style="width: 40px; height: 40px; background-color: ${backgroundColor}; border-radius: 50%;">
            <!-- icon SVG có thể thay theo alertType -->
            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock text-${alertType}" width="24" height="24"
                 viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                 stroke-linejoin="round">
                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                <rect x="5" y="11" width="14" height="10" rx="2" />
                <circle cx="12" cy="16" r="1" />
                <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
            </svg>
        </div>
        <div>
            <h4 class="alert-title mb-1">${title}</h4>
            <div class="text-secondary">${message}</div>
        </div>
    </div>
    `;
}
function hienThiBadgeTrangThai(trangThai, soNgayConLai) {
  let badgeClass = "";
  let text = "";

  if (trangThai === "Đã khóa") {
    badgeClass = "badge badge-status-locked";
    text = "Đã khóa";
  } else if (trangThai === "Hoạt động") {
    if (soNgayConLai === "Vĩnh viễn") {
      badgeClass = "badge badge-status-active";
      text = "Vĩnh viễn";
    } else if (soNgayConLai === "Hết hạn" || soNgayConLai <= 0) {
      badgeClass = "badge badge-status-expired";
      text = "Hết hạn";
    } else if (soNgayConLai < 7) {
      badgeClass = "badge badge-status-warning";
      text = `Còn lại ${soNgayConLai} ngày (sắp hết hạn)`;
    } else {
      badgeClass = "badge badge-status-active";
      text = `Còn ${soNgayConLai} ngày sử dụng`;
    } 
  } else {
    badgeClass = "badge badge-secondary";
    text = "Không xác định";
  }

  return `<span class="${badgeClass}">${text}</span>`;
}
function MomodalNguoiDungKey(id,tenphanmem,mota) {
  $('#modalNguoiDungKey').modal('show')
  $('#TenUngDungmd').text(tenphanmem)
  $('#MoTaUngDungmd').text(mota)
  LoadDSKichHoat(id, tenphanmem)
}
async function LoadDSKichHoat(id, tenphanmem) {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/KichHoatKey/getbyid/${id}/${tenphanmem}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    $('#DSKichHoatKeyTable').DataTable().destroy();
    $('#DSKichHoatKeyTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 0, leftColumns: 0
      },
      columns: [
        { data: 'nguoiKichHoat', width: "90px" },
        { data: 'donViKichHoat', width: "90px" },
        { data: 'emailKichHoat', width: "90px" },
        { data: 'link', width: "90px" },
        {
          data: 'ngayKichHoat',
          width: "90px",
          render: function (data, type, row) {
            if (!data) return '';
            const date = new Date(data);
            const pad = num => num.toString().padStart(2, '0');
            return `${pad(date.getDate())}/${pad(date.getMonth() + 1)}/${date.getFullYear()} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`;
          }
        },
        { data: 'tenThietBi', width: "90px" },
        {data: 'ipKichHoat', width: "90px" },
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    //setTimeout(() => {
    //  table.columns.adjust().draw(false);
    //}, 300);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}
async function DangKyPhanMem(id) {
  $('#DangKyModal').modal("show")
  var tenPhanMemList = $('#TenPhanMem' + id).text().split('|');
  $('#mdTenPhanMem').html(``)
  for (var j = 0; j < tenPhanMemList.length; j++) {
    $('#mdTenPhanMem').append(`${tenPhanMemList[j].trim().split('_')[0]}</br>`)
  }
  $('#hfKeyID').value(id)
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/NguoiDung/getbyid/${localStorage.getItem("userID")}`, {});
  if (response.succeeded) {
    var originalData = response.result;

    $('#mdHoTen').text(originalData[0].hoVaTen)
    $('#mdSDT').text(originalData[0].soDienThoai)
    $('#mdEmail').text(originalData[0].email)
    $('#mddonViCongTac').text(originalData[0].donViCongTac)


  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

$('#DangKyModalForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    dangKyGiayPhepID: "",
    phanMemID: "",
    userID: localStorage.getItem("userID"),
    noiDung: $('#noidung').value(),
    loaiDangKy: "2",
    KeyID: $('#hfKeyID').value(),
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/DangKyGiayPhep/create', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Đăng ký giấy phép sử dụng phần mềm thành công!");
      }
      $('#DangKyModal').modal('hide');
      $('#bnt' + $('#hfKeyID').value())
        .addClass('disabled')
        .attr('aria-disabled', 'true')
        .css('pointer-events', 'none');
      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Đăng ký giấy phép sử dụng phần mềm thất bại!");
  }
});

function momdanhsachkey(maKey, keyID) {
  $('#hfKeyID').value(keyID)
  LoadLuoiDanhSach()

  $('#modalDanhSachKey').modal('show');
 
}
function momthemmoidanhsachkey() {
  $('#modalChiaSeKey').modal('show');
  $('#ChiaSeKeyID').value('')
  TaoKeychiaSe()
  return false;
}
async function LoadLuoiDanhSach() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/ChiaSeKey/getByKeyID/${$('#hfKeyID').value()}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    $('#DanhSachKeyTable').DataTable().destroy();
    $('#DanhSachKeyTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },

      columns: [
        {
          data: 'trangThai',
          width: "20%",
          render: function (data, type, row) {
            let label = '';
            let icon = '';
            let className = 'd-inline-flex align-items-center gap-1 fw-semibold';

            switch (data) {
              case '0':
                label = 'Chưa kích hoạt';
                icon = `<svg xmlns="http://www.w3.org/2000/svg" class="text-secondary" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="9" />
                  <polyline points="12 7 12 12 15 15" />
               </svg>`;
                break;
              case '1':
                label = 'Đã kích hoạt';
                icon = `<svg xmlns="http://www.w3.org/2000/svg" class="text-success" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M5 12l5 5l10 -10" />
               </svg>`;
                break;
              case '2':
                label = 'Thu hồi key';
                icon = `<svg xmlns="http://www.w3.org/2000/svg" class="text-danger" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <rect x="5" y="11" width="14" height="10" rx="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
               </svg>`;
                break;
              default:
                label = 'Không xác định';
                icon = `<svg xmlns="http://www.w3.org/2000/svg" class="text-muted" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M12 9v2m0 4v.01" />
                  <path d="M10 3l-9 16h18l-9 -16z" />
               </svg>`;
            }

            return `<span class="${className}">${icon}${label}</span>`;
          }
        }

,
        {
          data: 'linkKichHoat',
          width: "25%",
          render: function (data, type, row) {
            if (!data) return '<span class="text-muted small fst-italic">Chưa có liên kết</span>';

            const maxLength = 50;
            const displayText = data.length > maxLength ? data.substring(0, maxLength) + '…' : data;

            return `
      <div class="d-flex align-items-center small text-muted" style="font-size:11px !important">
        <a href="${data}" target="_blank" class="me-2 text-decoration-none" title="${data}">
          <i class="fas fa-link"></i> ${displayText}
        </a>
        <button class="btn btn-sm btn-light border copy-link-btn" data-link="${data}" title="Sao chép liên kết">
          <i class="fas fa-copy"></i>
        </button>
      </div>
    `;
          }
        }
        ,
        { data: 'hoTen', width: "15%" },
    
        {
          data: 'email',
          width: "20%",
          render: function (data, type, row) {
            // Đã xác thực
            return `<span class="text-success" title="Đã xát thực email người dùng"><i class="fas fa-check-circle" ></i> ${data}</span>`;
          }
        },
        
        
        { data: 'tenDonViCongTac', width: "25%" },
        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "5%",
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.chiaSeKeyID}');   return false">
                                            <i class="fa fa-user-lock text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.chiaSeKeyID}');   return false">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    //setTimeout(() => {
    //  table.columns.adjust().draw(false);
    //}, 300);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

$(document).on('click', '.copy-link-btn', function () {
  const link = $(this).data('link');
  navigator.clipboard.writeText(link).then(() => {
  });
  return false
});
$('#modalChiaSeKeyForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    ChiaSeKeyID: $('#ChiaSeKeyID').value(),
    KeyID: $('#hfKeyID').value(),
    HoTen: $('#recipientName').value(),
    TenDonViCongTac: $('#donViNhanKey').value(),
    DonViCongTacID: '',
    Email: $('#recipientEmail').value(),
    LinkKichHoat: $('#linkKichHoat').value(),
    TrangThai: "",
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/ChiaSeKey/create', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Chia sẻ giấy phép sử dụng phần mềm thành công!");
      }
      LoadLuoiDanhSach()
      $('#modalChiaSeKey').modal('hide');

      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Chia sẻ giấy phép sử dụng phần mềm thất bại!");
  }
});

async function TaoKeychiaSe() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/ChiaSeKey/getbyid/${$('#hfKeyID').value()}/${($('#ChiaSeKeyID').value() == '' ? "00000000-0000-0000-0000-000000000000" : $('#ChiaSeKeyID').value())}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    $('#linkKichHoat').value(originalData[0].linkKichHoat)
    $('#ChiaSeKeyID').value(originalData[0].chiaSeKeyID)
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

async function deleteRow(id) {
  // Icon SVG Tabler cảnh báo (triangle-exclamation)
  const tablerWarningSVG = `
    <svg class="tabler-icon-warning" xmlns="http://www.w3.org/2000/svg" 
         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
         stroke-linejoin="round" stroke-linecap="round" style="width:72px; height:72px; margin: 0 auto 15px auto; stroke:#f59e0b;">
      <path d="M12 9v4"></path>
      <path d="M12 17h.01"></path>
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    </svg>
  `;

  const confirmed = await Swal.fire({
    title: 'Bạn có chắc muốn xóa?',
    text: "Hành động này sẽ không thể hoàn tác!",
    icon: 'warning', // vẫn phải có icon để vùng icon xuất hiện
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    reverseButtons: true,
    didOpen: () => {
      // Thay thế icon mặc định bằng SVG Tabler
      const iconElem = document.querySelector('.swal2-icon.swal2-warning');
      if (iconElem) {
        iconElem.innerHTML = tablerWarningSVG;
        iconElem.style.background = 'none'; // bỏ nền mặc định
        iconElem.style.border = 'none'; // bỏ viền mặc định
      }
    }
  });

  if (confirmed.isConfirmed) {
    const data = {
      ChiaSeKeyID: id
    };

    try {
      const response = await NTS.getAjaxAPIAsync(
        'POST',
        window.location.origin + '/api/ChiaSeKey/delete',
        data
      );
      if (response.succeeded == false && response.errors.length > 0) {
        NTS.canhbao(response.errors[0]);
      } else {
        if (response.succeeded == true) {
          await Swal.fire('Đã xóa!', 'Bản ghi đã được xóa thành công.', 'success');
          LoadLuoiDanhSach();
        }
      }
    } catch (error) {
      Swal.fire('Lỗi!', 'Không thể xóa bản ghi.', 'error');
    }
  }
}

async function editRow(id) {
  $('#editInfoModal').modal('show')
  $('#ChiaSeKeyID').value(id)
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/ChiaSeKey/getbyid/${$('#hfKeyID').value()}/${(id)}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    document.getElementById("hovaten").textContent = originalData[0].hoTen || "Chưa có";
    document.getElementById("email").textContent = originalData[0].email || "Chưa có";
    document.getElementById("donViCongTac").textContent = originalData[0].tenDonViCongTac || "Chưa có";
    document.getElementById("linkkichhoat").textContent = originalData[0].linkKichHoat || "Chưa có";
    $('#KhoaTaiKhoan').value((originalData[0].khoaKey == "0" ? true : false));
  }
}

$('#KhoaTaiKhoan').on('change', function () {
  const isLocked = $(this).is(':checked');
  $('#modal-statusLabel').text(isLocked ? "Thu hồi key" : "Key hoạt động");
});

document.getElementById('KhoaTaiKhoan').addEventListener('change', function () {
  const statusLabel = document.getElementById('modal-statusLabel');
  if (this.checked) {
    statusLabel.textContent = 'Key hoạt động';
    statusLabel.style.color = '#0054a6'; // Xanh dương khi kích hoạt
  } else {
    statusLabel.textContent = 'Thu hồi key';
    statusLabel.style.color = '#d63939'; // Đỏ khi khóa
  }
});


$('#editInfoModalForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    ChiaSeKeyID: $('#ChiaSeKeyID').value(),
    TrangThai: ($('#KhoaTaiKhoan').value() == false ? "1" : "0"),
    Loai: 'KhoaKey'
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/ChiaSeKey/update', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Chia sẻ giấy phép sử dụng phần mềm thành công!");
      }
      LoadLuoiDanhSach()
      $('#editInfoModal').modal('hide');

      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Chia sẻ giấy phép sử dụng phần mềm thất bại!");
  }
});