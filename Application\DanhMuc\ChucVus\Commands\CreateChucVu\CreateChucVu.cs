﻿using Microsoft.Data.SqlClient;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;

namespace NHATTAMID2025.Application.ChucVus.Commands.Create;

public record CreateChucVuCommand(string MaChucVu, string TenChucVu, string? DienGiai, bool NgungTD) : IRequest<Result<object>>;
public class CreateCommandValidator : AbstractValidator<CreateChucVuCommand>
{
    public CreateCommandValidator()
    {
    }
}
public class CreateChucVuCommandHandler : IRequestHandler<CreateChucVuCommand, Result<object>>
{
    private readonly IApplicationDbContext _context;

    public CreateChucVuCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<object>> Handle(CreateChucVuCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        var sql = "EXEC sp_ChucVu_Create @ChucVuID, @MaChucVu, @TenChucVu, @DienGiai, @NgungTD";

        var parameters = new[]
        {
            new SqlParameter("@ChucVuID", id),
            new SqlParameter("@MaChucVu", request.MaChucVu),
            new SqlParameter("@TenChucVu", request.TenChucVu),
            new SqlParameter("@DienGiai", request.DienGiai ?? (object)DBNull.Value),
            new SqlParameter("@NgungTD", request.NgungTD)
        };

        try
        {
            await _context.ExecuteSqlRawAsync(sql, parameters);
            return Result<object>.Success();
        }
        catch (Exception ex)
        {
            return Result<object>.Failure(new[] { ex.Message });
        }
    }
}
