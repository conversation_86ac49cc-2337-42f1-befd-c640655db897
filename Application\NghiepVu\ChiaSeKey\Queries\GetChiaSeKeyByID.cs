﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;
namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Queries;
internal class GetChiaSeKeyByID
{
}
public record GetChiaSeKeyByIDQuery(
    string KeyID,
    string ChiaSeKeyID
    ) : IRequest<Result<List<ChiaSeKeyDto>>?>;


public class GetChiaSeKeyByIDQueryValidator : AbstractValidator<GetChiaSeKeyByIDQuery>
{
    public GetChiaSeKeyByIDQueryValidator()
    {
    }
}
public class GetChiaSeKeyByIDQueryHandler : IRequestHandler<GetChiaSeKeyByIDQuery, Result<List<ChiaSeKeyDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetChiaSeKeyByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<ChiaSeKeyDto>>?> Handle(GetChiaSeKeyByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@KeyID", DungChung.NormalizationGuid(request.KeyID)),
                       new Microsoft.Data.SqlClient.SqlParameter("@ChiaSeKeyID", DungChung.NormalizationGuid(request.ChiaSeKeyID))
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ChiaSeKey_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<ChiaSeKeyDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            ChiaSeKeyID = reader.GetGuid(reader.GetOrdinal("ChiaSeKeyID")),
            HoTen = reader["HoTen"] as string,
            TenDonViCongTac = reader["TenDonViCongTac"] as string,
            Email = reader["Email"] as string,
            LinkKichHoat = reader["LinkKichHoat"] as string,
            TrangThai = reader["TrangThai"] as string,
            KhoaKey = reader["KhoaKey"] as string,
            
        };
    }
}
