﻿const baseUrl = "https://localhost:5001"; // 👈 sửa lại đúng địa chỉ baseUrl của bạn
$('#resendCode').on('click', async function () {
  NTS.loadding()
  const hoVaTen = $('#hoTen').val().trim();
  const gioiTinh = $('input[name="gioitinh"]:checked').val();
  const email = $('#email').val().trim();
  const soDienThoai = $('#tel').val().trim();
  const matMa = $('#password').val();
  const nhapLaiMatMa = $('#nhaplaipassword').val();
  const tenDangNhap = $('#tenDangNhap').val().trim();
  // Validate đơn giản

  const data = {
    tenDangNhap: tenDangNhap, // hoặc bạn có thể tự định nghĩa tên đăng nhập
    matMa: matMa,
    nhapLaiMatMa: nhapLaiMatMa,
    hoVaTen: hoVaTen,
    gioiTinh: gioiTinh,
    soDienThoai: soDienThoai,
    email: email,
    diaChi: "", // thêm input nếu cần
    donViCongTac: "", // thêm input nếu cần
    MaXacThuc: ""
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', baseUrl + '/api/SendEmaiController', data);

  } catch (error) {
    if (error && error.responseJSON && error.responseJSON.errors) {
      const errors = error.responseJSON.errors;

      // Lấy phần tử UL chứa các quy tắc mật khẩu
      const $rulesList = $('.password-rules');
      $rulesList.empty(); // Xóa các quy tắc cũ (mặc định)

      // Duyệt tất cả các lỗi
      for (let field in errors) {
        const fieldErrors = errors[field];

        fieldErrors.forEach(msg => {
          const $li = $('<li></li>').text('❌ ' + msg).css('color', 'red');
          $rulesList.append($li);
        });
      }
    } else {
      alert('Đăng ký thất bại. Lỗi không xác định.');
      console.error(error);
    }


  }
  NTS.unloadding()
});
$('#registerForm').on('submit', async function (e) {
  debugger
  NTS.loadding()
  e.preventDefault();
  const hoVaTen = $('#hoTen').val().trim();
  const gioiTinh = $('input[name="gioitinh"]:checked').val();
  const email = $('#email').val().trim();
  const soDienThoai = $('#tel').val().trim();
  const matMa = $('#password').val();
  const nhapLaiMatMa = $('#nhaplaipassword').val();
  const tenDangNhap = $('#tenDangNhap').val().trim();
  // Validate đơn giản

  const data = {
    tenDangNhap: tenDangNhap, // hoặc bạn có thể tự định nghĩa tên đăng nhập
    matMa: matMa,
    nhapLaiMatMa: nhapLaiMatMa,
    hoVaTen: hoVaTen,
    gioiTinh: gioiTinh,
    soDienThoai: soDienThoai,
    email: email,
    diaChi: "", // thêm input nếu cần
    donViCongTac: "", // thêm input nếu cần
    MaXacThuc: ""
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', baseUrl + '/api/SendEmaiController', data);
    $('#registerForm').addClass('d-none');
    $('#verifyFormContainer').removeClass('d-none');
  } catch (error) {
    if (error && error.responseJSON && error.responseJSON.errors) {
      const errors = error.responseJSON.errors;

      // Lấy phần tử UL chứa các quy tắc mật khẩu
      const $rulesList = $('.password-rules');
      $rulesList.empty(); // Xóa các quy tắc cũ (mặc định)

      // Duyệt tất cả các lỗi
      for (let field in errors) {
        const fieldErrors = errors[field];

        fieldErrors.forEach(msg => {
          const $li = $('<li></li>').text('❌ ' + msg).css('color', 'red');
          $rulesList.append($li);
        });
      }
    } else {
      alert('Đăng ký thất bại. Lỗi không xác định.');
      console.error(error);
    }


  }
  NTS.unloadding()
});

$('#verifyForm').on('submit', async function (e) {

  e.preventDefault();
  NTS.loadding()
  const hoVaTen = $('#hoTen').val().trim();
  const gioiTinh = $('input[name="gioitinh"]:checked').val();
  const email = $('#email').val().trim();
  const soDienThoai = $('#tel').val().trim();
  const matMa = $('#password').val();
  const nhapLaiMatMa = $('#nhaplaipassword').val();
  const tenDangNhap = $('#tenDangNhap').val().trim();
  // Validate đơn giản
  const code = Array.from(inputs).map(input => input.value).join('');
  const data = {
    tenDangNhap: tenDangNhap, // hoặc bạn có thể tự định nghĩa tên đăng nhập
    matMa: matMa,
    nhapLaiMatMa: nhapLaiMatMa,
    hoVaTen: hoVaTen,
    gioiTinh: gioiTinh,
    soDienThoai: soDienThoai,
    email: email,
    diaChi: "", // thêm input nếu cần
    donViCongTac: "", // thêm input nếu cần
    MaXacThuc: code  
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', baseUrl + '/api/DangKyUsersController', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Xát thực tài khoản thành công");
        setTimeout(() => {
          // Lấy tên miền gốc (ví dụ https://domain.com)
          const domain = window.location.origin;

          // Ví dụ chuyển đến trang dashboard hoặc trang đăng nhập
          window.location.href = domain + '/hethong/XatThucThanhCong';  // Thay '/dashboard' bằng URL bạn muốn

        }, 1500); 
      }
    }
  } catch (error) {

  }
  NTS.unloadding()
});


const inputs = document.querySelectorAll('.code-input');

inputs.forEach((input, index) => {
  input.addEventListener('input', () => {
    const value = input.value;
    if (value.length === 1 && index < inputs.length - 1) {
      inputs[index + 1].focus();
    }
  });

  input.addEventListener('keydown', (e) => {
    if (e.key === 'Backspace' && !input.value && index > 0) {
      inputs[index - 1].focus();
    }
  });
});

document.getElementById('codeInputs').addEventListener('paste', function (e) {
  const paste = (e.clipboardData || window.clipboardData).getData('text');
  if (/^\d{6}$/.test(paste)) {
    e.preventDefault();
    paste.split('').forEach((char, i) => {
      if (inputs[i]) inputs[i].value = char;
    });
    inputs[5].focus();
  }
});

