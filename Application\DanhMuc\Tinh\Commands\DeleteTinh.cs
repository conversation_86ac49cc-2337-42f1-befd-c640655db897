﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Tinh.Commands;
public record DeleteTinhCommand(
    string TinhID
    ) : IRequest<Result<TinhDto>?>;

public class DeleteTinhCommandValidator : AbstractValidator<DeleteTinhCommand>
{
    private readonly IApplicationDbContext _context;
    public DeleteTinhCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.TinhID)
            .NotEmpty().WithMessage("TinhID không được để trống.");
    }
}

public class DeleteTinhCommandHandler : IRequestHandler<DeleteTinhCommand, Result<TinhDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteTinhCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<TinhDto>?> Handle(DeleteTinhCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TinhID", DungChung.NormalizationGuid(request.TinhID))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Tinh_Delete",
                   MapFromReader,
                   true,
                   parameters);
            return Result<TinhDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TinhDto
        {
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
        };
    }
}

