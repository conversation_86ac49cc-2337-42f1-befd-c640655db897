﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.Commands;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class DonViTinhController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
             .RequireAuthorization()
            .MapPost(CreateDonViTinh, "/")
            .MapGet(GetAllDonViTinh, "/");
    }
    public async Task<DonViTinhDto?> CreateDonViTinh(ISender sender, [FromBody] CreateDonViTinhCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<List<DonViTinhDto>> GetAllDonViTinh(ISender sender)
      => await sender.Send(new GetAllDonViTinhQuery());
}
