﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;

namespace NHATTAMID2025.Web.Endpoints.HeThong;

public class SendEmaiController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(SendEmai, "")
            .MapPost(SendEmailXatThuc, "/SendEmailXatThuc")
            .MapPost(UpdateEmai, "/update");
    }
    public async Task<Result<object>?> SendEmai(ISender sender, [FromBody] CreateSendEmaiCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<object>?> SendEmailXatThuc(ISender sender, [FromBody] SendEmailXatThucCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<UsersDangKyDto>?> UpdateEmai(ISender sender, [FromBody] CreateUpdateEmailCommand command)
    {
        return await sender.Send(command);
    }
}
