﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Commands;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class DangKyGiayPhep : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetDangKyGiayPhepByUserID, "/getbyuserid/{Id}")
            .MapPost(UpdateDangKyGiayPhep, "/update")
            .MapPost(CreateDangKyGiayPhep, "/create")
            .MapGet(GetAllDangKyGiayPhep, "/getall")
            .MapGet(GetDangKyGiayPhepById, "/getbyid/{Id}");
    }
    public async Task<Result<DangKyGiayPhepDto>?> CreateDangKyGiayPhep(ISender sender, [FromBody] CreateDangKyGiayPhepCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<DangKyGiayPhepDto>?> UpdateDangKyGiayPhep(ISender sender, [FromBody] UpdateDangKyGiayPhepCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<DangKyGiayPhepDto>>?> GetAllDangKyGiayPhep(ISender sender)
     => await sender.Send(new GetAllDangKyGiayPhepCommand());
    public async Task<Result<List<DangKyGiayPhepDto>>?> GetDangKyGiayPhepById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetDangKyGiayPhepByIDQuery(id));

    public async Task<Result<List<DangKyGiayPhepDto>>?> GetDangKyGiayPhepByUserID(ISender sender, [FromRoute] string id)
   => await sender.Send(new GetDangKyGiayPhepByUserIDQuery(id));
}
