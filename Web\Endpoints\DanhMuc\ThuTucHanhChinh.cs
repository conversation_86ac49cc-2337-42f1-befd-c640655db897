﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.DanhMuc.ThuTucHanhChinhs.DTOs;
using NHATTAMID2025.Application.ThuTucHanhChinhs.Queries.GetAllThuTucHanhChinh;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class ThuTucHanhChinh : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllThuTucHanhChinh, "/getall");
    }

    public async Task<PaginatedList<ThuTucHanhChinhDto>> GetAllThuTucHanhChinh(
        ISender sender,
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize,
        [FromQuery] string? maLinhVuc = null)
    {
        var query = new GetAllThuTucHanhChinhQuery(pageNumber, pageSize, maLinhVuc);
        return await sender.Send(query);
    }
}
