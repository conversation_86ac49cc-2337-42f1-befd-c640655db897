﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Xa.Commands;
public record DeleteXaCommand(
    string XaID
    ) : IRequest<Result<XaDto>?>;

public class DeleteXaCommandValidator : AbstractValidator<DeleteXaCommand>
{
    private readonly IApplicationDbContext _context;
    public DeleteXaCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.XaID)
            .NotEmpty().WithMessage("XaID không được để trống.");
    }
}

public class DeleteXaCommandHandler : IRequestHandler<DeleteXaCommand, Result<XaDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteXaCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<XaDto>?> Handle(DeleteXaCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@XaID", DungChung.NormalizationGuid(request.XaID))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Xa_Delete",
                   MapFromReader,
                   true,
                   parameters);
            return Result<XaDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private XaDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new XaDto
        {
            XaID = reader.GetGuid(reader.GetOrdinal("XaID")),
        };
    }
}

