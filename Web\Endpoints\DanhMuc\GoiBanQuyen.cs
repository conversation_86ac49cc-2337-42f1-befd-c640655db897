﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class GoiBanQuyen : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(DeleteGoiBanQuyen, "/delete")
            .MapPost(UpdateGoiBanQuyen, "/update")
            .MapPost(CreateG<PERSON>BanQuyen, "/create")
            .MapGet(GetAllGoiBanQuyen, "/getall")
            .MapGet(GetGoiBanQuyenById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<GoiBanQuyenDto>?> CreateGoiBanQuyen(ISender sender, [FromBody] CreateGoiBanQuyenCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<GoiBanQuyenDto>?> UpdateGoiBanQuyen(ISender sender, [FromBody] UpdateGoiBanQuyenCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<GoiBanQuyenDto>?> DeleteGoiBanQuyen(ISender sender, [FromBody] DeleteGoiBanQuyenCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<GoiBanQuyenDto>>?> GetAllGoiBanQuyen(ISender sender)
     => await sender.Send(new GetAllGoiBanQuyenCommand());
    public async Task<Result<List<GoiBanQuyenDto>>?> GetGoiBanQuyenById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetGoiBanQuyenByIDQuery(id));
}
