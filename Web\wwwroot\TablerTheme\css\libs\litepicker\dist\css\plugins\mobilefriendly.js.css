/*!
 * 
 * ../css/plugins/mobilefriendly.js.css
 * Litepicker v2.0.12 (https://github.com/wakirin/Litepicker)
 * Package: litepicker (https://www.npmjs.com/package/litepicker)
 * License: MIT (https://github.com/wakirin/Litepicker/blob/master/LICENCE.md)
 * Copyright 2019-2021 Rinat G.
 *     
 * Hash: fc3887e0bb19d54c36db
 * 
 */
:root {
  --litepicker-mobilefriendly-backdrop-color-bg: #000;
}

@-webkit-keyframes lp-bounce-target-next {
  from {
    -webkit-transform: translateX(100px) scale(0.5);
            transform: translateX(100px) scale(0.5);
  }
  to {
    -webkit-transform: translateX(0px) scale(1);
            transform: translateX(0px) scale(1);
  }
}

@keyframes lp-bounce-target-next {
  from {
    -webkit-transform: translateX(100px) scale(0.5);
            transform: translateX(100px) scale(0.5);
  }
  to {
    -webkit-transform: translateX(0px) scale(1);
            transform: translateX(0px) scale(1);
  }
}

@-webkit-keyframes lp-bounce-target-prev {
  from {
    -webkit-transform: translateX(-100px) scale(0.5);
            transform: translateX(-100px) scale(0.5);
  }
  to {
    -webkit-transform: translateX(0px) scale(1);
            transform: translateX(0px) scale(1);
  }
}

@keyframes lp-bounce-target-prev {
  from {
    -webkit-transform: translateX(-100px) scale(0.5);
            transform: translateX(-100px) scale(0.5);
  }
  to {
    -webkit-transform: translateX(0px) scale(1);
            transform: translateX(0px) scale(1);
  }
}
