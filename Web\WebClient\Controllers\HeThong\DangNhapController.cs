﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NHATTAMID2025.Application.Authentication;

namespace NHATTAMID2025.Web.WebClient.Controllers.HeThong;

public class LoginRequest
{
    public  string? TenDangNhap { get; set; }
    public  string? MatKhau { get; set; }
}
//[ApiExplorerSettings(IgnoreApi = true)]
[Route("hethong/dangnhap")]

public class DangNhapController : Controller
{
    private readonly HttpClient _httpClient;
    public DangNhapController(IHttpClientFactory httpClientFactory, ILogger<DangNhapController> logger)
    {
        _httpClient = httpClientFactory.CreateClient();
    }
    [Route("")]
    public IActionResult Index()
    {
        return View();
    }
    [HttpPost]
    [Route("login")]

    public async Task<IActionResult> Login([FromBody] LoginRequest data)
    {
        var content = new StringContent(
           JsonConvert.SerializeObject(data),
           Encoding.UTF8,
           "application/json"
       );

        var baseUrl = $"{Request.Scheme}://{Request.Host}";

        _httpClient.BaseAddress = new Uri(baseUrl);

        var response = await _httpClient.PostAsync("/api/Authentication/login", content);

        if (response.IsSuccessStatusCode)
        {
            var responseData = await response.Content.ReadAsStringAsync();
            var json = JObject.Parse(responseData);

            // Lấy giá trị errorMessage
    
            // Try to parse the response data into TokenResponse
            var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseData);

            if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.AccessToken))
            {

                // Safely access the AccessToken
                string accessToken = tokenResponse.AccessToken;
               
                Response.Cookies.Append("access_token", accessToken, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTimeOffset.UtcNow.AddHours(12)
                });

                return Json(new { success = true, redirectUrl = "/", message = "Đăng nhập thành công!" });
            }
            else
            {
                var errorMessage = (string?)json["errorMessage"];
                return Json(new { success = false, message = errorMessage });
            }
        }
        else
        {
            // If the API call failed, display an error
            return Json(new { success = false, message = "Đăng nhập không thành công, vui lòng kiểm tra lại tài khoản đăng nhập!" });
        }
    }
}
