﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Queries;

public record GetAllDangKyGiayPhepCommand : IRequest<Result<List<DangKyGiayPhepDto>>?>;

public class GetAllDangKyGiayPhepCommandValidator : AbstractValidator<GetAllDangKyGiayPhepCommand>
{
    public GetAllDangKyGiayPhepCommandValidator()
    {
    }
}

public class GetAllDangKyGiayPhepCommandHandler : IRequestHandler<GetAllDangKyGiayPhepCommand, Result<List<DangKyGiayPhepDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllDangKyGiayPhepCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DangKyGiayPhepDto>>?> Handle(GetAllDangKyGiayPhepCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyGiayPhep_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );

            return Result<List<DangKyGiayPhepDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private DangKyGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DangKyGiayPhepDto
        {
            DangKyGiayPhepID = reader.GetGuid(reader.GetOrdinal("DangKyGiayPhepID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            HoTen = reader["HoTen"] as string,
            TenGoi = reader["TenGoi"] as string,
            Email = reader["Email"] as string,
            TenTaiKhoan= reader["TenTaiKhoan"] as string,
            DonViCongTac = reader["DonViCongTac"] as string,
            NoiDung = reader["NoiDung"] as string,
            LoaiDangKy = reader["LoaiDangKy"] as string,
            NgayDangKy = reader["NgayDangKy"] as string,
            TrangThai = reader["TrangThai"] as string,
            NgayLienHe = reader["NgayLienHe"] as string,
            NoiDungLienHe = reader["NoiDungLienHe"] as string
        };
    }
}


