﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.DonVi.Commands;
using NHATTAMID2025.Application.DanhMuc.DonVi.DTOs;
using NHATTAMID2025.Application.DanhMuc.DonVi.Queries;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class DonVi : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllDonVi, "/getall")
            .MapGet(GetAllDonViByTinhID, "/getbytinhid/{TinhID}")
            .MapGet(GetAllDonViByMaTinh, "/getbymatinh/{MaTinh}")
            .MapGet(GetAllDonViByXaID, "/getbyxaid/{XaID}")
            .MapGet(GetAllDonViByMaXa, "/getbymaxa/{MaXa}")
            .MapPost(CreateDonVi, "/create")
            .MapPost(UpdateDonVi, "/update")
            .MapPost(DeleteDonVi, "/delete")
            ;
    }

    public async Task<Result<List<DonViDto>>?> GetAllDonVi(ISender sender)
    => await sender.Send(new GetAllDonViCommand());

    public async Task<Result<List<DonViDto>>?> GetAllDonViByTinhID(ISender sender, [FromRoute] string TinhID)
    => await sender.Send(new GetAllDonViByTinhIDQuery(TinhID));

    public async Task<Result<List<DonViDto>>?> GetAllDonViByMaTinh(ISender sender, [FromRoute] string MaTinh)
    => await sender.Send(new GetAllDonViByMaTinhQuery(MaTinh));

    public async Task<Result<List<DonViDto>>?> GetAllDonViByXaID(ISender sender, [FromRoute] string XaID)
   => await sender.Send(new GetAllDonViByXaIDQuery(XaID));

    public async Task<Result<List<DonViDto>>?> GetAllDonViByMaXa(ISender sender, [FromRoute] string MaXa)
    => await sender.Send(new GetAllDonViByMaXaQuery(MaXa));

    public async Task<Result<DonViDto>?> CreateDonVi(ISender sender, [FromBody] CreateDonViCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<DonViDto>?> UpdateDonVi(ISender sender, [FromBody] UpdateDonViCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<DonViDto>?> DeleteDonVi(ISender sender, [FromBody] DeleteDonViCommand command)
    {
        return await sender.Send(command);
    }
}
