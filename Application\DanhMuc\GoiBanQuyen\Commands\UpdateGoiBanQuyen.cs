﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands;
public record UpdateGoiBanQuyenCommand(
    string GoiBanQuyenID,
    string MaGoi,
    string TenGoi,
    string MoTa,
    string ThoiHanNgay,
    string SoThietBiToiDa,
    string LoaiNguoiDungID,
    string GiaTien
    ) : IRequest<Result<GoiBanQuyenDto>?>;

public class UpdateGoiBanQuyenCommandValidator : AbstractValidator<UpdateGoiBanQuyenCommand>
{
    public UpdateGoiBanQuyenCommandValidator()
    {
    }
}

public class UpdateGoiBanQuyenCommandHandler : IRequestHandler<UpdateGoiBanQuyenCommand, Result<GoiBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateGoiBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<GoiBanQuyenDto>?> Handle(UpdateGoiBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@GoiBanQuyenID", DungChung.NormalizationGuid(request.GoiBanQuyenID)),
            new Microsoft.Data.SqlClient.SqlParameter("@MaGoi", request.MaGoi),
            new Microsoft.Data.SqlClient.SqlParameter("@TenGoi", request.TenGoi),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@ThoiHanNgay", DungChung.NormalizationNumber(request.ThoiHanNgay)),
            new Microsoft.Data.SqlClient.SqlParameter("@SoThietBiToiDa", DungChung.NormalizationNumber(request.SoThietBiToiDa)),
            new Microsoft.Data.SqlClient.SqlParameter("@LoaiNguoiDungID", DungChung.NormalizationGuid(request.LoaiNguoiDungID)),
            new Microsoft.Data.SqlClient.SqlParameter("@GiaTien", DungChung.NormalizationNumber(request.GiaTien))
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_GoiBanQuyen_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<GoiBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private GoiBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new GoiBanQuyenDto
        {
            GoiBanQuyenID = reader.GetGuid(reader.GetOrdinal("GoiBanQuyenID")),
        };
    }
}
