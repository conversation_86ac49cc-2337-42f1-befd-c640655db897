﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Commands;
internal class CreateDangKyGiayPhep
{
}
public record CreateDangKyGiayPhepCommand(
        string DangKyGiayPhepID,
        string PhanMemID,
        string UserID,
        string NoiDung,
        string LoaiDangKy,
        string KeyID
    ) : IRequest<Result<DangKyGiayPhepDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateDangKyGiayPhepCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateDangKyGiayPhepCommandHandler : IRequestHandler<CreateDangKyGiayPhepCommand, Result<DangKyGiayPhepDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateDangKyGiayPhepCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<DangKyGiayPhepDto>?> Handle(CreateDangKyGiayPhepCommand request, CancellationToken cancellationToken)
    {
        var idKey = Guid.NewGuid();

        //string keyMaHoaMatKhau = "rateAnd2012";

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@PhanMemID", DungChung.NormalizationGuid(request.PhanMemID)),
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", DungChung.NormalizationGuid(request.UserID)),
            new Microsoft.Data.SqlClient.SqlParameter("@NoiDung", request.NoiDung),
            new Microsoft.Data.SqlClient.SqlParameter("@LoaiDangKy", request.LoaiDangKy),
            new Microsoft.Data.SqlClient.SqlParameter("@KeyID", DungChung.NormalizationGuid(request.KeyID)),
            
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyGiayPhep_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<DangKyGiayPhepDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private DangKyGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DangKyGiayPhepDto
        {
            DangKyGiayPhepID = reader.GetGuid(reader.GetOrdinal("DangKyGiayPhepID")),
        };
    }
}

