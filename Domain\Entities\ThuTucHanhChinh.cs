﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Domain.Entities;
public class ThuTucHanhChinh
{
    public Guid ThuTucHanhChinhID { get; set; }
    public string? MaThuTucHanhChinh { get; set; }
    public string? TenThuTucHanhChinh { get; set; }
    public string? MaCapHanhChinh { get; set; }
    public int LoaiThuTucHanhChinh { get; set; }
    public string? MaLinhVuc { get; set; }
    public string? TrinhTuThucHien { get; set; }
    public string? CachThucHien { get; set; }
    public string? DoiTuongThucHien { get; set; }
    public string? DiaChiTiepNhan { get; set; }
    public string? YeuCau { get; set; }
    public string? KetQuaThucHien { get; set; }
    public string? MoTa { get; set; }
    public string? CanCuPhapLy { get; set; }
    public Guid? VanBanID { get; set; }
}
