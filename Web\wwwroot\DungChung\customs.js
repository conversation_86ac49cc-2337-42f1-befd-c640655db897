﻿function QuyenThem() {
  if (ntspermiss.them) {
    return true;
  }
  NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác thêm mới. Vui lòng kiểm tra lại.");
  return false;
}
function QuyenSua() {
  if (ntspermiss.sua) {
    return true;
  }
  NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác chỉnh sửa. Vui lòng kiểm tra lại.");
  return false;
}
function QuyenXoa() {
  if (ntspermiss.xoa) {
    return true;
  }
  NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác xóa. Vui lòng kiểm tra lại.");
  return false;
}
function QuyenIn() {
  if (ntspermiss.in) {
    return true;
  }
  NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác xóa. Vui lòng kiểm tra lại.");
  return false;
}
function QuyenXuatExcel() {
  if (ntspermiss.xuatexcel) {
    return true;
  }
  NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác xóa. Vui lòng kiểm tra lại.");
  return false;
}
function UpdateLabelDangSD(selector) {
  UpdateLabel(selector, 'Đang sử dụng', 'Ngưng sử dụng');
}
function UpdateLabel(selector, text1, text2) {
  let _switch = $(selector);
  $("label[for='" + _switch.prop('id') + "']").html(_switch.prop('checked') ? text1 : text2);
}
function resetForm(selector) {
  $(selector).find('input:not([type="radio"]), textarea, select').each(function () {
    $(this).value('');
  });
  focusInput(selector);
}
function focusInput(selector) {
  $(selector + " :input:visible:enabled:first").focus();
}
//Thao tác sửa, xóa
function formaterbtnThaoTac(ID) {
  return `<div class="show-or-hide"><a class='text-primary btnSuaGrid1' title="Sửa" data='${ID}'><i class="fa fa-pencil"></i></a></b>&ensp;<a class='text-danger btnXoaGrid1' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
};
//Thao tác xóa
function formaterbtnThaoTacXoa(ID, btnXoaGrid1) {
  return `<div class="show-or-hide"><a class='text-danger ${btnXoaGrid1}' title="Xoá" data='${ID}'><i class='fa fa-trash-o'></i></a></div>`;
};
//Thao tác sửa
function formaterbtnThaoTacSua(ID, btnXoaGrid1) {
  return `<div class="show-or-hide"><a class='text-primary ${btnXoaGrid1}' title="Sửa" data='${ID}'><i class='fa fa-pencil'></i></a></div>`;
};
//Thao tác xem
function formaterbtnThaoTacXem(ID, btnXemGrid) {
  return `<div class="show-or-hide"><a class='text-success ${btnXemGrid}' title="Xem" data='${ID}'><i class='fa fa-eye'></i></a></div>`;
};
//Thao tác xem đính kèm
function formaterbtnXemDinhKem(ID, loai) {
  return `<div><button class='btn btn-sm btn-success text-xemdinhkem' title="Xem đính kèm" data='${ID}' onclick="XemDinhKem('${loai}','${ID}');return false;"><i class='fa fa-paperclip'></i>&ensp;Xem đính kèm</button></div>`;
};
function formaterbtnThaoTac3(ID, btnSuaGrid1, btnXoaGrid1) {
  return `<div class="show-or-hide"><a class='text-primary ${btnSuaGrid1}' title="Sửa" data='${ID}'><i class='fa fa-pencil'></i></a></b>&ensp;<a class='text-danger ${btnXoaGrid1}' title="Xoá" data='${ID}'><i class='fa fa-trash'></i></a></div>`;
};
//Thao tác in
function formaterbtnThaoTacIn(ID, btnInGrid) {
  return `<div class="show-or-hide"><a class='text-success ${btnInGrid}' title="In" data='${ID}'><i class='fa fa-print'></i></a></div>`;
};
//Thao tác xuất 
function formaterbtnThaoTacXuatExcel(ID, btnXuatGrid) {
  return `<div class="show-or-hide"><a class='text-warning ${btnXuatGrid}' title="Xuất" data='${ID}'><i class='fa fa-file-excel-o'></i></a></div>`;
};
function formaterTrangThai(value, ID) {
  return ` <div class="form-group d-flex justify-content-center">
  <label class="form-check form-switch">
    <input
      class="form-check-input checkTrangThai"
      permiss="sua"
      type="checkbox"
      data="${ID}"
      id="customCheckbox1${ID}"
      ${value ? 'checked' : ''}
    >
    <span class="form-check-label"></span>
  </label>
</div>`;
}
function CanhBaoXoa(DongY, Huy) {
  $.confirm({
    title: '<span style="font-size:20px" class="text-dark">Cảnh báo!</span>',
    type: 'red',
    icon: 'fa fa-warning',
    typeAnimated: true,
    theme: 'material',
    columnClass: 'col-md-5 col-md-offset-3 w-max-400px',
    content: "<p class=\"mb-1\">Bạn có thật sự muốn xóa dòng dữ liệu đã chọn không?</p><p class=\"mb-1\"> - Đồng ý xóa chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>",
    buttons: {
      confirm: {
        text: '<i class="fa fa-check"></i> Có',
        btnClass: 'btn-blue',
        keys: ['shift'],
        action: DongY
      },
      cancel: {
        text: '<i class="fa fa-close"></i> Không',
        btnClass: 'btn-danger',
        keys: ['enter', 'esc', 'space'],
        action: Huy
      }
    }
  });
}
function CanhBaoXoaNhieuDong(Xoa1Dong, XoaNhieuDong, Huy) {
  $.confirm({
    title: '<span class="text-dark" style="font-size:20px">Cảnh báo!</span>',
    type: 'red',
    icon: 'fa fa-warning',
    typeAnimated: true,
    theme: 'material',
    columnClass: 'col-md-7 col-md-offset-3 w-max-700px',
    content: "<p>Bạn có thật sự muốn xóa dòng dữ liệu không? <br/>- Xóa 1 dòng đang chọn chọn <b>'Xóa dòng chọn'</b><br/>- Xóa tất cả các dòng đang chọn chọn <b>'Xóa các dòng đang chọn'</b><br/>- Không đồng ý chọn <b>'Không'</b>",
    buttons: {
      confirm: {
        text: '<i class="fa fa-trash"></i> Xóa dòng chọn',
        btnClass: 'btn-blue',
        keys: ['enter'],
        action: Xoa1Dong
      },
      confirm2: {
        text: '<i class="fa fa-check"></i> Xóa các dòng đang chọn',
        btnClass: 'btn-warning',
        keys: ['enter', 'shift'],
        action: XoaNhieuDong
      },
      cancel: {
        text: '<i class="fa fa-close"></i> Không',
        btnClass: 'btn-red',
        keys: ['esc', 'space'],
        action: Huy
      }
    }
  });
}
function ThongBaoXuLyNhieuDong(content, MotDong, NhieuDong, text, Huy) {
  $.confirm({
    title: '<span class="text-dark" style="font-size:20px">Cảnh báo!</span>',
    type: 'red',
    icon: 'fa fa-warning',
    typeAnimated: true,
    theme: 'material',
    columnClass: 'col-md-8 col-md-offset-3 w-max-800px',
    content: content,
    buttons: {
      confirm: {
        text: '<i class="fa fa-check"></i> ' + text[0],
        btnClass: 'btn-blue',
        keys: ['enter'],
        action: MotDong
      },
      confirm2: {
        text: '<i style="vertical-align: middle; font-size: 20px;" class="bx bx-check-double"></i> ' + text[1],
        btnClass: 'btn-warning',
        keys: ['enter', 'shift'],
        action: NhieuDong
      },
      cancel: {
        text: '<i class="fa fa-close"></i> Không',
        btnClass: 'btn-red',
        keys: ['esc', 'space'],
        action: Huy
      }
    }
  });
}
function ThongBaoXuLyNhieuDongTheoKichThuoc(col, content, MotDong, NhieuDong, text, Huy) {
  $.confirm({
    title: '<span class="text-dark" style="font-size:20px">Cảnh báo!</span>',
    type: 'red',
    icon: 'fa fa-warning',
    typeAnimated: true,
    theme: 'material',
    columnClass: 'col-md-' + col + ' col-md-offset-3 w-max-800px',
    content: content,
    buttons: {
      confirm: {
        text: '<i class="fa fa-check"></i> ' + text[0],
        btnClass: 'btn-blue',
        keys: ['enter'],
        action: MotDong
      },
      confirm2: {
        text: '<i style="vertical-align: middle; font-size: 20px;" class="bx bx-check-double"></i> ' + text[1],
        btnClass: 'btn-warning',
        keys: ['enter', 'shift'],
        action: NhieuDong
      },
      cancel: {
        text: '<i class="fa fa-close"></i> Không',
        btnClass: 'btn-red',
        keys: ['esc', 'space'],
        action: Huy
      }
    }
  });
}
function checkTrangThai(selector, tenBang, tenCot, tenCotCapNhat, siteURL) {
  $(document).on('change', selector, function () {
    debugger
    var inp = $(this);
    var id = $(this).attr('data');
    //if (!QuyenSua()) {
    //  inp.prop('checked', !inp.prop('checked'));
    //  return false;
    //}
    $.confirm({
      title: '<span style="font-size:20px" class="text-dark">Cảnh báo!</span>',
      type: 'blue',
      icon: 'fa fa-question-circle',
      typeAnimated: true,
      theme: 'material',
      columnClass: 'col-md-5 col-md-offset-3 w-max-400px',
      content: NTS.CauCanhBaoDangSD,
      buttons: {
        confirm: {
          text: '<i class="fa fa-check"></i> Có',
          btnClass: 'btn-primary',
          keys: ['enter'],
          action: async function () {
            var saveData = {
              ID: id,
              TenBang: tenBang,
              TenCot: tenCot,
              TenCotCapNhat: tenCotCapNhat,
              GiaTri: inp.prop('checked')
            };
            var result = await NTS.getAjaxAsync('PUT', (siteURL || '') +  'api/DungChungs/updateTrangThai', { data: saveData });
            if (result.succeeded) {
              NTS.thanhcong(result.message);
            } else {
              NTS.canhbao(result.message);
              inp.prop('checked', !inp.prop('checked'));
            }
          }
        },
        cancel: {
          text: '<i class="fa fa-close"></i> Không',
          btnClass: 'btn-danger',
          keys: ['esc'],
          action: function () {
            inp.prop('checked', !inp.prop('checked'));
          }
        }
      }
    });

  })
}
function dinhDangSoLuoi(cell, formatterParams, onRendered) {
  var value = cell.getValue();//( dieukien ) ? ( đúng ) : ( sai );
  if (value != null) {
    if (value.toString().split(".").length > 1) {
      var formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".").replaceAll('.', ',');
    } else {
      var formattedValue = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    }
    return formattedValue.split(',')[0];
  } else {
    return "";
  }
}