﻿using NHATTAMID2025.Application.Common.Models;
using Microsoft.AspNetCore.Identity;

namespace NHATTAMID2025.Infrastructure.Identity;

public static class IdentityResultExtensions
{
    public static Result<object> ToApplicationResult(this IdentityResult result)
    {
        return result.Succeeded
            ? Result<object>.Success()
            : Result<object>.Failure(result.Errors.Select(e => e.Description));
    }
}
