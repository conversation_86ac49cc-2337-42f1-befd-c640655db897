﻿const baseUrl = "https://localhost:5001"; // 👈 sửa lại đúng địa chỉ baseUrl của bạn

$('#registerForm').on('submit', async function (e) {
  e.preventDefault();
  NTS.loadding()
  const TenDangNhap = $('#TenDangNhap').val().trim();
  const MatMa = $('#MatMa').val();

  // Validate đơn giản


  const data = {
    TenDangNhap: TenDangNhap, // hoặc bạn có thể tự định nghĩa tên đăng nhập
    MatKhau: MatMa
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', '/Hethong/DangNhap/login', data);
    debugger
    if (resDangKy.success) {
      NTS.thanhcong('Đăng nhập thành công!');
      const domain = window.location.origin;
      window.location.href = domain + '/danhmuc/trienkhaiphanmem';  // Thay '/dashboard' bằng URL bạn muốn
    } else {
      const index = resDangKy.message.split('_')[0];
      const message = resDangKy.message.split('_')[1];
      if (index === '1' || index === '2' || index === '3') {
        let htmlMessage = message;

        if (index === '2') {
          htmlMessage = `  <span >
              Vui lòng xác minh địa chỉ email của bạn để tiếp tục!
               <a href="/hethong/XatThucTaiKhoan" class="alert-link ms-1" style="text-decoration: underline; font-weight: 600;">
              Xác minh địa chỉ email
            </a>
            </span>
           `;

          sessionStorage.setItem('resetTenDangNhap', TenDangNhap);
          sessionStorage.setItem('resetMatMa', MatMa);
        }

        $('#loginAlert').removeClass('d-none').html(htmlMessage);
      }

    }
  } catch (error) {
    NTS.canhbao('Đăng nhập không thành công, vui lòng kiểm tra lại tài khoản đăng nhập!');
  }
  NTS.unloadding()
});

$('#TenDangNhap, #MatMa').on('input', function () {
  $('#loginAlert').addClass('d-none').text('');
});