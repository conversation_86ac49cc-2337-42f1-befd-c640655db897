﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.PhanMem.DTOs;
using NHATTAMID2025.Application.DanhMuc.PhanMem.Queries;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class PhanMem : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllPhanMem, "/getall")
            .MapGet(GetPhanMemById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<List<PhanMemDto>>?> GetAllPhanMem(ISender sender)
    => await sender.Send(new GetAllPhanMemCommand());
    public async Task<Result<List<PhanMemDto>>?> GetPhanMemById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetPhanMemByIDQuery(id));
}
