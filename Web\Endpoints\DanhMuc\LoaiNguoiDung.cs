﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.LoaiNguoiDung.Queries;
using NHATTAMID2025.Application.DanhMuc.LoaiNguoiDung.DTOs;
namespace NHATTAMID2025.Web.Endpoints.DanhMuc;
public class LoaiNguoiDung : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllLoaiNguoiDung, "/getall")
        ;
    }
    public async Task<Result<List<LoaiNguoiDungDto>>?> GetAllLoaiNguoiDung(ISender sender)
     => await sender.Send(new GetAllLoaiNguoiDungCommand());

}
