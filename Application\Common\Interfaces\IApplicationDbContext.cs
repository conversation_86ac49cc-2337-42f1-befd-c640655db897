﻿using System.Data.Common;
using NHATTAMID2025.Domain.Entities;
namespace NHATTAMID2025.Application.Common.Interfaces;


public interface IApplicationDbContext
{
    DbSet<NhanVien> Nhan<PERSON>ien { get; }
    DbSet<ChucVu> Chuc<PERSON>u { get; }
    DbSet<Users> Users { get; }
    DbSet<MaXacThucEntity> MaXacThuc { get; }
    DbSet<PhanMem> PhanMem { get; }
    DbSet<TrienKhaiPhanMem> TrienKhaiPhanMem { get; }
    DbSet<Tinh> Tinh { get; }
    DbSet<Xa> Xa { get; }
    DbSet<LoaiNguoiDung> LoaiNguoiDung { get; }
    DbSet<GoiBanQuyen> GoiBanQuyen { get; }
    DbSet<KeyBanQuyen> KeyBanQuyen { get; }
    DbSet<KichHoatKey> KichHoatKey { get; }
    DbSet<LienKetTaiKhoan> LienKetTaiKhoan { get; }
    DbSet<DangKyGiayPhep> DangKyGiayPhep { get; }
    DbSet<PhanQuyen> Phan<PERSON><PERSON><PERSON> { get; }
    DbSet<DonVi> DonVi { get; }

    DbSet<ChiaSeKey> ChiaSeKey { get; }
    DbSet<ThuTucHanhChinh> ThuTucHanhChinh { get; }
    DbSet<ThanhPhanHoSoTTHC> ThanhPhanHoSoTTHC { get; }
    DbSet<LinhVuc> LinhVuc { get; }
    Task<int> SaveChangesAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Executes a raw SQL non-query command (such as UPDATE, INSERT, DELETE).
    /// </summary>
    /// <param name="sql">The SQL command string.</param>
    /// <param name="parameters">Parameters to apply to the command.</param>
    /// <returns>The number of rows affected.</returns>
    Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters);

   
    Task<List<T>> ExecuteSqlQueryRawAsync<T>(
        string sql,
        Func<DbDataReader, T> map,
        bool proc,
        params DbParameter[] parameters
    );
}
