﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}

<style>
    .form-switch .form-check-input {
        transition: all 0.2s ease-in-out;
    }
    .form-switch .form-check-input:checked {
        background-color: #0054a6; /* <PERSON><PERSON><PERSON> xanh dương đậm khi bật */
    }
    .form-switch .form-check-input:not(:checked) {
        background-color: #d63939; /* <PERSON><PERSON>u đỏ khi tắt */
    }
    .highlight-section {
        background-color: #f8f9fa; /* Nền sáng nhẹ */
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #0054a6; /* Viền trái xanh dương nổi bật */
    }
    #ThoiHanLoaiinput .ts-wrapper.form-select.single {
        height: 30px !important;
        min-height: 30px !important;
        padding: 0 !important;
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
        margin-left:0px;
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        display: flex;
        align-items: center;
        border: 1px solid #dce9f5;
        background-color: #fff;
        font-size: 13px;
    }

    /* ✅ Khi focus (được click vào) */
    #ThoiHanLoaiinput .ts-wrapper.form-select.single.focus {
       border-color: #83b7e8 !important;
    }
</style>
<style>
    .badge.bg-success {
        background-color: #4caf50; /* màu xanh lá tươi, dễ chịu */
    }
    .badge.bg-danger {
        background-color: #e57373; /* màu đỏ nhạt dịu mắt */
    }
    /* icon select + padding giữ nguyên */
    .input-icon .form-select.ps-10 {
        padding-left: 2.75rem !important;
    }
    .input-icon-addon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
    }

</style>

<div class="card cardview" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">KEY BẢN QUYỀN</h2>
        </div>
        <div class="card-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-capKey" onclick="targetonclick()">
                Thêm mới
            </button>
        </div>
    </div>
    <div class="card-body">
        <table id="keyBanQuyenTable" class="table table-bordered table-hover custom-table" style="width: 100%">
            <thead>
                <tr>
                    <th>Trạng thái</th>
                    <th>Mã key</th>
                    <th>Người dùng</th>
                    <th>Phần mềm</th>
                    <th>Tên gói</th>
                    <th>Ngày cấp</th>
                    <th>Ngày hết hạn</th>
                    <th>Kích hoạt</th>

                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
</div>

<!-- Modal Thêm/Sửa Key Bản Quyền -->
<!-- Modal cấp key bản quyền - Tabler style -->
<div class="modal modal-blur fade" id="modal-capKey" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document" style="max-width:60%">
        <div class="modal-content">
            <form id="capKeyForm" class="card card-md">
                <div class="modal-header">
                    <h5 class="modal-title">Thông tin key bản quyền</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>
                <div class="card-body" style="padding: 1.5rem">
                    <div class="row">
                        <!-- Cột trái: Thông tin Key -->
                        <div class="col-md-6">
                           
                            
                            <div class="mb-3">
                                <label class="form-label">Triển khai phần mềm</label>
                                <select class="form-select" id="TrienKhaiPhanMemID" name="TrienKhaiPhanMemID"  required>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Gói bản quyền</label>
                                <select class="form-select" id="GoiBanQuyenID" name="GoiBanQuyenID" required>
                                    <option value="">-- Chọn gói bản quyền --</option>
                                </select>
                            </div>


                            <div class="row g-2">
                                <div class="col-6">
                                    <label class="form-label">Ngày tạo</label>
                                    <input type="date" class="form-control" id="NgayTao" name="NgayTao" disabled>
                                </div>
                                <div class="col-6">
                                    <label class="form-label">Ngày hết hạn</label>
                                    <input type="date" class="form-control" id="NgayHetHan" name="NgayHetHan" disabled>
                                </div>
                            </div>
                            <div class="row g-2 mt-3">
                                <div class="col-6">
                                    <label class="form-label">Giới hạn thiết bị</label>
                                    <input type="number" class="form-control" id="GioiHanKichHoat" name="GioiHanKichHoat" value="0" disabled>
                                </div>
                                <div class="col-6" style="display:none">
                                    <label class="form-label">Số lần kích hoạt</label>
                                    <input type="number" class="form-control" id="SoLanKichHoat" name="SoLanKichHoat" value="0" disabled>
                                </div>
                                <div class="col-6 ">
                                    <label class="form-label">Trạng thái</label>
                                    <label class="form-check form-switch" style="background-color: #f8f9fa;
                                                                                    padding: 5px;
                                                                                    border-radius: 5px;
                                                                                    border-left: 4px solid #0054a6;">
                                        <input class="form-check-input" type="checkbox" id="TrangThai" name="TrangThai" checked style="margin-left:5px; margin-top: 2px">
                                        <span class="status-label" id="modal-statusLabel" style="margin-left:5px">Mở key</span>
                                    </label>
                                </div>
                            </div>
                          
                            <div class="mb-6" style="display:none">
                                <label class="form-label">Mã Key</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="MaKey" name="MaKey"  >
                                    <button class="btn btn-outline-primary" type="button" id="btnAutoKey">Tạo tự động</button>
                                </div>
                            </div>
                        </div>

                        <!-- Cột phải: Người dùng -->
                        <div class="col-md-6">
                            <div class="mb-4">
                                <label class="form-label fw-semibold">Người dùng</label>
                             @*    <div class="input-icon mb-2">
                                    <span class="input-icon-addon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-users" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="#3a86ff" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                            <circle cx="9" cy="7" r="4" />
                                            <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                                            <circle cx="17" cy="7" r="4" />
                                            <path d="M21 21v-2a4 4 0 0 0 -3 -3.85" />
                                        </svg>
                                    </span>
                                    <select class="form-select ps-10" id="NguoiDungID" name="NguoiDungID" onchange="hienThongTinNguoiDung(this.value)" required>
                                        <option value="">-- Chọn người dùng --</option>
                                    </select>
                                </div> *@

                                <select class="form-select" id="NguoiDungID" name="NguoiDungID" required>
                                </select>
                            </div>

                            <div class="card shadow-sm rounded-3">
                                <div class="card-body">
                                    <h5 class="card-title mb-3 fw-bold">Thông tin người dùng</h5>
                                    <div id="ThongTinNguoiDungList" style="min-height:150px; transition: opacity 0.3s ease;">
                                        <div class="d-flex justify-content-between align-items-center border-bottom py-2" style="height: 36px;">
                                            <span class="form-label mb-0">Họ tên:</span>
                                            <span id="HoTenNguoiDung" class="text-primary fw-semibold">—</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center border-bottom py-2" style="height: 36px;">
                                            <span class="form-label mb-0">Email:</span>
                                            <span id="EmailNguoiDung" class="text-muted">—</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center border-bottom py-2" style="height: 36px;">
                                            <span class="form-label mb-0">SĐT:</span>
                                            <span id="SoDienThoaiNguoiDung" class="text-muted">—</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center border-bottom py-2" style="height: 36px;">
                                            <span class="form-label mb-0">Địa chỉ:</span>
                                            <span id="DiaChiNguoiDung" class="text-muted">—</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center py-2" style="height: 36px;">
                                            <span class="form-label mb-0">Trạng thái:</span>
                                            <span id="TrangThaiNguoiDung" class="text-success fw-semibold">—</span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="ti ti-device-floppy"></i> Tạo key
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x"></i> Hủy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Hidden để lưu ID -->
<input type="hidden" id="KeyID" />
<script src="~/scripts/nghiepvu/KeyBanQuyen.js"></script>
