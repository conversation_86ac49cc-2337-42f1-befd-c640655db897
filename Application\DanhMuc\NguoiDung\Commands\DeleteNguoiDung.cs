﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands;
public record DeleteNguoiDungCommand(
    string UserID
    ) : IRequest<Result<NguoiDungDto>?>;

public class DeleteCommandValidator : AbstractValidator<DeleteNguoiDungCommand>
{
    public DeleteCommandValidator()
    {
    }
}

public class DeleteNguoiDungCommandHandler : IRequestHandler<DeleteNguoiDungCommand, Result<NguoiDungDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<NguoiDungDto>?> Handle(DeleteNguoiDungCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_NguoiDung_Delete",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<NguoiDungDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new NguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
        };
    }
}
