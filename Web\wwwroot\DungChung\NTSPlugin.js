﻿$(function () {
  $('.menu-is-opening .nav-link').addClass("active");
  $('.nav-treeview .nav-item .nav-toggle').removeClass("active");
  //var Toast = Swal.mixin({
  //    toast: true,
  //    position: 'top-end',
  //    showConfirmButton: false,
  //    timer: 3000
  //});
  $.fn.tolist = function (options) {
    if (typeof $(this).val() == "undefined")
      NTS.loi("Không tìm thấy control" + this.selector);
    var defaults = {
      ajaxUrl: '', //Đường dẫn lấy dữ liệu
      ajaxParam: '', //tham số 
      indexValue: 0,
      indexText: 2
    };
    var settings = $.extend(defaults, options);
    var data = NTS.getdata({
      ajaxUrl: settings.ajaxUrl,
      ajaxParam: settings.ajaxParam,
    });
    var elements;
    if (data.d != null) {
      elements = $.map(data.d, function (e) {
        return [$.map(e, function (v) {
          return v;
        })];
      });
    } else {
      elements = new Array();
    }

    $(this).attr("type", "list");
    $(this).html('');
    if (data.d != null)
      for (i = 0; i < elements.length; i++) {
        $(this).append('<li><label class="middle"><input type="checkbox" class="ace" value="' + elements[i][settings.indexValue] + '"/><span class="lbl"> ' + elements[i][settings.indexText] + '</span></label></li>');
      }
    /*$(this).prepend('<input type="text" class="form-control input-sm" onkeyup="$(this).search();" for="' + $(this).attr("id") + '" placeholder="Nhập nội dung tìm kiếm..." />');*/
  }
  $.fn.search = function () {
    if (typeof ($(this).context.attributes["for"]) == 'undefined')
      NTS.loi("Control không được cài đặt để điều khiển bất kì list nào!");
    var key = $(this).value();
    $("#" + $(this).context.attributes["for"].nodeValue + " li").each(function (index) {
      if ($(this).find(".lbl").html().toLowerCase().indexOf(key.toLowerCase()) > -1) {
        $(this).show();
      }
      else {
        $(this).hide();
      }
    });
  };
  $.fn.value = function (data) {
    if (typeof $(this).val() == "undefined")
      NTS.loi("Không tìm thấy control" + this.selector);
    if (typeof data !== "undefined") {
      //set giá trị
      if ($(this).prop('type') == 'text') {
        if ($(this).attr('class').lastIndexOf('date-picker') != -1) {
          //set gia tri ngay thang
          $(this).datepicker({ dateFormat: "dd/mm/yyyy" }).datepicker("setDate", data);
        }
        else if ($(this).attr('class').lastIndexOf('number-format') != -1) {
          $(this).val((data + "").replace(".", ",")).trigger('change');
        }
        else if ($(this).attr('class').lastIndexOf('number-table-nts') != -1) {
          $(this).val((data + "").replace(".", ",")).trigger('change');
        }
        else {
          $(this).val(data);
        }
      }
      else if ($(this).prop('type').search('select') != -1) {
        if (data == '0' || data == null || data == '0' || data == "00000000-0000-0000-0000-000000000000")
          $(this).val('').trigger('change');
        else {
          $(this).val(data).trigger('change');
        }
      }
      else if ($(this).prop('type') == 'checkbox' || $(this).prop('type') == 'radio') {
        $(this).prop('checked', data);
      }
      else if ($(this).prop('type') == 'list') {
        var elements = $.map(data, function (e) {
          return [$.map(e, function (v) {
            return v;
          })];
        });
        //elements[i][settings.indexValue] 

        $($(this).selector + " li").each(function (index) {
          for (i = 0; i < elements.length; i++) {
            if ($(this).find("input:checkbox")[0].value == elements[i][0]) {
              $(this).find("input:checkbox").prop("checked", true);
              break;
            }
            else
              $(this).find("input:checkbox").prop("checked", false);
          }
        });

      }
      else {
        $(this).val(data);
      }
    }
    else {
      //Lấy giá trị
      if ($(this).prop('type') == 'text')
        return $(this).val();//($(this).val() == "0" ? "" : $(this).val());
      else if ($(this).prop('type').search('select') != -1) {

        if ($(this).val() == null || $(this).val() == "0" || $(this).val() == "00000000-0000-0000-0000-000000000000")
          return "";
        else
          return $(this).val();
      }
      else if ($(this).prop('type') == 'checkbox' || $(this).prop('type') == 'radio') {
        return $(this).prop('checked');
      }
      else if ($(this).prop('type') == 'list') {
        var rtn = [];
        $($(this).selector + " li").each(function (index) {
          if ($(this).find("input:checkbox").prop("checked") == true) {
            rtn.push({
              "id": $(this).find("input:checkbox")[0].value, //khóa chính
              "value": $(this).find(".lbl").html(),//Text hiển thị 
            });
          }
        });
        return rtn;
      }
      else
        return $(this).val();
    }
  }
  // Hàm kiểm tra xem có đúng định dạng ngày hay không
  $.fn.isdateformat = function (frmt) {
    var value = $(this).val().substring(8, 10) + "/" + $(this).val().substring(5, 7) + "/" + $(this).val().substring(0, 4);
    $(this).val(value);
  }
  // Kiểm tra chuỗi có rỗng hay không
  $.fn.isempty = function () {
    value = $(this).val();
    var rtn = false;
    if (value == null || value == "")
      rtn = true;
    return rtn;
  }
  // Kiểm tra chuỗi có rỗng hay không + thông báo lỗi
  $.fn.isempty = function (message) {
    value = $(this).val();
    var rtn = false;
    if (value == null || value == "") {
      NTS.loi(message);
      rtn = true;
    }
    return rtn;
  }
  $.fn.isdate = function (message) {
    value = $(this).val();
    var rtn = false;
    if (value == null || value == "") {
      NTS.loi(message);
      rtn = true;
    }
    return rtn;
  }
  $.fn.taoSoCT = function (url) {
    var data = NTS.getdata(
      {
        ajaxUrl: url,
        ajaxParam: ''
      });
    if (data.length != 0) {
      $(this).val(JSON.parse(data.d))
    }
  }
  $.fn.taoMa = function (col, tab) {
    $(this).val('');
    var obj = new Array();
    obj[0] = col;
    obj[1] = tab;
    var data = NTS.getdata(
      {
        ajaxUrl: '/Services/ServiceSystem.asmx/taoMaTuTang',
        ajaxParam: obj
      });
    if (data.length != 0) {

      $(this).val(JSON.parse(data.d));
    }

  }
  //$('.tabulator-page-size').select2({ width: "55px" });
  $('.select2').each(function () {
    $(this).select2({
      theme: "bootstrap-5",
      dropdownParent: $(this).parent(), // fix select2 search input focus bug
    })
  })

  // fix select2 bootstrap modal scroll bug
  $(document).on('select2:close', '.my-select2', function (e) {
    var evt = "scroll.select2"
    $(e.target).parents().off(evt)
    $(window).off(evt)
  });

  // tắt loading khi load xong trang
  NTS.unloadding();

});

var NTS = {
  // phân quyền
  permiss: function (func, table, value) {
    var rtn = false;
    //
    switch (func) {
      case 'sua':
        if (!ntspermiss.sua)
          NTS.canhbao('Bạn không được cấp quyền thực hiện chức năng sửa!');
        rtn = true;
        break;
      case 'xoa':
        if (!ntspermiss.sua)
          NTS.canhbao('Bạn không được cấp quyền thực hiện chức năng xóa!');
        rtn = true;
        break;
    }
    return rtn
  },
  loadding: function (obj) {
    var originWeb = window.location.origin;
    $('#Loadding').html('<div class="message-loading-overlay"><img class="ace-icon" src="' + originWeb + '/Images/loading.gif" width="48" height="48" alt="Vui lòng chờ..." /></div>');

    $('#Loadding').show();
  },
  unloadding: function (obj) {
    $('#Loadding').html('').hide();
  },
  // Thông báo thành công
  thanhcong: function (obj) {
    toastr.success(obj);
  },
  // Thông báo cảnh báo
  canhbao: function (obj) {
    toastr.warning(obj);
  },
  // Thông báo lỗi
  loi: function (obj) {
    toastr.error(obj);
  },
  // Đóng tất cả thông báo trên màn hình
  dongthongbao: function () {
    //$('.gritter-item-wrapper').remove();
    //$('.bs-toast').hide();
    toastr.remove();
  },

  upload: function (type, url, data) {
    var result = null;

    $.ajax({
      url: url,
      type: type,
      data: data,
      processData: false,
      contentType: type === 'DELETE' ? 'application/json' : false, // Dùng application/json cho DELETE
      async: false,
      success: function (response) {
        //console.log(`${type} thành công:`, response);
        result = response;
        if (type === 'POST') {
          NTS.thanhcong('Tải file thành công!');
        } else if (type === 'DELETE') {
          NTS.thanhcong('Xóa file thành công!');
        }
      },
      error: function (xhr, status, error) {
        NTS.canhbao(`Lỗi khi ${type}:`, error);
        //alert(`Lỗi khi ${type === 'POST' ? 'upload' : 'xóa'} file. Chi tiết: ${xhr.responseText}`);
        result = null;
      }
    });

    return result;
  },
  // Lấy dữ liệu từ sever
  getdata: function (options) {
    //Khởi tạo biến return
    var result;
    var defaults = {
      ajaxUrl: '',
      ajaxParam: {},
      ajaxParamObject: false
    };
    var settings = $.extend(defaults, options);
    if (!$.isEmptyObject(settings.ajaxParam)) {
      //result = { d: this.getAjax('string', settings.ajaxUrl, { data: settings.ajaxParam }) }; code gốc
      result = { d: this.getAjax(settings.ajaxUrl, settings.ajaxParam) };
    } else {
      //result = { d: this.getAjax('string', settings.ajaxUrl, { data: settings.ajaxParam }) };
      result = { d: this.getAjax(settings.ajaxUrl, {}) };
    }

    return result;
  },
  getstring: function (options) {
    var result;
    var defaults = {
      ajaxUrl: '',
      ajaxParam: {},
    };
    var settings = $.extend(defaults, options);
    if (!$.isEmptyObject(settings.ajaxParam)) {
      //result = { d: this.getAjax('string', settings.ajaxUrl, { data: settings.ajaxParam }) };
      result = { d: this.getAjax(settings.ajaxUrl, settings.ajaxParam) };
    } else {
      result = { d: this.getAjax(settings.ajaxUrl, {}) };
    }
    return result;
  },
  // Thực thi với sever
  // Trả về dạng {0|1}_{Nội dung}
  exec: function (options) {
    var result = false;
    var defaults = {
      ajaxUrl: '',
      ajaxParam: {},
    };
    var settings = $.extend(defaults, options);
    if (!$.isEmptyObject(settings.ajaxParam)) {
      var data = this.getAjax(settings.ajaxUrl, settings.ajaxParam);
      try {
        if (data.split('_')[0] == '1') {
          NTS.thanhcong(data.split('_')[1]);
          result = true;
        }
        else {
          NTS.loi(data.split('_')[1]);
          result = false;
        }
      } catch (e) {
        result = false;
      }
    }
    else {
      var data = this.getAjax(settings.ajaxUrl, {});
      try {
        if (data.split('_')[0] == '1') {
          NTS.thanhcong(data.split('_')[1]);
          result = true;
        }
        else {
          NTS.loi(data.split('_')[1]);
          result = false;
        }
      } catch (e) {
        result = false;
      }
    }
    return result;
  },
  //Hàm dùng để load dữ liệu từ combobox
  //name: '' -> ID select
  //ajaxUrl: '' -> Đường dẫn lấy dữ liệu
  //ajaxParam: '' -> tham số
  //indexValue: 0 -> index cột lấy làm value
  //indexText: 1 -> index cột lấy làm text
  //indexText1: 0 -> index cột lấy làm mã khi combo 2 cột
  //indexDefault: 0 -> index mặc định khi load combo
  //columns: 1 -> số lượng cột của combo
  //textChange: "text1" -> Khi chọn combo cột nào sẽ hiển thị {id,text,text1}
  loadDataCombo: function (options) {
    setTimeout(() => {
      var defaults = {
        name: '',
        ajaxUrl: '',
        ajaxParam: '',
        indexValue: 0,
        indexText: 1,
        indexText1: 0,
        indexText2: 2,
        indexDefault: 0,
        columns: 1,
        textChange: "text1",
        showTatCa: false,
        textShowTatCa: 'Tất Cả',
        hideshowTatCa: false
      };
      var settings = $.extend(defaults, options);

      if (!$(settings.name).length) {
        NTS.loi('Không tồn tại control ' + settings.name + ' cho hàm loadDataCombo');
        return;
      }

      // Gọi API bằng NTS.getAjax
      var data = NTS.getAjax('GET', settings.ajaxUrl, { data: settings.ajaxParam });

      var elements = [];
      try {
        // Trường hợp 1: data là mảng trực tiếp
        if (Array.isArray(data) && data.length > 0) {
          // Kiểm tra nếu phần tử đầu tiên là mảng hoặc object
          if (Array.isArray(data[0])) {
            elements = data; // Đã là mảng các mảng, dùng trực tiếp
          } else if (typeof data[0] === 'boolean' && data[1] && typeof data[1] === 'object') {
            // Trường hợp đặc biệt: [true, {object}]
            elements = [Object.values(data[1])];
          } else {
            elements = $.map(data, function (e) {
              if (e && typeof e === 'object' && !Array.isArray(e)) {
                return [Object.values(e)]; // Chuyển object thành mảng các giá trị
              }
              return null;
            }).filter(function (e) {
              return e !== null;
            });
          }
        }
        // Trường hợp 2: data là object và có thuộc tính data là mảng
        else if (data && typeof data === 'object' && Array.isArray(data.data) && data.data.length > 0) {
          elements = $.map(data.data, function (e) {
            return [Object.values(e)];
          });
        }
        // Trường hợp 3: data là object và có thuộc tính result là mảng
        else if (data && typeof data === 'object' && Array.isArray(data.result) && data.result.length > 0) {
          elements = $.map(data.result, function (e) {
            return [Object.values(e)];
          });
        }
        // Trường hợp 4: data là object đơn (không phải mảng)
        else if (data && typeof data === 'object' && !Array.isArray(data)) {
          // Chuyển object thành mảng chứa một object
          var dataAsArray = [data];
          // Ánh xạ dataAsArray vào elements
          elements = $.map(dataAsArray, function (e) {
            return [Object.values(e)]; // Chuyển object thành mảng các giá trị
          });
          // Bỏ phần tử đầu tiên trong mảng elements
          if (elements.length > 0) {
            elements.shift();
          }
        }
      } catch (e) {
      }


      $(settings.name).html('');
      // Xử lý dữ liệu dựa trên số cột
      if (settings.columns === 1) {
        var newDataSource = [{
          "id": "-1",
          "text": "",
          "disabled": true
        }];
        if (settings.showTatCa) {
          newDataSource.push({
            "id": "",
            "text": settings.textShowTatCa,
            "disabled": false
          });
        }

        try {
          for (var i = 0; i < elements.length; i++) {
            if (elements[i][settings.indexValue].toString() !== "") {
              newDataSource.push({
                "id": elements[i][settings.indexValue], // Cột ID
                "text": elements[i][settings.indexText] || "Không có tên", // Cột tên
                "disabled": false
              });
            }
          }
        } catch (e) {
          console.error("Error mapping elements to newDataSource:", e);
        }

        $(settings.name).select2({
          width: "100%",
          data: newDataSource,
          language: { noResults: function () { return "Không tìm thấy thông tin!"; } },
          templateResult: function (data) {
            if (!data.id) return data.text;
            return $('<span>' + data.text + '</span>');
          },
          templateSelection: function (data) {
            return data.text;
          },
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true
        });
      }
      // Phần xử lý columns = 2, columns = 3 giữ nguyên
      else if (settings.columns === 2) {
        var newDataSource = [{
          "id": "-1",
          "text": "",
          "text1": "",
          "disabled": true
        }];
        if (settings.showTatCa && !settings.hideshowTatCa) {
          newDataSource.push({
            "id": "",
            "text": settings.textShowTatCa,
            "text1": settings.textShowTatCa,
            "disabled": false
          });
        }

        try {
          for (var i = 0; i < elements.length; i++) {
            if (elements[i][settings.indexValue].toString() !== "") {
              newDataSource.push({
                "id": elements[i][settings.indexValue],
                "text": elements[i][settings.indexText] || "Không có tên",
                "text1": elements[i][settings.indexText1] || "",
                "disabled": false
              });
            }
          }
        } catch (e) {
          console.error("Error mapping elements to newDataSource (columns=2):", e);
        }

        $(settings.name).select2({
          width: "100%",
          data: newDataSource,
          language: { noResults: function () { return "Không tìm thấy thông tin!"; } },
          templateResult: function (data) {
            if (!data.id) {
              return $('<div class="row"><div class="col-4">' + settings.textShowTatCa + '</div><div class="col-8">' + settings.textShowTatCa + '</div></div>');
            }
            return $('<div class="row"><div class="col-4">' + data.text + '</div><div class="col-8">' + data.text1 + '</div></div>');
          },
          templateSelection: function (data) {
            return settings.textChange === "text1" ? data.text1 : data.text;
          },
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true
        });
      }
      // Phần xử lý columns = 3 giữ nguyên

      $(settings.name).on("select2:open", function (e) {
        try {
          var ID = $(this)[0].id;
          var addNew = document.getElementById(ID).getAttribute("add-new");
          if (!$('#' + ID + 'AddNew').length && addNew.toUpperCase() === "TRUE") {
            var a = $(this).data('select2');
            a.$results.parents('.select2-results')
              .append('<button id="' + ID + 'AddNew" permiss="them" onclick="' + ID + 'AddNew(); return false;" class="btn btn-success select2-link"><i class="fa fa-plus"></i> Thêm mới </button>')
              .on('click', function (b) {
                a.trigger('close');
              });
          }
        } catch (e) {
          console.error("Error in select2:open event:", e);
        }
      });

      $(settings.name + ' :nth-child(' + settings.indexDefault + ')').prop('selected', true).trigger('change');
    }, 200);
  },
  loadDataComboTableNTS: function (options) {

    var defaults = {
      //ID select
      name: '',
      data: [], //Đường dẫn lấy dữ liệu
      indexValue: 0, //index cột lấy làm value
      indexText: 1,//index cột lấy làm text
      indexText1: 0,//index cột lấy làm mã khi combo 2 cột
      indexText2: 2,//index cột lấy làm mã khi combo 3 cột
      indexDefault: 0,//index mặc định khi load combo
      columns: 1,//số lượng cột của combo
      textChange: "text1",//Khi chọn combo cột nào sẽ hiển thị {id,text,text1}
      showTatCa: false,
      textShowTatCa: 'Tất cả',
      hideshowTatCa: false
    };
    var settings = $.extend(defaults, options);
    if (!$(settings.name)) {
      NTS.loi('Không tồn tại control ' + settings.name + ' cho hàm loadDataCombo');
    }

    var data = settings.data

    var elements;
    if (!$.isEmptyObject(data.d) && !data.d.Err && data.d.Result.length != 0)
      elements = $.map(data.d.Result, function (e) {
        return [$.map(e, function (v) {
          return v;
        })];
      });
    $(settings.name).html('');
    if (settings.columns == 3) {
      //Tạo 1 source mẫu theo options select2
      var newDataSource = [{
        "id": "-1",
        "text": "",
        "text1": "",
        "text2": "",
        "disabled": true
      }
        , {
        "id": "",
        "text": (settings.showTatCa ? settings.textShowTatCa : ""),
        "text1": (settings.showTatCa ? settings.textShowTatCa : ""),
        "text2": (settings.showTatCa ? settings.textShowTatCa : ""),
        "disabled": false
      }];
      if (settings.hideshowTatCa) {
        newDataSource.length = 0;
        newDataSource = [{
          "id": "-1",
          "text": "",
          "text1": "",
          "text2": "",
          "disabled": true
        }];
      }
      try {
        //Chuyển source vào source mẫu
        for (i = 0; i < elements.length; i++) {
          if (elements[i][settings.indexValue].toString() != "")
            newDataSource.push({
              "id": elements[i][settings.indexValue], //khóa chính
              "text": elements[i][settings.indexText],//Text hiển thị
              "text1": elements[i][settings.indexText1],//Mã hiển thị
              "text2": elements[i][settings.indexText2],//Mã hiển thị
              "disabled": (data.d.Result[i]['disabled'] == "1" ? true : false)
            });
        }
      } catch (e) {
        //console.log(e.message);
      }
      //Combo2 cột người dùng chọn hiển thị mã
      if (settings.textChange == "text2") {
        $(settings.name).select2({
          width: "100%",
          dropdownCssClass: settings.menuWidth,
          data: newDataSource,
          language: {
            noResults: function () {
              return "Không tìm thấy thông tin!";
            }
          },
          templateResult: templateResult2,
          templateSelection: templateSelection_ChangeText2,
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true
        });
      }
      else
        if (settings.textChange == "text1") {
          $(settings.name).select2({
            width: "100%",
            dropdownCssClass: settings.menuWidth,
            data: newDataSource,
            language: {
              noResults: function () {
                return "Không tìm thấy thông tin!";
              }
            },
            templateResult: templateResult2,
            templateSelection: templateSelection_ChangeText1,
            escapeMarkup: function (m) { return m; },
            matcher: matcher,
            tags: true
          });
        }
        else {
          //Combo2 cột người dùng chọn hiển thị tên
          $(settings.name).select2({
            width: "100%",
            data: newDataSource,
            language: {
              noResults: function () {
                return "Không tìm thấy thông tin!";
              }
            },
            templateResult: templateResult2,
            templateSelection: templateSelection,
            escapeMarkup: function (m) { return m; },
            matcher: matcher,
            tags: true
          });
        }
    }
    else if (settings.columns == 2) {
      //Tạo 1 source mẫu theo options select2
      var newDataSource = [{
        "id": "-1",
        "text": "",
        "text1": "",
        "disabled": true
      }
        , {
        "id": "",
        "text": (settings.showTatCa ? settings.textShowTatCa : ""),
        "text1": (settings.showTatCa ? settings.textShowTatCa : ""),
        "disabled": false
      }];
      if (settings.hideshowTatCa) {
        newDataSource.length = 0;
        newDataSource = [{
          "id": "-1",
          "text": "",
          "text1": "",
          "disabled": true
        }];
      }
      try {
        //Chuyển source vào source mẫu
        for (i = 0; i < elements.length; i++) {
          if (elements[i][settings.indexValue].toString() != "")
            newDataSource.push({
              "id": elements[i][settings.indexValue], //khóa chính
              "text": elements[i][settings.indexText],//Text hiển thị
              "text1": elements[i][settings.indexText1],//Mã hiển thị
              "disabled": (data.d.Result[i]['disabled'] == "1" ? true : false)
            });
        }
      } catch (e) {
        //console.log(e.message);
      }
      //Combo2 cột người dùng chọn hiển thị mã
      if (settings.textChange == "text1") {
        $(settings.name).select2({
          width: "100%",
          dropdownCssClass: settings.menuWidth,
          data: newDataSource,
          language: {
            noResults: function () {
              return "Không tìm thấy thông tin!";
            }
          },
          templateResult: templateResult1,
          templateSelection: templateSelection_ChangeText1,
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true
        });
      }
      else {
        //Combo2 cột người dùng chọn hiển thị tên
        $(settings.name).select2({
          width: "100%",
          data: newDataSource,
          language: {
            noResults: function () {
              return "Không tìm thấy thông tin!";
            }
          },
          templateResult: templateResult,
          templateSelection: templateSelection,
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true
        });
      }
      //select.$results.parents('.select2-results')
      //                        .append('<button class="btn btn-info select2-link"><i class="fa fa-plus"></i> Thêm mới </button>');
    } else {
      //Tạo 1 source mẫu theo options select2
      var newDataSource = [{
        "id": "",
        "text": (settings.showTatCa ? settings.textShowTatCa : ""),
        "disabled": !1
      }];
      if (settings.hideshowTatCa) {
        newDataSource.length = 0;
      }
      try {
        //Chuyển source vào source mẫu elements[i][settings.indexValue]
        for (i = 0; i < elements.length; i++) {
          {
            if (elements[i][settings.indexValue].toString() != "") {
              newDataSource.push({
                "id": elements[i][settings.indexValue], //khóa chính
                "text": elements[i][settings.indexText],//Text hiển thị
                "disabled": (data.d.Result[i]['disabled'] == "1" ? true : false)
              });
            }
          }
        }
      } catch (e) {
      }
      //
      $(settings.name).select2({
        width: "100%",
        dropdownCssClass: settings.menuWidth,
        data: newDataSource,
        language: {
          noResults: function () {
            return "Không tìm thấy thông tin!";
          }
        },
      });
    };
    $(settings.name).on("select2:open", function (e) {
      //Tạo nút thêm mới
      try {
        var ID = $(this)[0].id;
        var addNew = document.getElementById(ID).getAttribute("add-new");
        if (!$('#' + ID + 'AddNew').length) {
          if (addNew.toUpperCase() == "true".toUpperCase()) {
            var a = $(this).data('select2');
            if (a.id == "select2-" + ID);
            {
              //console.log(a.$results.parents());
              var x = a.$results.parents('.select2-results')[0].innerHTML;;
              a.$results.parents('.select2-results')
                .append('<button id="' + ID + 'AddNew" permiss="them" onclick="' + ID + 'AddNew(); return false;" class="btn btn-success select2-link"><i class="fa fa-plus"></i> Thêm mới </button>')
                .on('click', function (b) {
                  a.trigger('close');
                });
            }
          }
        }
      } catch (e) {
      }
    });
    $(settings.name + ' :nth-child(' + settings.indexDefault + ')').prop('selected', true).change();
  },
  loadDataCombo_v1: function (options) {
    var defaults = {
      //ID select
      name: '',
      ajaxUrl: '', //Đường dẫn lấy dữ liệu
      ajaxParam: '', //tham số
      indexValue: 0, //index cột lấy làm value
      indexText: 1,//index cột lấy làm text
      indexText1: 0,//index cột lấy làm mã khi combo 2 cột
      indexDefault: 0,//index mặc định khi load combo 
      columns: 1,//số lượng cột của combo
      textChange: "text1",//Khi chọn combo cột nào sẽ hiển thị {id,text,text1}
      showTatCa: false,
      textShowTatCa: 'Tất cả',
      source: null // Mảng array
    };
    var settings = $.extend(defaults, options);
    if (!$(settings.name)) {
      NTS.loi('Không tồn tại control ' + settings.name + ' cho hàm loadDataCombo');
    }
    var data = null;
    if (settings.source != null && !$.isEmptyObject(settings.source)) {
      data = JSON.parse(settings.source);
    }
    else {
      data = NTS.getAjax_v1(settings.ajaxUrl, { data: settings.ajaxParam }).Result.Table;
    }
    //if (settings.ajaxParam != '')
    //    data = NTS.getAjax_v1(settings.ajaxUrl, { data: settings.ajaxParam });
    //else
    //    data = NTS.getAjax_v1(settings.ajaxUrl, {}); 
    if (data != null) {
      data = data.Result.Table;
      var elements = $.map(data, function (e) {
        return [$.map(e, function (v) {
          return v;
        })];
      });
    }
    else {
      data = [];
    }
    $(settings.name).html('');

    if (settings.columns == 2) {
      //Tạo 1 source mẫu theo options select2
      var newDataSource = [{
        "id": "-1",
        "text": "",
        "text1": "",
        "disabled": true
      }
        , {
        "id": "",
        "text": (settings.showTatCa ? settings.textShowTatCa : ""),
        "text1": (settings.showTatCa ? settings.textShowTatCa : ""),
        "disabled": false
      }]
      try {
        //Chuyển source vào source mẫu
        for (i = 0; i < elements.length; i++) {

          if (elements[i][settings.indexValue].toString() != "")

            newDataSource.push({
              "id": elements[i][settings.indexValue], //khóa chính
              "text": elements[i][settings.indexText],//Text hiển thị
              "text1": elements[i][settings.indexText1],//Mã hiển thị
              "disabled": (data[i]['disabled'] == "1" ? true : false)
            });
        }
      } catch (e) {
        //console.log(e.message);
      }
      //Combo2 cột người dùng chọn hiển thị mã
      if (settings.textChange == "text1") {
        $(settings.name).select2({
          width: "100%",
          dropdownCssClass: settings.menuWidth,
          data: newDataSource,
          language: {
            noResults: function () {
              return "Không tìm thấy thông tin!";
            }
          },
          templateResult: templateResult1,
          templateSelection: templateSelection_ChangeText1,
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true,
          dir: "ltr",
        });
      }
      else {
        //Combo2 cột người dùng chọn hiển thị tên
        $(settings.name).select2({
          width: "100%",
          data: newDataSource,
          language: {
            noResults: function () {
              return "Không tìm thấy thông tin!";
            }
          },
          templateResult: templateResult,
          templateSelection: templateSelection,
          escapeMarkup: function (m) { return m; },
          matcher: matcher,
          tags: true,
          dir: "ltr",
        });
      }

      //select.$results.parents('.select2-results')
      //                        .append('<button class="btn btn-info select2-link"><i class="fa fa-plus"></i> Thêm mới </button>');
    } else {
      //Tạo 1 source mẫu theo options select2
      var newDataSource = [{
        "id": "",
        "text": (settings.showTatCa ? settings.textShowTatCa : ""),
        "disabled": !1
      }]
      try {
        //Chuyển source vào source mẫu elements[i][settings.indexValue]
        for (i = 0; i < elements.length; i++) {
          {
            if (elements[i][settings.indexValue].toString() != "") {
              newDataSource.push({
                "id": elements[i][settings.indexValue], //khóa chính
                "text": elements[i][settings.indexText],//Text hiển thị 
                "disabled": (data[i]['disabled'] == "1" ? true : false)
              });
            }
          }
        }
      } catch (e) {

      }
      //
      $(settings.name).select2({
        width: "100%",
        dropdownCssClass: settings.menuWidth,
        data: newDataSource,
        language: {
          noResults: function () {
            return "Không tìm thấy thông tin!";
          }
        },
      });
    }

    $(settings.name).on("select2:open", function (e) {

      //Tạo nút thêm mới
      try {
        var ID = $(this)[0].id;
        var addNew = document.getElementById(ID).getAttribute("add-new");
        if (!$('#' + ID + 'AddNew').length) {
          if (addNew.toUpperCase() == "true".toUpperCase()) {
            var a = $(this).data('select2');
            if (a.id == "select2-" + ID);
            {
              //console.log(a.$results.parents());
              var x = a.$results.parents('.select2-results')[0].innerHTML;;
              a.$results.parents('.select2-results')
                .append('<button id="' + ID + 'AddNew" permiss="them" onclick="' + ID + 'AddNew(); return false;" class="btn btn-success select2-link"><i class="fa fa-plus"></i> Thêm mới </button>')
                .on('click', function (b) {
                  a.trigger('close');
                });

            }
          }
        }
      } catch (e) {
        //console.log(e);

      }
    })
    $(settings.name).find('option:eq(' + settings.indexDefault + ')').prop('selected', true).change();
  },
  // kiểm tra xem đối tượng có tồn tại hay không
  // Kiểm tra hình ảnh, file có tồn tại trên sever hay không
  checkLinkExits: function (options) {
    var defaults = {
      //link cần kiểm tra
      link: ''
    };
    var settings = $.extend(defaults, options);

    var xhr = new XMLHttpRequest();
    if (settings.link.length <= 4) {
      return false;
    }
    xhr.open('HEAD', settings.link + '?subins=' + 3000, false);
    try {
      xhr.send();
      if (xhr.status >= 200 && xhr.status < 304) return true;
      else return false;
    } catch (e) {
      return false;
    }
    return check;
  },
  // bỏ chọn các dòng đã chọn trong Grid
  // GridID: ID grid
  deselectedGrid: function (GridID) {
    try {
      for (var i = 0; i < GridID.Rows.length; i++) {
        GridID.deselectRecord(i);
      }
    } catch (e) {
    }
  },
  // hiển thị ngày hiện tại lên textbox
  hienNgayHienTaiLenTextbox: function (id) {
    try {
      id = id.replaceAll(' ', '');
      var data = id.split(',');
      const today = new Date();
      const yyyy = today.getFullYear();
      let mm = today.getMonth() + 1; // Months start at 0!
      let dd = today.getDate();

      if (dd < 10) dd = '0' + dd;
      if (mm < 10) mm = '0' + mm;

      const formattedToday = dd + '/' + mm + '/' + yyyy;
      for (var i = 0; i < data.length; i++) {
        $('#' + data[i]).value(formattedToday);
      }

    } catch (e) {
    }
  },
  // hiển thị giờ:phút hiện tại lên textbox
  hienGioPhutHienTaiLenTextbox: function (id) {
    try {
      id = id.replaceAll(' ', '');
      var data = id.split(',');
      for (var i = 0; i < data.length; i++) {
        $('#' + data[i]).value((((new Date()).getHours() <= 9 ? "0" + ((new Date()).getHours()) : ((new Date()).getHours()))) + ":" + (((new Date()).getMinutes() <= 9 ? "0" + ((new Date()).getMinutes()) : ((new Date()).getMinutes()))));
      }
    } catch (e) {
    }
  },
  SetChieuCaoFrame: function (FrameID, chieucaotruhao) {
    var chieucao = window.innerHeight - chieucaotruhao;
    var MangFrameID = FrameID.split(',');
    for (var i = 0; i < MangFrameID.length; i++) {
      if (chieucao < 550) {
        chieucao = 550;
      }
      $('#' + MangFrameID[i]).css('height', chieucao + 'px');
    }
  },
  addHeader: function (options) {
    var defaults = {
      name: '',
      listMer: [],
    };
    var settings = $.extend(defaults, options);
    if (settings.listMer.length == 0)
      return;
    var Grid = settings.name;
    if ($('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer table tbody').find('tr').length == 1) {
      //Nhân bản tiêu đề lưới
      //$('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table tbody').append(
      //    $('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table tbody').html()
      //);
      //
      if ($('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table .headerMegreOfGrid').length > 0)
        $('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table .headerMegreOfGrid').remove();
      $($('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table tbody').html().replace('ob_gHR', 'ob_gHR headerMegreOfGrid')).insertBefore(
        $('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table tbody')
      );
      //Xóa text của tr đầu tiên
      //Xóa icon sort  của tr đầu tiên
      $('.headerMegreOfGrid').find('div').text('');

      for (var j = 0; j < settings.listMer.length; j++) {
        var chieuDaiCot = 0;
        for (var i = settings.listMer[j].CotBD; i <= settings.listMer[j].CotKT; i++) {
          chieuDaiCot += parseFloat(Grid.ColumnsCollection[i].Width);
        }
        //Append nội dung merg
        $('#ctl00_ContentPlaceHolder1_' + Grid.ID + '_ob_' + Grid.ID + 'HeaderContainer div table tbody').find('tr:first > :nth-child(' + (settings.listMer[j].CotBD + 1) + ')').append('<div class="mercol" style="width: ' + chieuDaiCot + 'px;">' + settings.listMer[j].NoiDung + '</div>');
      }
    }
  },
  GridToJsonString: function (index, grid) {
    var mang = [];
    for (var i = 0; i < Grid1.Rows.length; i++) { // quyets dòng
      var arrTemp = {};
      for (var j = index; j < grid.ColumnsCollection.length; j++) { // quét cột
        if (grid.ColumnsCollection[j].HeaderText != "") {
          var s = grid.ColumnsCollection[j].DataField;
          arrTemp[s] = grid.Rows[i].Cells[j].Value;
        }
      }
      mang.push(arrTemp);
    }
    return JSON.stringify(mang);
  },
  RemoveCharCodeAt0: function (str) {
    if (str === null || str === undefined) {
      str = '';
    }
    str = str.trim();
    while (str.search(String.fromCharCode(10)) != -1) {
      str = str.replace(String.fromCharCode(10), '<br />');
    }
    return str;
  },
  AddCharCodeAt0: function (str) {
    if (str === null || str === undefined) {
      str = '';
    }
    str = str.trim();
    while (str.search('<br />') != -1) {
      str = str.replace('<br />', String.fromCharCode(10));
    }
    return str;
  },
  NomalArray: function (arrMang) {
    for (var i = 0; i < arrMang.length; ++i) {
      (arrMang[i] == null || arrMang[i] == undefined) && (arrMang[i] = "");
    }
  },
  KiemTraQuyenThem: function () {
    if (!ntspermiss.them) {
      NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác thêm. Vui lòng kiểm tra lại.");
      return !1;
    }
    return !0;
  },
  KiemTraQuyenSua: function () {
    if (!ntspermiss.sua) {
      NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác sửa. Vui lòng kiểm tra lại.");
      return !1;
    }
    return !0;
  },
  KiemTraQuyenXoa: function () {
    if (!ntspermiss.xoa) {
      NTS.canhbao("User bạn đang sử dụng không thể thực hiện thao tác xoá. Vui lòng kiểm tra lại.");
      return !1;
    }
    return !0;
  },
  CauChonMauThuyetMinh: function () {
    return "<p class=\"mb-1\">Chọn mẫu biểu dự toán mới sẽ xóa các chỉ tiêu hiện tại và thay thế bằng chỉ tiêu từ mẫu biểu đang chọn. Bạn có chắc chắn muốn tiếp tục?</p><p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoXoa: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn xóa dòng dữ liệu đã chọn không?</p><p class=\"mb-1\"> - Đồng ý xóa chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoXoaFull: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn xóa tất cả dòng dữ liệu lập dự toán không?</p><p class=\"mb-1\"> - Đồng ý xóa chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoXoaNhom: function () {
    return "<p class=\"mb-1\">Bạn có chắc chắn muốn xóa nhóm này không? Các nhóm con (nếu có) cũng sẽ bị xóa.</p><p class=\"mb-1\"> - Đồng ý xóa chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongLamMoi: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn làm mới dữ liệu đã chọn không?</p><p class=\"mb-1\"> - Đồng ý làm mới chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongTongHop: function () {
    return "<p class=\"mb-1\">Anh/Chị có đồng ý tổng hợp số liệu không?</p><p class=\"mb-1\"> - Tổng hợp số liệu chọn <b>'Có'</b></p><p class=\"mb-1\">- Bỏ qua chọn <b>'Không'</b></p>";
  },
  CauThongBaoTaoTuDong: function () {
    return "<p class=\"mb-1\">Anh/Chị có đồng ý lập dự toán tự động từ số liệu dự toán năm trước không?</p><p class=\"mb-1\"> - Lập tự động số liệu chọn <b>'Có'</b></p><p class=\"mb-1\">- Bỏ qua chọn <b>'Không'</b></p>";
  },

  CauThongTongHop_KhongThayDoi: function () {
    return "<p  class=\"mb-1\">Số liệu dự toán thu, chi năm " + $('#texthdNamNS').text() + " đã tổng hợp. Anh/Chị có đồng ý tổng hợp lại không?</p><p class=\"mb-1\"> - Tổng hợp số liệu chọn <b>'Có'</b></p><p class=\"mb-1\">- Bỏ qua chọn <b>'Không'</b></p>";
  },
  CauThongTongHop_canhbaodonviDaKiemTra: function () {
    return "<p  class=\"mb-1\">" + $('#canhbaodonviDaKiemTra').value() + "</p><p class=\"mb-1\"> - Tổng hợp số liệu chọn <b>'Có'</b></p><p class=\"mb-1\">- Bỏ qua chọn <b>'Không'</b></p>";
  },
  CauThongTongHop_ThayDoi: function () {
    return "<p class=\"mb-1\">Số liệu lập dự toán thu, chi năm " + $('#texthdNamNS').text() + " có thay đổi. Anh/Chị có đồng ý tổng hợp lại không?</p><p class=\"mb-1\"> - Tổng hợp số liệu chọn <b>'Có'</b></p><p class=\"mb-1\">- Bỏ qua chọn <b>'Không'</b></p>";
  },
  CauThongBaoDuyet: function () {
    return "Bạn có đồng ý xét duyệt thông tin đã chọn không?<br/> - Đồng ý chọn <b>'Có'</b><br/>- Không đồng ý chọn <b>'Không'</b>.";
  },
  CauThongBaoBoDuyet: function () {
    return "<p class=\"mb-1\">Bạn có đồng ý bỏ duyệt thông tin đã chọn không?<br/> - Đồng ý chọn <b>'Có'</b>- Không đồng ý chọn <b>'Không'</b>.";
  },
  CauThongBaoGui: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác gửi, số liệu sẽ không được phép chỉnh sửa và đơn vị tiếp nhận sẽ nhận được số liệu bạn gửi. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoNop: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác nộp, số liệu sẽ không được phép chỉnh sửa và đơn vị tiếp nhận sẽ nhận được số liệu bạn gửi. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoThuHoiNop: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác thu hồi, số liệu sẽ được phép chỉnh sửa và đơn vị tiếp nhận sẽ không nhận được số liệu bạn nộp. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoBoGui: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác bỏ gửi, số liệu sẽ được phép chỉnh sửa và đơn vị tiếp nhận sẽ không nhận được số liệu bạn gửi. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauCanhBaoDangSD: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn cập nhật cho dòng dữ liệu đã chọn không?</p><p class=\"mb-1\"> - Đồng ý cập nhật chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauCanhBaoMacDinhSD: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn cập nhật cho dòng dữ liệu đã chọn không?</p><p class=\"mb-1\"> - Đồng ý cập nhật chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoXoaDinhKem: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn xóa đính kèm đã chọn không?</p><p class=\"mb-1\"> - Đồng ý xóa chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBaoCapNhatMacDinh: function () {
    return "<p class=\"mb-1\">Bạn có thật sự muốn lưu dòng dữ liệu với trạng thái mặc định sử dụng thay thế cho các thiết lập mặc định trước đó không?</p><p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },

  CauThongBaoPheDuyet: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác phê duyệt, số liệu sẽ không được phép chỉnh sửa. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },
  CauThongBoPheDuyet: function () {
    return "<p class=\"mb-1\">Khi thực hiện thao tác hủy phê duyệt, số liệu sẽ được phép chỉnh sửa. Bạn có muốn tiếp tục thực hiện thao tác không? <p class=\"mb-1\"> - Đồng ý chọn <b>'Có'</b></p><p class=\"mb-1\">- Không đồng ý chọn <b>'Không'</b></p>";
  },


  getAjax: function (type, duongDanAjax, duLieuGui, baseUrl) {
    var result = null;
    type = type.toUpperCase();

    // Ghép baseUrl + relative URL
    var url = (baseUrl || '') + duongDanAjax;
    var dataToSend = null;

    if (duLieuGui !== null && duLieuGui !== undefined) {
      if (typeof duLieuGui === 'object' && duLieuGui.hasOwnProperty('data')) {
        dataToSend = duLieuGui.data;
      } else if (typeof duLieuGui !== 'object' || Array.isArray(duLieuGui)) {
        dataToSend = duLieuGui;
      }
    }
    if (dataToSend !== null && dataToSend !== undefined) {
      if (type === 'GET') {
        var queryParams = {};
        if (typeof dataToSend === 'object' && !Array.isArray(dataToSend)) {
          queryParams = dataToSend;
        } else {
          url = (baseUrl || '') + duongDanAjax + dataToSend;
          dataToSend = null;
        }
        if (Object.keys(queryParams).length > 0) {
          var queryString = $.param(queryParams);
          url = (baseUrl || '') + duongDanAjax +
            (duongDanAjax.includes('?') ? '&' : '?') +
            queryString;
          dataToSend = null;
        }
      } else if (type === 'DELETE') {
        if (typeof dataToSend !== 'object' || Array.isArray(dataToSend)) {
          url = (baseUrl || '') + duongDanAjax + dataToSend;
          dataToSend = null;
        } else {
          var firstKey = Object.keys(dataToSend)[0];
          var idValue = dataToSend[firstKey];
          if (idValue) {
            url = (baseUrl || '') + duongDanAjax + idValue;
            dataToSend = null;
          }
        }
      }
    }
    try {
      $.ajax({
        url: url,
        type: type,
        data: (type === 'POST' || type === 'PUT') ? JSON.stringify(dataToSend) : null,
        contentType: "application/json; charset=utf-8",
        async: false,
        timeout: 100000,
        beforeSend: function (request) {
          try {
            request.setRequestHeader("__RequestVerificationToken",
              document.querySelector('input[name=__RequestVerificationToken]').value);
          } catch (e) { /* ignore */ }
        },
        success: function (response) {
          if (Array.isArray(response.items)) {
            result = response.items;
          } else {
            result = response;
          }
        },
        error: function (xhr) {
          if (xhr.statusText == "error") {
            xhr.responseJSON.succeeded = false;
            var errors = xhr.responseJSON.errors || {};
            var allMessages = [];
            for (var key in errors) {
              if (errors.hasOwnProperty(key)) {
                allMessages = allMessages.concat(errors[key]);
              }
            }
            xhr.responseJSON.message = allMessages[0] || "";
            return xhr.responseJSON;
          }
        }
      });
    } catch (ignored) {
      // we’ve already captured the error in `error:` callback
    }

    return result;
  },
  getAjaxAPI: function (method, url, data = {}) {
    var result = null;
    var finalUrl = url;
    var ajaxOptions = {
      type: method,
      url: finalUrl,
      async: false,
      timeout: 100000,
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function (response) {
        result = response;
      },
      error: function (response) {
        result = null;
        NTS.loi(response); // Hàm báo lỗi của bạn
      }
    };

    // Nếu là GET, đẩy data lên query string
    if (method.toUpperCase() === 'GET') {
      const query = new URLSearchParams(data).toString();
      if (query != "") {
        ajaxOptions.url += (ajaxOptions.url.includes('?') ? '&' : '?') + query;
      }
    } else {
      // Với các method khác, gửi body JSON
      ajaxOptions.contentType = "application/json; charset=utf-8";
      ajaxOptions.dataType = "json";
      ajaxOptions.data = JSON.stringify(data);
      ajaxOptions.beforeSend = function (request) {
        try {
          request.setRequestHeader("__RequestVerificationToken", document.querySelector('input[name=__RequestVerificationToken]').value);
        } catch (e) { }
      };
    }

    $.ajax(ajaxOptions);
    return result;
  },

  getAjaxAPIAsync: async function (method, url, data = {}) {
    try {
      const result = await new Promise((resolve, reject) => {
        let finalUrl = url;
        const ajaxOptions = {
          type: method,
          url: finalUrl,
          timeout: 100000,
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          success: function (response) {
            resolve(response);
          },
          error: function (response) {
            reject(response);
          }
        };

        // Nếu là GET, đẩy data lên query string
        if (method.toUpperCase() === 'GET') {
          const query = new URLSearchParams(data).toString();
          if (query != "") {
            ajaxOptions.url += (ajaxOptions.url.includes('?') ? '&' : '?') + query;
          }
        } else {
          // Với các method khác, gửi body JSON
          ajaxOptions.contentType = "application/json; charset=utf-8";
          ajaxOptions.dataType = "json";
          ajaxOptions.data = JSON.stringify(data);
          ajaxOptions.beforeSend = function (request) {
            try {
              request.setRequestHeader("__RequestVerificationToken", document.querySelector('input[name=__RequestVerificationToken]').value);
            } catch (e) { }
          };
        }

        $.ajax(ajaxOptions);
      });

      return result;
    } catch (error) {
      console.error("Lỗi khi gọi API:", error);
      throw error; // Có thể throw lỗi để sử dụng ở ngoài
    }
  },

  getAjaxAsync: async function (type, duongDanAjax, duLieuGui, baseUrl) {

    var result = null;
    type = type.toUpperCase();
    var url = (baseUrl || '') + duongDanAjax;
    var dataToSend = null;

    if (duLieuGui !== null && duLieuGui !== undefined) {
      if (typeof duLieuGui === 'object' && duLieuGui.hasOwnProperty('data')) {
        dataToSend = duLieuGui.data;
      } else if (typeof duLieuGui !== 'object' || Array.isArray(duLieuGui)) {
        dataToSend = duLieuGui;
      }
    }

    if (dataToSend !== null && dataToSend !== undefined) {
      if (type === 'GET') {
        var queryParams = {};
        if (typeof dataToSend === 'object' && !Array.isArray(dataToSend)) {
          queryParams = dataToSend;
        } else {
          url = url + dataToSend;
          dataToSend = null;
        }
        if (Object.keys(queryParams).length > 0) {
          var queryString = $.param(queryParams);
          url = url + (duongDanAjax.includes('?') ? '&' : '?') + queryString;
          dataToSend = null;
        }
      } else if (type === 'DELETE') {
        if (typeof dataToSend !== 'object' || Array.isArray(dataToSend)) {
          url = url + dataToSend;
          dataToSend = null;
        } else {
          var firstKey = Object.keys(dataToSend)[0];
          var idValue = dataToSend[firstKey];
          if (idValue) {
            url = (baseUrl || '') + duongDanAjax + idValue;
            dataToSend = null;
          }
        }
      }
    }

    try {
      await $.ajax({
        url: url,
        type: type,
        data: (type === 'POST' || type === 'PUT') ? JSON.stringify(dataToSend) : null,
        contentType: "application/json; charset=utf-8",
        async: true,
        timeout: 100000,
        beforeSend: function (request) {
          try {
            request.setRequestHeader("__RequestVerificationToken",
              document.querySelector('input[name=__RequestVerificationToken]').value);
          } catch (e) { /* ignore */ }
        },
        success: function (response) {
          if (Array.isArray(response.items)) {
            result = response.items;
          } else {
            result = response;
          }
        },
        error: function (xhr) {
          debugger;
          if (xhr.statusText == "error") {
            xhr.responseJSON.succeeded = false;
            var errors = xhr.responseJSON.errors || {};
            var allMessages = [];
            for (var key in errors) {
              if (errors.hasOwnProperty(key)) {
                allMessages = allMessages.concat(errors[key]);
              }
            }
            xhr.responseJSON.message = allMessages[0] || "";
            result = xhr.responseJSON;
          }
        },
        complete: function () {
          NTS.unloadding();
        }
      });
    } catch (ignored) {
      // we’ve already captured the error in `error:` callback
    }
    
    return result;
  }
}
var TabulatorLangsVi = {
  "default": {
    "columns": {
      "name": "Name",
    },
    "ajax": {
      "loading": "Đang tải...",
      "error": "Lỗi tải dữ liệu",
    },
    "groups": {
      "item": "dòng",
      "items": "dòng",
    },
    "pagination": {
      "page_size": "Kích thước",
      "page_title": "Hiển thị",
      "first": '<i class="fa fa-step-backward" aria-hidden="true"></i>',
      "first_title": "Trang đầu",
      "last": '<i class="fa fa-step-forward" aria-hidden="true"></i>',
      "last_title": "Trang cuối",
      "prev": '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
      "prev_title": "Lùi lại",
      "next": '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
      "next_title": "Kế tiếp",
      "all": "All",
      "counter": {
        "showing": "Hiển thị",
        "of": "của",
        "rows": "dòng",
        "pages": "trang",
      }
    },
    "headerFilters": {
      "default": "filter column...",
      "columns": {
        "name": "filter name...",
      }
    },
  }
}
var firstEmptySelect = true;
function templateResult2(result) {
  if (firstEmptySelect) {
    firstEmptySelect = false;
    return '<div class="row">' +
      '<div class="col-3"></div>' +
      '<div class="col-3"></div>' +
      '<div class="col-6"></div>' +
      '</div>';
    //return '<div class="row header-select2">' +
    //    '<div class="col-3"><b>Mã</b></div>' +
    //    '<div class="col-3"><b>Mã</b></div>' +
    //    '<div class="col-6"><b>Tên</b></div>' +
    //    '</div>' +
    //    '<div class="row">' +
    //    '<div class="col-3"></div>' +
    //    '<div class="col-3"></div>' +
    //    '<div class="col-6"></div>' +
    //    '</div>';
  }
  return '<div class="row">' +
    '<div class="col-3">' + result.text + '</div>' +
    '<div class="col-3">' + result.text1 + '</div>' +
    '<div class="col-6">' + result.text2 + '</div>' +
    '</div>';
}
function templateResult1(result) {
  if (firstEmptySelect) {
    firstEmptySelect = false;
    return '<div class="row header-select2">' +
      '<div class="col-4"><b>Mã</b></div>' +
      '<div class="col-8"><b>Tên</b></div>' +
      '</div>' +
      '<div class="row">' +
      '<div class="col-4"></div>' +
      '<div class="col-8"></div>' +
      '</div>';
  }
  return '<div class="row">' +
    '<div class="col-4">' + result.text + '</div>' +
    '<div class="col-8">' + result.text1 + '</div>' +
    '</div>';
}
function templateResult(result) {
  if (firstEmptySelect) {
    firstEmptySelect = false;
    return '<div class="row" >' +
      '<div class="col-4"><b>Mã</b></div>' +
      '<div class="col-8"><b>Tên</b></div>' +
      '</div>';
  }
  return '<div class="row">' +
    '<div class="col-4">' + result.text + '</div>' +
    '<div class="col-8">' + result.text1 + '</div>' +
    '</div>';
}
function templateSelection_ChangeText1(result) {
  return result.text1;
}
function templateSelection_ChangeText2(result) {
  return result.text2;
}
function templateSelection(result) {
  return result.text;
}
function matcher(query, option) {
  if (!query.term || option.id == "" || option.id == "-1") {
    return option;
  }

  var has = true;
  var words = query.term.toUpperCase().split(" ");
  for (var i = 0; i < words.length; i++) {
    var word = words[i];
    has = (has && (option.text.toUpperCase().indexOf(word) >= 0)) || (has && (option.text1.toUpperCase().indexOf(word) >= 0));
  }
  if (has) return option;
  return null;
}
function str_replace(search, replace, str) {
  var ra = replace instanceof Array, sa = str instanceof Array, l = (search = [].concat(search)).length, replace = [].concat(replace), i = (str = [].concat(str)).length;
  while (j = 0, i--)
    while (str[i] = str[i].split(search[j]).join(ra ? replace[j] || "" : replace[0]), ++j < l);
  return sa ? str : str[0];
}
function remove_vietnamese_accents(str) {
  accents_arr = new Array(
    "à", "á", "ạ", "ả", "ã", "â", "ầ", "ấ", "ậ", "ẩ", "ẫ", "ă",
    "ằ", "ắ", "ặ", "ẳ", "ẵ", "è", "é", "ẹ", "ẻ", "ẽ", "ê", "ề",
    "ế", "ệ", "ể", "ễ",
    "ì", "í", "ị", "ỉ", "ĩ",
    "ò", "ó", "ọ", "ỏ", "õ", "ô", "ồ", "ố", "ộ", "ổ", "ỗ", "ơ",
    "ờ", "ớ", "ợ", "ở", "ỡ",
    "ù", "ú", "ụ", "ủ", "ũ", "ư", "ừ", "ứ", "ự", "ử", "ữ",
    "ỳ", "ý", "ỵ", "ỷ", "ỹ",
    "đ",
    "À", "Á", "Ạ", "Ả", "Ã", "Â", "Ầ", "Ấ", "Ậ", "Ẩ", "Ẫ", "Ă",
    "Ằ", "Ắ", "Ặ", "Ẳ", "Ẵ",
    "È", "É", "Ẹ", "Ẻ", "Ẽ", "Ê", "Ề", "Ế", "Ệ", "Ể", "Ễ",
    "Ì", "Í", "Ị", "Ỉ", "Ĩ",
    "Ò", "Ó", "Ọ", "Ỏ", "Õ", "Ô", "Ồ", "Ố", "Ộ", "Ổ", "Ỗ", "Ơ",
    "Ờ", "Ớ", "Ợ", "Ở", "Ỡ",
    "Ù", "Ú", "Ụ", "Ủ", "Ũ", "Ư", "Ừ", "Ứ", "Ự", "Ử", "Ữ",
    "Ỳ", "Ý", "Ỵ", "Ỷ", "Ỹ",
    "Đ"
  );

  no_accents_arr = new Array(
    "a", "a", "a", "a", "a", "a", "a", "a", "a", "a", "a",
    "a", "a", "a", "a", "a", "a",
    "e", "e", "e", "e", "e", "e", "e", "e", "e", "e", "e",
    "i", "i", "i", "i", "i",
    "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o", "o",
    "o", "o", "o", "o", "o",
    "u", "u", "u", "u", "u", "u", "u", "u", "u", "u", "u",
    "y", "y", "y", "y", "y",
    "d",
    "A", "A", "A", "A", "A", "A", "A", "A", "A", "A", "A", "A",
    "A", "A", "A", "A", "A",
    "E", "E", "E", "E", "E", "E", "E", "E", "E", "E", "E",
    "I", "I", "I", "I", "I",
    "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O",
    "O", "O", "O", "O", "O",
    "U", "U", "U", "U", "U", "U", "U", "U", "U", "U", "U",
    "Y", "Y", "Y", "Y", "Y",
    "D"
  );

  return str_replace(accents_arr, no_accents_arr, str);
}
function handleEvent(e) {
  var loaded = e.loaded;
  var total = e.total;
  var progressValue = Math.round((loaded / total) * 100);
}
function handleLoadstart(e, msg) {
  NTS.loadding();
}
function handleProgress(e, msg) {
  var loaded = e.loaded;
  var total = e.total;
  var progressValue = Math.round((loaded / total) * 100);
  $('.process').html(msg + ' ' + progressValue + '%');
}
function handleLoadend(e) {
  NTS.unloadding();
}
function addListeners(xhr, msg) {
  xhr.upload.addEventListener('loadstart', handleLoadstart(xhr, msg));
  xhr.addEventListener('load', handleEvent(xhr, msg));
  xhr.addEventListener('loadend', handleLoadend(xhr, msg));
  xhr.addEventListener('progress', handleProgress(xhr, msg));
  xhr.addEventListener('error', handleEvent);
  xhr.addEventListener('abort', handleEvent);
}
function request(kieuTraVe, data) {
  result = data;
  if (typeof (result) == 'undefined') {
    if (kieuTraVe == 'string')
      return '';
    return JSON.stringify({ d: [] });
  }
  if (typeof (result) == 'string')
    return result;
  return JSON.parse(result);
}

function toastDispose(toast) {
  if (toast && toast._element !== null) {
    if (toastPlacementExample) {
      toastPlacementExample.classList.remove(selectedType);
      DOMTokenList.prototype.remove.apply(toastPlacementExample.classList, selectedPlacement);
    }
    toast.dispose();
  }
}

//function initTinyMCE(selector, height = 400) {
//  tinymce.init({
//    selector: selector,
//    plugins: 'image code table lists link media',
//    toolbar: 'undo redo | styles | bold italic underline | alignleft aligncenter alignright | bullist numlist outdent indent | link image media table | code',
//    height: height,
//    automatic_uploads: true,
//    images_upload_url: '/Home/UploadImage',
//    images_upload_credentials: true,
//    setup: function (editor) {
//      editor.on('init', function () {
//        // Ẩn Upgrade và Branding nếu cần
//        let style = document.createElement("style");
//        style.innerHTML = `
//						.tox-statusbar__branding,
//						.tox-promotion {
//							display: none !important;
//						}`;
//        document.head.appendChild(style);
//      });
//    }
//  });
//}
function exportToWord(html, filename) {
  const header = "<html><head><meta charset='utf-8'></head><body>";
  const footer = "</body></html>";
  const blob = new Blob([header + html + footer], {
    type: "application/msword"
  });

  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
function exportToPDF(html) {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  tempDiv.style.padding = "20px";
  document.body.appendChild(tempDiv);

  html2pdf()
    .from(tempDiv)
    .set({
      margin: 10,
      filename: 'document.pdf',
      html2canvas: { scale: 2 },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    })
    .save()
    .then(() => {
      document.body.removeChild(tempDiv);
    });
}
function initTinyMCE(selector, height = 400) {
  tinymce.init({
    selector: selector,
    plugins: 'image code table lists link media fullscreen preview save print pagebreak anchor codesample footnotes mergetags template comments spellchecker a11ychecker wordcount advlist autolink charmap emoticons checklist',
    // Split toolbar into two rows
    toolbar1: "undo redo | bold italic underline strikethrough | fontfamily fontsize | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media table",
    toolbar2: "forecolor backcolor formatpainter removeformat | charmap emoticons checklist | code fullscreen preview | save print | pagebreak anchor codesample footnotes mergetags | template addcomment showcomments | spellcheckdialog a11ycheck | exportToWord exportToPDF",
    height: height,
    // Cấu hình Font Family và Font Size
    font_formats: "Arial=arial,helvetica,sans-serif; Times New Roman=times new roman,times,serif; Courier New=courier new,courier,monospace; Verdana=verdana,geneva,sans-serif; Georgia=georgia,times,serif; Tahoma=tahoma,arial,helvetica,sans-serif",
    fontsize_formats: "8pt 10pt 12pt 14pt 16pt 18pt 24pt 36pt",
    automatic_uploads: true,
    images_upload_url: '/Home/UploadImage',
    images_upload_credentials: true,
    setup: function (editor) {
      // Export Word button
      editor.ui.registry.addButton('exportToWord', {
        text: 'Export Word',
        icon: 'export',
        tooltip: 'Xuất Word',
        onAction: function () {
          const content = editor.getContent();
          exportToWord(content, 'document.doc');
        }
      });

      // Export PDF button
      editor.ui.registry.addButton('exportToPDF', {
        text: 'Export PDF',
        icon: 'download',
        tooltip: 'Xuất PDF',
        onAction: function () {
          const content = editor.getContent();
          exportToPDF(content);
        }
      });

      // Hide branding and promotion
      editor.on('init', function () {
        let style = document.createElement("style");
        style.innerHTML = `
          .tox-statusbar__branding, 
          .tox-promotion {
            display: none !important;
          }`;
        document.head.appendChild(style);
      });
    }
  });
}
function tablerSelect(selectId, dataList, labelField, valueField, TextShow) {
  const select = document.getElementById(selectId);

  // Tạo HTML <option>
  select.innerHTML = `<option value="">` + TextShow + `</option>` +
    dataList.map(item =>
      `<option value="${item[valueField]}" data-icon="${item.icon}">${item[labelField]}</option>`
    ).join("");

  // Nếu đã có TomSelect, huỷ để khởi tạo lại
  if (select.tomselect) select.tomselect.destroy();

  // Khởi tạo lại TomSelect
  new TomSelect(select, {
    render: {
      option: function (data, escape) {
        return `<div><i class="${escape(data.icon)} me-2"></i>${escape(data.text)}</div>`;
      },
      item: function (data, escape) {
        return `<div><i class="${escape(data.icon)} me-2"></i>${escape(data.text)}</div>`;
      }
    },
    onInitialize: function () {
      const control = this;
      const options = Array.from(control.input.options);
      options.forEach((opt) => {
        if (opt.dataset.icon) {
          control.updateOption(opt.value, {
            ...control.options[opt.value],
            icon: opt.dataset.icon
          });
        }
      });

    }
  });
}


function ResetPasswordVisibility() {
  document.querySelectorAll("[data-toggle-password]").forEach(function (wrapper) {
    const input = wrapper.querySelector("input");
    const toggle = wrapper.querySelector("[data-password-toggle]");
    const eyeShow = wrapper.querySelector(".eye-show");
    const eyeHide = wrapper.querySelector(".eye-hide");

    // Đặt lại input về password
    input.type = "password";

    // Gạch chéo (ẩn) hiện, mắt thường ẩn
    eyeShow.classList.add("d-none");
    eyeHide.classList.remove("d-none");

    if (toggle) toggle.title = "Hiện mật khẩu";
  });
}
document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll("[data-toggle-password]").forEach(function (wrapper) {
    const input = wrapper.querySelector("input[type='password'], input[type='text']");
    const toggle = wrapper.querySelector("[data-password-toggle]");
    const eyeShow = wrapper.querySelector(".eye-show");
    const eyeHide = wrapper.querySelector(".eye-hide");

    toggle.addEventListener("click", function (e) {
      e.preventDefault();
      const isHidden = input.type === "password";
      input.type = isHidden ? "text" : "password";
      toggle.title = isHidden ? "Ẩn mật khẩu" : "Hiện mật khẩu";

      // Đổi icon
      eyeShow.classList.toggle("d-none", !isHidden);
      eyeHide.classList.toggle("d-none", isHidden);
    });
  });

  // Khởi tạo tooltip Bootstrap (nếu cần)
  if (typeof bootstrap !== "undefined") {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
});


function resetForm(id) {
  const form = document.getElementById("" + id);
  form.querySelectorAll("input, select, textarea").forEach(el => {
    if (el.type === "checkbox" || el.type === "radio") {
      el.checked = false;
    } else {
      el.value = "";
    }

    // Reset Tom Select nếu có
    if (el.tomselect) {
      el.tomselect.clear();
    }
  });
}