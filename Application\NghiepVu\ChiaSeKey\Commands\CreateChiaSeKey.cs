﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;
using NHATTAMID2025.Domain.Entities;
using WEB_DLL;

namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands;
internal class CreateChiaSeKey
{
}
public record CreateChiaSeKeyCommand(
        string ChiaSeKeyID,
        string KeyID,
        string HoTen,
        string TenDonViCongTac,
        string DonViCongTacID,
        string Email,
        string LinkKichHoat,
        string TrangThai
 ) : IRequest<Result<ChiaSeKeyDto>?>;
public class CreateCommandValidator : AbstractValidator<CreateChiaSeKeyCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateChiaSeKeyCommandHandler : IRequestHandler<CreateChiaSeKeyCommand, Result<ChiaSeKeyDto>?>
{
    private readonly IApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    public CreateChiaSeKeyCommandHandler(IApplicationDbContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
    }

    public async Task<Result<ChiaSeKeyDto>?> Handle(CreateChiaSeKeyCommand request, CancellationToken cancellationToken)
    {
        var idKey = Guid.NewGuid();

        string keyMaHoaMatKhau = "rateAnd2012";
        string license = ntsSecurity._mEncrypt(request.Email + "|" + request.HoTen + "|" + request.ChiaSeKeyID+"|"+ request.TenDonViCongTac, keyMaHoaMatKhau, true);

        string giaima = ntsSecurity._mDecrypt(license, keyMaHoaMatKhau, true);
        string Link = request.LinkKichHoat + "/hethong/login?share-license=" + license;
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@ChiaSeKeyID", DungChung.NormalizationGuid(request.ChiaSeKeyID)),
            new Microsoft.Data.SqlClient.SqlParameter("@KeyID", DungChung.NormalizationGuid(request.KeyID)),
            new Microsoft.Data.SqlClient.SqlParameter("@HoTen", request.HoTen),
            new Microsoft.Data.SqlClient.SqlParameter("@TenDonViCongTac", request.TenDonViCongTac),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViCongTacID", DungChung.NormalizationGuid(request.DonViCongTacID)),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@LinkKichHoat", Link),
            new Microsoft.Data.SqlClient.SqlParameter("@TrangThai", "0"),
        };
        try
        {
            if(request.Email != "")
            {
                var guiMail = await GuiEmailXacThuc(request.Email, Link);
                if (!guiMail)
                {
                    // Bạn có thể throw exception hoặc return lỗi
                    return null;
                }
            }
           
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ChiaSeKey_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<ChiaSeKeyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            ChiaSeKeyID = reader.GetGuid(reader.GetOrdinal("ChiaSeKeyID")),
        };
    }

    private async Task<bool> GuiEmailXacThuc(string email, string link)
    {
        try
        {
            var fromAddress = new MailAddress(_configuration.GetSection("EmailSettings")?["FromEmail"] ?? "", "Công ty TNHH Phát triển phần mềm Nhất Tâm");
            string fromPassword = _configuration.GetSection("EmailSettings")?["FromPassword"] ?? "";
            var toAddress = new MailAddress(email);
            string subject = "Kích hoạt key bản quyền phần mềm";
            string body = $@"
            <!DOCTYPE html>
            <html lang='vi'>
            <head>
                <meta charset='UTF-8'>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        background-color: #f4f6f8;
                        margin: 0;
                        padding: 0;
                    }}
                    .email-container {{
                        max-width: 600px;
                        margin: 40px auto;
                        background-color: #ffffff;
                        padding: 30px;
                        border-radius: 10px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                    }}
                    .content {{
                        font-size: 16px;
                        color: #444;
                        line-height: 1.6;
                    }}
                    .cta-button {{
                        display: inline-block;
                        background-color: #3498db;
                        color: #fff !important;
                        padding: 12px 24px;
                        border-radius: 6px;
                        text-decoration: none;
                        font-weight: 600;
                        font-size: 16px;
                        margin-top: 20px;
                    }}
                    .footer {{
                        text-align: center;
                        font-size: 13px;
                        color: #888;
                        margin-top: 30px;
                    }}
                </style>
            </head>
            <body>
                <div class='email-container'>
                    <div class='content'>
                        <p>Chào bạn,</p>
                        <p>Bạn vừa nhận được một liên kết để kích hoạt key bản quyền phần mềm.</p>
                        <p>Vui lòng nhấn vào nút bên dưới để thực hiện kích hoạt:</p>

                        <p style='text-align: center;'>
                            <a href='{link}' class='cta-button'>Kích hoạt key</a>
                        </p>

                        <p>Liên kết này có hiệu lực trong vòng <strong>10 phút</strong>.</p>
                        <p>Nếu bạn không yêu cầu, vui lòng bỏ qua email này.</p>
                    </div>
                    <div class='footer'>
                        &copy; {DateTime.Now.Year} NHATTAMID2025. Mọi quyền được bảo lưu.
                    </div>
                </div>
            </body>
            </html>
            ";


            var smtp = new SmtpClient
            {
                Host = "smtp.gmail.com", // ví dụ: smtp.gmail.com
                Port = 587,
                EnableSsl = true,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Credentials = new NetworkCredential(fromAddress.Address, fromPassword),
                Timeout = 20000,
            };

            using var message = new MailMessage(fromAddress, toAddress)
            {
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };
            await smtp.SendMailAsync(message);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
