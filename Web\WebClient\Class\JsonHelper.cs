﻿using System.Data;
using System.Globalization;
using Newtonsoft.Json;

namespace NHATTAMID2025.Web.WebClient.Class;

public  class J<PERSON><PERSON><PERSON>per
{
    public static string To<PERSON><PERSON>(object obj)
    {

        return JsonConvert.SerializeObject(obj);

    }
    public static object? FromJson(string obj)
    {
        return JsonConvert.DeserializeObject(obj);
    }

    public static DataTable? ToTable(string obj)
    {
        return JsonConvert.DeserializeObject<DataTable>(obj);
    }
}
