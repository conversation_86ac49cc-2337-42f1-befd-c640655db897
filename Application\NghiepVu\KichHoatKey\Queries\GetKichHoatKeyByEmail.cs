﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KichHoatKey.Queries;
internal class GetKichHoatKeyByEmail
{
}
public record GetKichHoatKeyByEmailQuery(
    string UserID
    ) : IRequest<Result<List<KichHoatKeyDto>>?>;


public class GetKichHoatKeyByEmailQueryValidator : AbstractValidator<GetKichHoatKeyByEmailQuery>
{
    public GetKichHoatKeyByEmailQueryValidator()
    {
    }
}
public class GetKichHoatKeyByEmailQueryHandler : IRequestHandler<GetKichHoatKeyByEmailQuery, Result<List<KichHoatKeyDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetKichHoatKeyByEmailQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<KichHoatKeyDto>>?> Handle(GetKichHoatKeyByEmailQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
           
                 new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KichHoatKey_GetByEmail",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<KichHoatKeyDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private KichHoatKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KichHoatKeyDto
        {

            TenPhanMem = reader["TenPhanMem"] as string,
            MoTa = reader["MoTa"] as string,
            Link = reader["Link"] as string
        };
    }
}
