﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}
<style>
    .cardview {
        min-height: 100vh; /* <PERSON>ôn cao bằng chiều cao màn hình */
        display: flex;
        flex-direction: column;
        border-radius: 0 !important;
    }

</style>
<div class="card cardview" style="border-radius: 0 !important;">
    <div class="card-header" style="">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">LIÊN KẾT TÀI KHOẢN</h2>
        </div>
        <div class="card-actions d-flex align-items-center gap-2">
        </div>
    </div>
    <div class="container-xl my-4">
        <div class=" row ">

            <div class="col-md-12">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <!-- Thông tin bên trái -->
                            <div class="col-md-6">
                                <div class="d-flex align-items-center mb-3">
                                <h2 class="ms-1 mb-0">Thông tin phần mềm đã liên kết tài khoản trên các dịch vụ của NTSOFT</h2>
                                </div>
                                <p class="text-muted">
                                    Thông tin tài khoản và các tùy chọn giúp bạn quản lý dữ liệu đăng ký giấy phép phần mềm của mình trên hệ thống NTSOFT. Bạn có thể cập nhật thông tin liên hệ, kiểm tra trạng thái giấy phép và theo dõi các hồ sơ đã được cấp phép dễ dàng và minh bạch.
                                </p>
                            </div>

                            <!-- Hình ảnh bên phải -->
                            <div class="col-md-6 text-center">
                            <img src="/Images/giaodienherder.png" alt="Minh họa hồ sơ" class="profile-image" style="height: 130px;">
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    <div class="container-xl mt-4" style="margin-left: 12px;margin-top: -19px !important;">
        <div class="row row-cards" style="margin-top:1px" id="DSSanPhamDaKichHoat">


      



          
        </div>

    </div>


    <style>
        .no-data-message {
            display: flex;
            flex-direction: column;
            align-items: center;
           
            text-align: center;
            padding: 2rem;
            margin-top: 4rem;
         
            border-radius: 0.75rem;
            transition: all 0.3s ease-in-out;
        }

            .no-data-message .nodata-img {
                width: 300px;
                height: auto;
                max-width: 100%;
                margin-bottom: 1rem;
                opacity: 0.8;
                margin-top: 30px;
            }

            .no-data-message p {
                font-size: 1.1rem;
                font-style: italic;
                margin: 0;
            }
    </style>

    <div id="noDataMsg" class="no-data-message cardview" style="display: none;">
        <img src="/Images/nodata.png" alt="Không có dữ liệu" class="nodata-img">
        <p>Không có dữ liệu.</p>
    </div>
</div>
<script src="~/scripts/nguoidung/lienkettaikhoan.js"></script>