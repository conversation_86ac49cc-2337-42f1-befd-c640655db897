﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Commands;
using NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.DTOs;
using NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class LienKetTaiKhoan : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(CreateLienKetTaiKhoan, "/create")
            .MapPost(SendEmailLienKetTaiKhoan, "/SendEmailLienKetTaiKhoan")
            .MapGet(GetLienKetTaiKhoanByUserID, "/getbyid/{Id}");
    }
    public async Task<Result<LienKetTaiKhoanDto>?> CreateLienKetTaiKhoan(ISender sender, [FromBody] CreateLienKetTaiKhoanCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<object>?> SendEmailLienKetTaiKhoan(ISender sender, [FromBody] SendEmailLienKetTaiKhoanCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<List<LienKetTaiKhoanDto>>?> GetLienKetTaiKhoanByUserID(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetLienKetTaiKhoanByUserIDQuery(id));
}
