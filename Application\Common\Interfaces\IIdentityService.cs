﻿using NHATTAMID2025.Application.Common.Models;

namespace NHATTAMID2025.Application.Common.Interfaces;

public interface IIdentityService
{
    Task<string?> GetUserNameAsync(string userId);

    Task<bool> IsInRoleAsync(string userId, string role);

    Task<bool> AuthorizeAsync(string userId, string policyName);

    Task<(Result<object> Result, string UserId)> CreateUserAsync(string userName, string password);

    Task<Result<object>> DeleteUserAsync(string userId);
}
