﻿using NHATTAMID2025.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace NHATTAMID2025.Infrastructure.Data.Configurations;

//public class TodoItemConfiguration : IEntityTypeConfiguration<TodoItem>
//{
//    public void Configure(EntityTypeBuilder<TodoItem> builder)
//    {
//        builder.Property(t => t.Title)
//            .HasMaxLength(200)
//            .IsRequired();
//    }
//}
