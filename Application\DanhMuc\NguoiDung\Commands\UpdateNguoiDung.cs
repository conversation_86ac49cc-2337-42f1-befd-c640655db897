﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;


namespace NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands;
public record UpdateNguoiDungCommand(
    string UserID,
    bool DangSD
    ) : IRequest<Result<NguoiDungDto>?>;

public class UpdateNguoiDungCommandValidator : AbstractValidator<UpdateNguoiDungCommand>
{
    public UpdateNguoiDungCommandValidator()
    {
    }
}

public class UpdateNguoiDungCommandHandler : IRequestHandler<UpdateNguoiDungCommand, Result<NguoiDungDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<NguoiDungDto>?> Handle(UpdateNguoiDungCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID),
            new Microsoft.Data.SqlClient.SqlParameter("@DangSD", request.DangSD)
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_NguoiDung_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<NguoiDungDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new NguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
        };
    }
}
