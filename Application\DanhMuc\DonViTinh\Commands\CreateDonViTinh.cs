﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using NHATTAMID2025.Application.ChucVus.Commands.Create;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonViTinh.Commands;
public record CreateDonViTinhCommand(string MaDonViTinh, string TenDonViTinh,  bool NgungTD) : IRequest<DonViTinhDto?>;

public class CreateCommandValidator : AbstractValidator<CreateDonViTinhCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateDonViTinhCommandHandler : IRequestHandler<CreateDonViTinhCommand, DonViTinhDto?>
{
    private readonly IApplicationDbContext _context;

    public CreateDonViTinhCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<DonViTinhDto?> Handle(CreateDonViTinhCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TenDonViTinh", request.TenDonViTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@MaDonViTinh", request.MaDonViTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@NgungTD", request.NgungTD),
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DonViTinh_Create",
                   MapFromReader,
                   true,
                   parameters
               );

            return dto.FirstOrDefault();

        }
        catch (Exception)
        {
            return null;
        }
    }

    private DonViTinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViTinhDto
        {
            DonViTinhID = reader.GetGuid(reader.GetOrdinal("DonViTinhID")),
            MaDonViTinh = reader["MaDonViTinh"] as string,
            TenDonViTinh = reader["TenDonViTinh"] as string,
            NgungTD = reader.IsDBNull(reader.GetOrdinal("NgungTD")) ? null : reader.GetBoolean(reader.GetOrdinal("NgungTD"))
        };
    }
}
