﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}
<style>


    .edit-button {
        display: inline-flex;
        align-items: center;
        font-size: 0.875rem;
        padding: 0.25rem 0.6rem;
        border: 1px solid #0d6efd;
        border-radius: 4px;
        color: #0d6efd;
        background-color: transparent;
        text-decoration: none;
        transition: all 0.2s ease;
    }

        .edit-button:hover {
            background-color: #0d6efd;
            color: #fff;
        }

        .edit-button .icon {
            margin-right: 0.3rem;
        }


    .edit-button {
        display: none;
    }

    .editable[contenteditable="true"] {
        border: 1px dashed #ccc;
        padding: 2px 4px;
        background-color: #fefefe;
    }

    .avatar-wrapper {
        position: relative;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #ccc;
    }

        .avatar-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border-radius: 50%;
            transition: filter 0.3s ease;
        }

    /* Overlay ẩn mặc định */
    .avatar-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0,0,0,0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        border-radius: 50%;
        transition: opacity 0.3s ease;
        color: white;
        pointer-events: none;
    }

    /* Khi hover vào avatar-wrapper */
    .avatar-wrapper:hover .avatar-overlay {
        opacity: 1;
        pointer-events: auto;
    }

    /* Hiệu ứng làm mờ ảnh khi hover */
    .avatar-wrapper:hover img {
        filter: brightness(70%);
    }


 

    .avatar-text {
        font-size: 12px;
        color: #888;
    }

    .avatar-wrapper:hover .avatar-overlay {
        opacity: 1;
        cursor: pointer;
    }
</style>

<div class="card cardview" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">THÔNG TIN CÁ NHÂN</h2>
        </div>
        <div class="card-actions">
           
        </div>
    </div>
    <div class="container my-4">
    <div class=" row ">
   
        <div class="col-md-12">
        <!-- Header center -->
    

        <!-- Card layout: text left, image right -->
        <div class="">
            <div class="card-body">
                <div class="row align-items-center">
                    <!-- Thông tin bên trái -->
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <h2 class="ms-1 mb-0">Thông tin trong hồ sơ trên các dịch vụ của NTSOFT</h2>
                        </div>
                        <p class="text-muted">
                                    Thông tin tài khoản và các tùy chọn giúp bạn quản lý dữ liệu đăng ký giấy phép phần mềm của mình trên hệ thống NTSOFT. Bạn có thể cập nhật thông tin liên hệ, kiểm tra trạng thái giấy phép và theo dõi các hồ sơ đã được cấp phép dễ dàng và minh bạch.
                        </p>
                    </div>

                    <!-- Hình ảnh bên phải -->
                    <div class="col-md-6 text-center">
                                <img src="/Images/banner1.png" alt="Minh họa hồ sơ" class="profile-image" style="height: 130px;">
                    </div>
                </div>
            </div>
        </div>
        </div>
       
    </div>
    </div>
    <div class="" style="margin-top: -27px;">
        <div class="container my-4">
            <div class="">
                <div class="row align-items-start g-4">

                    <!-- Cột thông tin cá nhân -->
                
                    <div class="col-md-10" style="    margin-left: 15px;">
                        <div class="card card-sm mb-3 cardthongtincoban">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <h5 class="text-muted small mb-1">Thông tin cơ bản</h5>
                                </div>
                                <a href="" id="save-coban"
                                   class="edit-button" >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="me-1 icon" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a1.003 1.003 0 0 0 0-1.42l-2.34-2.34a1.003 1.003 0 0 0-1.42 0l-1.83 1.83 3.75 3.75 1.84-1.82z" />
                                    </svg>
                                    Lưu
                                </a>
                              
                            </div>
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <label for="avatar-input" class="mb-0">
                                    <div class="avatar-wrapper me-3">
                                        <img id="avatar-preview"
                                             src="https://lh3.googleusercontent.com/a/ACg8ocLJwhOYNmwi1XTO7f6W0s9Qm448nGGCQT8v6WVyPOSfJWsPHCA=s60-c-rg-br100"
                                             alt="Avatar"
                                             onerror="this.src='https://id.misacdn.net/img/profile/Default-Avatar.png'">
                                            <div class="h5 mb-0 editable" style="display:none" id="imgsrcAvata"></div>
                                        <div class="avatar-overlay">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
                                                 class="bi bi-camera" viewBox="0 0 16 16">
                                                <path d="M10.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM4.5 8a3.5 3.5 0 1 0 7 0 3.5 3.5 0 0 0-7 0z" />
                                                <path d="M0 4a2 2 0 0 1 2-2h1.172a2 2 0 0 1 1.414.586l.828.828H12a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H5.414l-1-1H2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    </label>

                                    <input type="file" id="avatar-input" accept="image/*" style="display:none;">

                                    <div>
                                        <div class="text-muted small mb-1">Ảnh đại diện</div>
                                        <div class="avatar-text">Đây là ảnh hiển thị khi bạn tương tác với người khác, nhấn vào ảnh để thay đổi</div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Tên đầy đủ</div>
                                    <div class="h5 mb-0 editable" id="HoVaTen">Chưa có thông tin</div>
                                </div>
                               

                            </div>
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Ngày sinh</div>
                                    <div class="h5 mb-0 editable" id="NgaySinh">Chưa có thông tin</div>
                                </div>
                                
                            </div>
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Giới tính</div>
                                    <div class="h5 mb-0 editable" id="GioiTinh">Chưa có thông tin</div>
                                </div>
                                
                            </div>

                        </div>
                       
                        <div class="card card-sm mb-3 cardthongtinlienhe">
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <h5 class="text-muted small mb-1">Thông tin liên hệ</h5>
                                </div>
                                <a href="" id="save-lienhe"
                                   class="edit-button">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="me-1 icon" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04a1.003 1.003 0 0 0 0-1.42l-2.34-2.34a1.003 1.003 0 0 0-1.42 0l-1.83 1.83 3.75 3.75 1.84-1.82z" />
                                    </svg>
                                    Lưu
                                </a>
                            </div>
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Số điện thoại</div>
                                    <div class="h5 mb-0 editable" id="SoDienThoai">Chưa có thông tin</div>
                                </div>
                                
                            </div>

                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Email</div>
                                    <div class="h5 mb-0 d-flex align-items-center gap-2 ">
                                        <div class="" id="Email"><EMAIL></div>
                                        <!-- Badge trạng thái nhẹ nhàng -->

                                        <div class="d-flex align-items-center ml-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-shield-check text-success" width="18" height="18" viewBox="0 0 24 24" stroke-width="1.5" stroke="#228be6" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                                <path d="M9 12l2 2l4 -4" />
                                                <path d="M12 3l7.5 4v5c0 5 -3.5 9 -7.5 10c-4 -1 -7.5 -5 -7.5 -10v-5l7.5 -4" />
                                            </svg>
                                            <div class="ms-2 text-muted small verified-text">Đã xác thực</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body d-flex align-items-center justify-content-between">
                                <div>
                                    <div class="text-muted small mb-1">Địa chỉ</div>
                                    <div class="h5 mb-0 editable" id="DiaChi">Chưa có thông tin</div>
                                </div>
                                
                            </div>
                        


                           <div class="card-body d-flex align-items-center justify-content-between">
                            <div id="DonViCongTacmd">
                                <div class="text-muted small mb-1">Đơn vị công tác</div>

                                <!-- Ghi chú hướng dẫn -->
                                <div class="form-text text-muted mb-2" style="font-size: 0.85rem;">
                                    Nếu bạn là <strong>cơ quan nhà nước</strong>, hãy <span style=" font-weight: 500;">chọn biểu tượng bên phải</span>.
                                    Nếu không, hãy nhập tên đơn vị công tác trực tiếp vào ô văn bản.
                                </div>

                                <!-- Nội dung và icon chọn -->
                                <div class="h5 mb-0 d-flex align-items-center">
                                    <div id="DonViCongTac" class="editable me-2">Chưa có thông tin</div>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="18" height="18" fill="#0d6efd" viewBox="0 0 24 24" style="cursor:pointer;" title="Chọn đơn vị nhà nước">
                                        <path d="M3 10h18v2H3zm2 3h2v7H5zm4 0h2v7H9zm4 0h2v7h-2zm4 0h2v7h-2zM3 21h18v2H3zM12 1L1 6v2h22V6L12 1z" />
                                    </svg>
                                </div>

                                <input type="hidden" id="hfdonViID" />
                                <input type="hidden" id="hftendonVi" />
                            </div>
                        </div>
                        </div>
                       
                    </div>
                    <!-- Cột hình ảnh minh họa -->
                   
                  @*   <div class="col-md-2 d-flex align-items-center justify-content-center" style="height: 634px">
                        <img src="https://www.gstatic.com/identity/boq/accountsettingsmobile/privacycheckup_scene_with_new_shield_316x112_6f524a4a87d89af8e0120501a6295875.png"
                             alt="Minh họa bảo mật"
                             class="img-fluid"
                             style="max-width: 100%; height: auto;">
                    </div> *@
                </div>
            </div>
        </div>
    </div>
</div>
<style>
   .row .dt-row .col-sm-12 {
        overflow-x: auto;
       height: 200px !important
    }
</style>
<div class="modal modal-blur fade" id="modalDonVi" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" style="min-width:50% !important">
        <div class="modal-content">
            <form id="modalDonViForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Cơ quan nhà nước</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="card-body">

                        <table id="DonViTable" class="table table-bordered table-hover custom-table" >
                            <thead>
                                <tr>
                                    <th>Thao tác</th>
                                    <th>Tên đơn vị</th>
                                    <th>Tỉnh</th>
                                </tr>
                            </thead>
                            <tbody id="table-body"></tbody>
                        </table>
                    </div>

                
                </div>
                <div class="modal-footer">
                  
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Chọn và đóng
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<input type="hidden" id="UserID" />


<script src="~/scripts/nghiepvu/thongtinnguoidung.js"></script>
