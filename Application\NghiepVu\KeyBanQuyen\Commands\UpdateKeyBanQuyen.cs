﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands;
public record UpdateKeyBanQuyenCommand(
    string KeyID,
    string MaKey,
    string TrienKhaiPhanMemID,
    string GoiBanQuyenID,
    string NgayTao,
    string NgayHetHan,
    string SoLanKichHoat,
    string UserID,
    bool Khoa
    ) : IRequest<Result<KeyBanQuyenDto>?>;

public class UpdateKeyBanQuyenCommandValidator : AbstractValidator<UpdateKeyBanQuyenCommand>
{
    public UpdateKeyBanQuyenCommandValidator()
    {
    }
}

public class UpdateKeyBanQuyenCommandHandler : IRequestHandler<UpdateKeyBanQuyenCommand, Result<KeyBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateKeyBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<KeyBanQuyenDto>?> Handle(UpdateKeyBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@KeyID", DungChung.NormalizationGuid(request.KeyID)),
            new Microsoft.Data.SqlClient.SqlParameter("@MaKey", request.MaKey),
            new Microsoft.Data.SqlClient.SqlParameter("@TrienKhaiPhanMemID", request.TrienKhaiPhanMemID),
            new Microsoft.Data.SqlClient.SqlParameter("@GoiBanQuyenID", DungChung.NormalizationGuid(request.GoiBanQuyenID)),
            new Microsoft.Data.SqlClient.SqlParameter("@NgayTao", request.NgayTao),
            new Microsoft.Data.SqlClient.SqlParameter("@NgayHetHan", request.NgayHetHan),
            new Microsoft.Data.SqlClient.SqlParameter("@SoLanKichHoat", DungChung.NormalizationNumber(request.SoLanKichHoat)),
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", DungChung.NormalizationGuid(request.UserID)),
             new Microsoft.Data.SqlClient.SqlParameter("@Khoa", request.Khoa)
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KeyBanQuyen_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<KeyBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private KeyBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KeyBanQuyenDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
        };
    }
}

