[{"ContainingType": "NHATTAMID2025.Web.Endpoints.Authentication", "Method": "<PERSON><PERSON>", "RelativePath": "api/Authentication/login", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "query", "Type": "NHATTAMID2025.Application.Authentication.LoginCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Authentication.TokenResponse", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Authentication"], "EndpointName": "<PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ChiaSeKey", "Method": "CreateChiaSeKey", "RelativePath": "api/Chia<PERSON>e<PERSON><PERSON>/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands.CreateChiaSeKeyCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChiaSeKey"], "EndpointName": "CreateChiaSeKey"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ChiaSeKey", "Method": "DeleteChiaSeKey", "RelativePath": "api/ChiaSeKey/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands.DeleteChiaSeKeyCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChiaSeKey"], "EndpointName": "DeleteChiaSeKey"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ChiaSeKey", "Method": "GetChiaSeKeyByID", "RelativePath": "api/ChiaSeKey/getByID/{KeyID}/{ChiaSeKeyID}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "KeyID", "Type": "System.String", "IsRequired": true}, {"Name": "ChiaSeKeyID", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChiaSeKey"], "EndpointName": "GetChiaSeKeyByID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ChiaSeKey", "Method": "GetChiaSeKeyByKeyID", "RelativePath": "api/ChiaSeKey/getByKeyID/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChiaSeKey"], "EndpointName": "GetChiaSeKeyByKeyID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ChiaSeKey", "Method": "UpdateChiaSeKey", "RelativePath": "api/ChiaSeKey/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands.UpdateChiaSeKeyCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChiaSeKey"], "EndpointName": "UpdateChiaSeKey"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ChucVuController", "Method": "CreateChucVu", "RelativePath": "api/ChucVuController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.ChucVus.Commands.Create.CreateChucVuCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChucVuController"], "EndpointName": "CreateChucVu"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ChucVuController", "Method": "GetAllChucVu", "RelativePath": "api/ChucVuController/", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs.ChucVuDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChucVuController"], "EndpointName": "GetAllChucVu"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ChucVuController", "Method": "UpdateChucVu", "RelativePath": "api/ChucVuController/{id}", "HttpMethod": "PUT", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "NHATTAMID2025.Application.ChucVus.Commands.UpdateChucVu.UpdateChucVuCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChucVuController"], "EndpointName": "UpdateChucVu"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ChucVuController", "Method": "DeleteChucVu", "RelativePath": "api/ChucVuController/{id}", "HttpMethod": "DELETE", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChucVuController"], "EndpointName": "DeleteChucVu"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ChucVuController", "Method": "GetChucVuById", "RelativePath": "api/ChucVuController/{id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs.ChucVuDto", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ChucVuController"], "EndpointName": "GetChucVuById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.DangKyGiayPhep", "Method": "CreateDangKyGiayPhep", "RelativePath": "api/DangKyGiayPhep/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Commands.CreateDangKyGiayPhepCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs.DangKyGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyGiayPhep"], "EndpointName": "CreateDangKyGiayPhep"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.DangKyGiayPhep", "Method": "GetAllDangKyGiayPhep", "RelativePath": "api/Dang<PERSON>y<PERSON>iay<PERSON>hep/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs.DangKyGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyGiayPhep"], "EndpointName": "GetAllDangKyGiayPhep"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.DangKyGiayPhep", "Method": "GetDangKyGiayPhepById", "RelativePath": "api/DangKyGiayPhep/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs.DangKyGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyGiayPhep"], "EndpointName": "GetDangKyGiayPhepById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.DangKyGiayPhep", "Method": "GetDangKyGiayPhepByUserID", "RelativePath": "api/DangKyGiayPhep/getbyuserid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs.DangKyGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyGiayPhep"], "EndpointName": "GetDangKyGiayPhepByUserID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.DangKyGiayPhep", "Method": "UpdateDangKyGiayPhep", "RelativePath": "api/DangKyGiayPhep/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Commands.UpdateDangKyGiayPhepCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs.DangKyGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyGiayPhep"], "EndpointName": "UpdateDangKyGiayPhep"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.DangKyUsersController", "Method": "CreateDangKyUsers", "RelativePath": "api/DangKyUsersController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.CreateDangKyUsersCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.HeThong.DangKyUser.DTOs.UsersDangKyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DangKyUsersController"], "EndpointName": "CreateDangKyUsers"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "CreateDonVi", "RelativePath": "api/DonVi/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.DonVi.Commands.CreateDonViCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "CreateDonVi"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "DeleteDonVi", "RelativePath": "api/<PERSON><PERSON><PERSON>/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.DonVi.Commands.DeleteDonViCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "DeleteDonVi"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "GetAllDonVi", "RelativePath": "api/Don<PERSON>i/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "GetAllDonVi"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "GetAllDonViByMaTinh", "RelativePath": "api/DonVi/getbymatinh/{MaTinh}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "GetAllDonViByMaTinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "GetAllDonViByMaXa", "RelativePath": "api/DonVi/getbymaxa/{MaXa}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "MaXa", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "GetAllDonViByMaXa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "GetAllDonViByTinhID", "RelativePath": "api/DonVi/getbytinhid/{TinhID}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "TinhID", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "GetAllDonViByTinhID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "GetAllDonViByXaID", "RelativePath": "api/DonVi/getbyxaid/{XaID}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "XaID", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "GetAllDonViByXaID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonVi", "Method": "UpdateDonVi", "RelativePath": "api/DonVi/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.DonVi.Commands.UpdateDonViCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.DonVi.DTOs.DonViDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonVi"], "EndpointName": "UpdateDonVi"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonViTinhController", "Method": "CreateDonViTinh", "RelativePath": "api/DonViTinhController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.DonViTinh.Commands.CreateDonViTinhCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs.DonViTinhDto", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonViTinhController"], "EndpointName": "CreateDonViTinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.DonViTinhController", "Method": "GetAllDonViTinh", "RelativePath": "api/DonViTinhController/", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs.DonViTinhDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["DonViTinhController"], "EndpointName": "GetAllDonViTinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.GoiBanQuyen", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands.CreateGoiBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs.GoiBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.GoiBanQuyen", "Method": "DeleteGoi<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands.DeleteGoiBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs.GoiBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "DeleteGoi<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.GoiBanQuyen", "Method": "GetAllGoi<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs.GoiBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetAllGoi<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.GoiBanQuyen", "Method": "GetGoiBanQuyenById", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs.GoiBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetGoiBanQuyenById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.GoiBanQuyen", "Method": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands.UpdateGoiBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs.GoiBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KeyBanQuyen", "Method": "C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands.CreateKeyBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs.KeyBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KeyBanQuyen", "Method": "DeleteKey<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands.DeleteKeyBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs.KeyBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "DeleteKey<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KeyBanQuyen", "Method": "GetAllKeyB<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs.KeyBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetAllKeyB<PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KeyBanQuyen", "Method": "GetKeyBanQuyenById", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs.KeyBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetKeyBanQuyenById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KeyBanQuyen", "Method": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands.UpdateKeyBanQuyenCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs.KeyBanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "Update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KichHoatKey", "Method": "CreateKichHoatKey", "RelativePath": "api/KichH<PERSON>Key/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.KichHoatKey.Commands.CreateKichHoatKeyCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs.KichHoatKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["KichHoatKey"], "EndpointName": "CreateKichHoatKey"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KichHoatKey", "Method": "GetKichHoatKeyByEmail", "RelativePath": "api/KichH<PERSON><PERSON>ey/getbyemail/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs.KichHoatKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["KichHoatKey"], "EndpointName": "GetKichHoatKeyByEmail"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KichHoatKey", "Method": "GetKichHoatKeyById", "RelativePath": "api/Kich<PERSON><PERSON><PERSON><PERSON>/getbyid/{Id}/{ten}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}, {"Name": "ten", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs.KichHoatKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["KichHoatKey"], "EndpointName": "GetKichHoatKeyById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.KiemTraThuHoiKey", "Method": "KiemTraThuHoiKeyID", "RelativePath": "api/KiemTraThuHoi<PERSON>ey/KiemTra/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs.ChiaSeKeyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["KiemTraThuHoi<PERSON>"], "EndpointName": "KiemTraThuHoiKeyID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.LienKetTaiKhoan", "Method": "CreateLienKetTaiKhoan", "RelativePath": "api/LienKetTai<PERSON>/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Commands.CreateLienKetTaiKhoanCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.DTOs.LienKetTaiKhoanDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["LienKetTaiKhoan"], "EndpointName": "CreateLienKetTaiKhoan"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.LienKetTaiKhoan", "Method": "GetLienKetTaiKhoanByUserID", "RelativePath": "api/LienKetTaiK<PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.DTOs.LienKetTaiKhoanDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["LienKetTaiKhoan"], "EndpointName": "GetLienKetTaiKhoanByUserID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.LienKetTaiKhoan", "Method": "SendEmailLienKetTaiKhoan", "RelativePath": "api/LienKetTaiK<PERSON>/SendEmailLienKetTaiKhoan", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Commands.SendEmailLienKetTaiKhoanCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["LienKetTaiKhoan"], "EndpointName": "SendEmailLienKetTaiKhoan"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.LinhVuc", "Method": "GetAllLinhVuc", "RelativePath": "api/<PERSON>h<PERSON><PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.LinhVucs.Queries.GetAllLinhVuc.PaginatedList`1[[NHATTAMID2025.Application.DanhMuc.LinhVucs.DTOs.LinhVucDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["LinhVuc"], "EndpointName": "GetAllLinhVuc"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.LoaiNguoiDung", "Method": "GetAllLoaiNguoiDung", "RelativePath": "api/<PERSON>ai<PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.LoaiNguoiDung.DTOs.LoaiNguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["LoaiNguoiDung"], "EndpointName": "GetAllLoaiNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NguoiDung", "Method": "CreateNguoiDung", "RelativePath": "api/<PERSON>uoiDung/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands.CreateNguoiDungCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs.NguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "CreateNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NguoiDung", "Method": "DeleteNguoiDung", "RelativePath": "api/Nguoi<PERSON>ung/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands.DeleteNguoiDungCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs.NguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "DeleteNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NguoiDung", "Method": "GetAllNguoiDung", "RelativePath": "api/<PERSON>uoi<PERSON><PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs.NguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetAllNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NguoiDung", "Method": "GetNguoiDungById", "RelativePath": "api/<PERSON>uoi<PERSON><PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs.NguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetNguoiDungById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NguoiDung", "Method": "UpdateNguoiDung", "RelativePath": "api/NguoiDung/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands.UpdateNguoiDungCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs.NguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "UpdateNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NhanViens", "Method": "Create", "RelativePath": "api/NhanViens/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.CreateNhanVien.CreateNhanVienCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Guid", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["NhanViens"], "EndpointName": "Create"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NhanViens", "Method": "GetAll", "RelativePath": "api/NhanViens/", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs.NhanVienDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["NhanViens"], "EndpointName": "GetAll"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NhanViens", "Method": "Update", "RelativePath": "api/NhanViens/{id}", "HttpMethod": "PUT", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.UpdateNhanVien.UpdateNhanVienCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "MediatR.Unit", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["NhanViens"], "EndpointName": "Update"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NhanViens", "Method": "Delete", "RelativePath": "api/NhanViens/{id}", "HttpMethod": "DELETE", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["NhanViens"], "EndpointName": "Delete"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.NhanViens", "Method": "GetById", "RelativePath": "api/NhanViens/{id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "Microsoft.AspNetCore.Mvc.ActionResult`1[[NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs.NhanVienDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["NhanViens"], "EndpointName": "GetById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.PhanMem", "Method": "GetAllPhanMem", "RelativePath": "api/Phan<PERSON>em/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.PhanMem.DTOs.PhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetAllPhanMem"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.PhanMem", "Method": "GetPhanMemById", "RelativePath": "api/PhanMem/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.PhanMem.DTOs.PhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetPhanMemById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.PhanQuyen", "Method": "GetPhanQuyenById", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.HeThong.PhanQuyen.DTOs.PhanQuyenDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "EndpointName": "GetPhanQuyenById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.QuyenMatKhauController", "Method": "QuenMatKhau", "RelativePath": "api/<PERSON>uyenMatKhauController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.CreateQuenMatKhauCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["QuyenMatKhauController"], "EndpointName": "QuenMatKhau"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.SendEmaiController", "Method": "SendEmai", "RelativePath": "api/SendEmaiController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.CreateSendEmaiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["SendEmaiController"], "EndpointName": "SendEmai"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.SendEmaiController", "Method": "SendEmailXatThuc", "RelativePath": "api/SendEmaiController/SendEmailXatThuc", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.SendEmailXatThucCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["SendEmaiController"], "EndpointName": "SendEmailXatThuc"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.SendEmaiController", "Method": "UpdateEmai", "RelativePath": "api/SendEmaiController/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.CreateUpdateEmailCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.HeThong.DangKyUser.DTOs.UsersDangKyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["SendEmaiController"], "EndpointName": "UpdateEmai"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.TaoMatKhauMoiController", "Method": "TaoMatKhauMoi", "RelativePath": "api/TaoMatKhauMoiController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.TaoMatKhauMoiCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.HeThong.DangKyUser.DTOs.UsersDangKyDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TaoMatKhauMoiController"], "EndpointName": "TaoMatKhauMoi"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ThanhPhanHoSoTTHC", "Method": "GetAllThanhPhanHoSoTTHC", "RelativePath": "api/ThanhPhanHoSoTTHC/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maThuTucHanhChinh", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.ThanhPhanHoSoTTHCs.Queries.GetAllThanhPhanHoSoTTHC.PaginatedList`1[[NHATTAMID2025.Application.DanhMuc.ThanhPhanHoSoTTHCs.DTOs.ThanhPhanHoSoTTHCDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ThanhPhanHoSoTTHC"], "EndpointName": "GetAllThanhPhanHoSoTTHC"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ThongTinGiayPhep", "Method": "GetThongTinGiayPhepById", "RelativePath": "api/ThongTinGiayPhep/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.ThongTinGiayPhep.DTOs.ThongTinGiayPhepDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON>hep"], "EndpointName": "GetThongTinGiayPhepById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ThongTinNguoiDung", "Method": "GetThongTinNguoiDungById", "RelativePath": "api/ThongTin<PERSON><PERSON><PERSON><PERSON><PERSON>/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.DTOs.ThongTinNguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Thong<PERSON><PERSON><PERSON>gu<PERSON>"], "EndpointName": "GetThongTinNguoiDungById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.NghiepVu.ThongTinNguoiDung", "Method": "UpdateThongTinNguoiDung", "RelativePath": "api/ThongTin<PERSON>gu<PERSON>ung/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.Commands.UpdateThongTinNguoiDungCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.DTOs.ThongTinNguoiDungDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Thong<PERSON><PERSON><PERSON>gu<PERSON>"], "EndpointName": "UpdateThongTinNguoiDung"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.ThuTucHanhChinh", "Method": "GetAllThuTucHanhChinh", "RelativePath": "api/ThuTuc<PERSON>anh<PERSON>hinh/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maLinhVuc", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.ThuTucHanhChinhs.Queries.GetAllThuTucHanhChinh.PaginatedList`1[[NHATTAMID2025.Application.DanhMuc.ThuTucHanhChinhs.DTOs.ThuTucHanhChinhDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["ThuTucHanhChinh"], "EndpointName": "GetAllThuTucHanhChinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Tinh", "Method": "GetAllTinh", "RelativePath": "api/Tinh/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Tinh.DTOs.TinhDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON>"], "EndpointName": "GetAllTinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Tinh", "Method": "GetTinhById", "RelativePath": "api/Tinh/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Tinh.DTOs.TinhDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["<PERSON><PERSON>"], "EndpointName": "GetTinhById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.TrienKhaiPhanMem", "Method": "Create<PERSON><PERSON><PERSON>haiPhan<PERSON>em", "RelativePath": "api/<PERSON>en<PERSON>haiPhan<PERSON>em/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands.CreateTrienKhaiPhanMemCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs.TrienKhaiPhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TrienKhaiPhan<PERSON><PERSON>"], "EndpointName": "Create<PERSON><PERSON><PERSON>haiPhan<PERSON>em"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.TrienKhaiPhanMem", "Method": "DeleteTrienKhaiPhanMem", "RelativePath": "api/<PERSON>enKhaiPhanMem/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands.DeleteTrienKhaiPhanMemCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs.TrienKhaiPhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TrienKhaiPhan<PERSON><PERSON>"], "EndpointName": "DeleteTrienKhaiPhanMem"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.TrienKhaiPhanMem", "Method": "GetAllTrienKhaiPhanMem", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON>/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs.TrienKhaiPhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TrienKhaiPhan<PERSON><PERSON>"], "EndpointName": "GetAllTrienKhaiPhanMem"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.TrienKhaiPhanMem", "Method": "GetTrienKhaiPhanMemById", "RelativePath": "api/<PERSON>enKhaiPhan<PERSON>em/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs.TrienKhaiPhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TrienKhaiPhan<PERSON><PERSON>"], "EndpointName": "GetTrienKhaiPhanMemById"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.TrienKhaiPhanMem", "Method": "UpdateTrienKhaiPhanMem", "RelativePath": "api/TrienKhaiPhanMem/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands.UpdateTrienKhaiPhanMemCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs.TrienKhaiPhanMemDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["TrienKhaiPhan<PERSON><PERSON>"], "EndpointName": "UpdateTrienKhaiPhanMem"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "CreateXa", "RelativePath": "api/Xa/create", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.Xa.Commands.CreateXaCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "CreateXa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "DeleteXa", "RelativePath": "api/Xa/delete", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.Xa.Commands.DeleteXaCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "DeleteXa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "GetAllXa", "RelativePath": "api/Xa/getall", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "GetAllXa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "GetAllXaByID", "RelativePath": "api/Xa/getbyid/{Id}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "GetAllXaByID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "GetAllXaByMaTinh", "RelativePath": "api/Xa/getbymatinh/{MaTinh}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "GetAllXaByMaTinh"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "GetAllXaByMa", "RelativePath": "api/Xa/getbymaxa/{MaXa}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "MaXa", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "GetAllXaByMa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "GetAllXaByTinhID", "RelativePath": "api/Xa/getbytinhid/{TinhID}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "TinhID", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Collections.Generic.List`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "GetAllXaByTinhID"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.DanhMuc.Xa", "Method": "UpdateXa", "RelativePath": "api/Xa/update", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.DanhMuc.Xa.Commands.UpdateXaCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[NHATTAMID2025.Application.DanhMuc.Xa.DTOs.XaDto, NHATTAMID2025.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["Xa"], "EndpointName": "UpdateXa"}, {"ContainingType": "NHATTAMID2025.Web.Endpoints.HeThong.XatThucQuenMatKhauController", "Method": "XatThucQuenMatKhau", "RelativePath": "api/XatThucQuenMatKhauController/", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "command", "Type": "NHATTAMID2025.Application.HeThong.DangKyUser.Commands.XatThucQuenMatKhauCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "NHATTAMID2025.Application.Common.Models.Result`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}], "Tags": ["XatThucQuenMatKhauController"], "EndpointName": "XatThucQuenMatKhau"}, {"ContainingType": "NHATTAMID2025.Web.WebClient.Controllers.HeThong.DangNhapController", "Method": "Index", "RelativePath": "hethong/dangnhap", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "NHATTAMID2025.Web.WebClient.Controllers.HeThong.DangNhapController", "Method": "<PERSON><PERSON>", "RelativePath": "hethong/dangnhap/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "data", "Type": "NHATTAMID2025.Web.WebClient.Controllers.HeThong.LoginRequest", "IsRequired": true}], "ReturnTypes": []}]