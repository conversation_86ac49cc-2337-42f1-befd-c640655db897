﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;

namespace NHATTAMID2025.Web.Endpoints.HeThong;
public class TaoMatKhauMoiController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
           .MapPost(TaoMatKhauMoi, "");
    }
    public async Task<Result<UsersDangKyDto>?> TaoMatKhauMoi(ISender sender, [FromBody] TaoMatKhauMoiCommand command)
    {
        return await sender.Send(command);
    }
}
