﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Xa.Queries;

public record GetAllXaCommand : IRequest<Result<List<XaDto>>?>;

public class GetAllXaCommandValidator : AbstractValidator<GetAllXaCommand>
{
    public GetAllXaCommandValidator()
    {
    }
}

public class GetAllXaCommandHandler : IRequestHandler<GetAllXaCommand, Result<List<XaDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllXaCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<XaDto>>?> Handle(GetAllXaCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_Xa_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<XaDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private XaDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new XaDto
        {
            XaID = reader.GetGuid(reader.GetOrdinal("XaID")),
            TinhID = reader.IsDBNull(reader.GetOrdinal("TinhID"))
                     ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            MaXa = reader["MaXa"] as string,
            TenXa = reader["TenXa"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
