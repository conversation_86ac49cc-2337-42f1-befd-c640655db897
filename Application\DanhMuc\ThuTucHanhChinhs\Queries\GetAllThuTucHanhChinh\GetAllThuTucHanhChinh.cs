﻿using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.ThuTucHanhChinhs.DTOs;
using MediatR;
using FluentValidation;
using Microsoft.EntityFrameworkCore;

namespace NHATTAMID2025.Application.ThuTucHanhChinhs.Queries.GetAllThuTucHanhChinh;

public record GetAllThuTucHanhChinhQuery(
    int? PageNumber,
    int? PageSize,
    string? MaLinhVuc
) : IRequest<PaginatedList<ThuTucHanhChinhDto>>;

public class GetAllThuTucHanhChinhQueryValidator : AbstractValidator<GetAllThuTucHanhChinhQuery>
{
    public GetAllThuTucHanhChinhQueryValidator()
    {
        RuleFor(x => x.PageNumber).GreaterThanOrEqualTo(0);
        RuleFor(x => x.PageSize).GreaterThanOrEqualTo(0).LessThanOrEqualTo(1000);
    }
}

public class GetAllThuTucHanhChinhQueryHandler
    : IRequestHandler<GetAllThuTucHanhChinhQuery, PaginatedList<ThuTucHanhChinhDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllThuTucHanhChinhQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedList<ThuTucHanhChinhDto>> Handle(GetAllThuTucHanhChinhQuery request, CancellationToken cancellationToken)
    {
        var query = _context.ThuTucHanhChinh.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(request.MaLinhVuc))
        {
            query = query.Where(x => x.MaLinhVuc == request.MaLinhVuc);
        }

        var totalCount = await query.CountAsync(cancellationToken);

        bool noPaging = !request.PageNumber.HasValue || !request.PageSize.HasValue
                        || request.PageNumber <= 0 || request.PageSize <= 0;

        var baseQuery = query
            .OrderBy(x => x.MaThuTucHanhChinh)
            .Select(x => new ThuTucHanhChinhDto
            {
                ThuTucHanhChinhID = x.ThuTucHanhChinhID,
                MaThuTucHanhChinh = x.MaThuTucHanhChinh,
                TenThuTucHanhChinh = x.TenThuTucHanhChinh,
                MaCapHanhChinh = x.MaCapHanhChinh,
                LoaiThuTucHanhChinh = x.LoaiThuTucHanhChinh,
                MaLinhVuc = x.MaLinhVuc,
                TrinhTuThucHien = x.TrinhTuThucHien,
                CachThucHien = x.CachThucHien,
                DoiTuongThucHien = x.DoiTuongThucHien,
                DiaChiTiepNhan = x.DiaChiTiepNhan,
                YeuCau = x.YeuCau,
                KetQuaThucHien = x.KetQuaThucHien,
                MoTa = x.MoTa,
                CanCuPhapLy = x.CanCuPhapLy,
                VanBanID = x.VanBanID
            });

        if (noPaging)
        {
            var allItems = await baseQuery.ToListAsync(cancellationToken);
            return new PaginatedList<ThuTucHanhChinhDto>(allItems, totalCount, 1, totalCount);
        }

        var items = await baseQuery
            .Skip((request.PageNumber!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize.Value)
            .ToListAsync(cancellationToken);

        return new PaginatedList<ThuTucHanhChinhDto>(items, totalCount, request.PageNumber.Value, request.PageSize.Value);
    }


}

public class PaginatedList<T>
{
    public List<T> Items { get; }
    public int TotalCount { get; }
    public int PageNumber { get; }
    public int PageSize { get; }
    public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);

    public PaginatedList(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items;
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}
