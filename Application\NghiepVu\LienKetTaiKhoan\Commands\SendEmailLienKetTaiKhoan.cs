﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Domain.Entities;
using WEB_DLL;

namespace NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Commands;
internal class SendEmailLienKetTaiKhoan
{
}
public record SendEmailLienKetTaiKhoanCommand(
    string UserIDLienKet,
    string Email,
    string Link
    ) : IRequest<Result<object>?>;
public class SendEmailLienKetTaiKhoanCommandValidator : AbstractValidator<SendEmailLienKetTaiKhoanCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly Dictionary<SendEmailLienKetTaiKhoanCommand, string> _errorMessages = new();
    public SendEmailLienKetTaiKhoanCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(cmd => cmd)
           .MustAsync(BeExistingEmailTaiKhoan)
           .WithMessage(cmd => _errorMessages.ContainsKey(cmd)
               ? _errorMessages[cmd]
               : $"Email '{cmd.Email}' không hợp lệ hoặc đã được liên kết.");
    }

    private async Task<bool> BeExistingEmailTaiKhoan(SendEmailLienKetTaiKhoanCommand cmd, CancellationToken cancellationToken)
    {
        var email = cmd.Email;
        var userId = cmd.UserIDLienKet;
        var link = cmd.Link;

        if (string.IsNullOrWhiteSpace(email))
        {
            _errorMessages[cmd] = "Email không được để trống.";
            return false;
        }
        if (string.IsNullOrWhiteSpace(userId))
        {
            _errorMessages[cmd] = "Người dùng không được để trống.";
            return false;
        }
        if (string.IsNullOrWhiteSpace(link))
        {
            _errorMessages[cmd] = "link không được để trống.";
            return false;
        }
        var emailExists = await _context.Users.AnyAsync(u => u.Email == email, cancellationToken);
        if (!emailExists)
        {
            _errorMessages[cmd] = $"Email '{email}' chưa được đăng ký trong hệ thống ID NTSOFT.";
            return false;
        }

        var linkExists = await _context.TrienKhaiPhanMem.AnyAsync(u => u.UrlTruyCap == link, cancellationToken);
        if (!linkExists)
        {
            _errorMessages[cmd] = $"Link '{link}' chưa được triển khai trong hệ thống ID NTSOFT.";
            return false;
        }

        var fullLinked = await _context.LienKetTaiKhoan.AnyAsync(
            lk => lk.EmailLienKet == email && lk.UserIDLienKet == userId && lk.Link == link,
            cancellationToken);
        if (fullLinked)
        {
            _errorMessages[cmd] = $"Email '{email}' đã được liên kết với tài khoản.";
            return false;
        }

        var emailLinkedToOtherUser = await _context.LienKetTaiKhoan.AnyAsync(
            lk => lk.EmailLienKet == email && lk.Link == link && lk.UserIDLienKet != userId,
            cancellationToken);
        if (emailLinkedToOtherUser)
        {
            _errorMessages[cmd] = $"Email '{email}' đã được liên kết với người dùng khác.";
            return false;
        }

        var userLinkedToOtherEmail = await _context.LienKetTaiKhoan.AnyAsync(
            lk => lk.UserIDLienKet == userId && lk.Link == link && lk.EmailLienKet != email,
            cancellationToken);
        if (userLinkedToOtherEmail)
        {
            _errorMessages[cmd] = $"Tài khoản đã được liên kết với email khác.";
            return false;
        }

        return true;
    }

}

public class SendEmailLienKetTaiKhoanCommandHandler : IRequestHandler<SendEmailLienKetTaiKhoanCommand, Result<object>?>
{
    private readonly IApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    public SendEmailLienKetTaiKhoanCommandHandler(IApplicationDbContext context, IConfiguration configuration)
    {
        _context = context;
        _configuration = configuration;
    }

    public async Task<Result<object>?> Handle(SendEmailLienKetTaiKhoanCommand request, CancellationToken cancellationToken)
    {
        try
        {

            if (string.IsNullOrWhiteSpace(request.Email))
            {
                throw new Exception("Email trong request đang bị null hoặc rỗng!");
            }
            var random = new Random();
            var maXacThuc = random.Next(100000, 1000000).ToString();
            var guiMail = await GuiEmailXacThuc(request.Email, maXacThuc);
            if (!guiMail)
            {
                // Bạn có thể throw exception hoặc return lỗi
                return null;
            }
            var ma = new MaXacThucEntity
            {
                Email = request.Email+ request.UserIDLienKet + request.Link,
                MaXacThuc = maXacThuc,
                ThoiGianTao = DateTime.UtcNow,
                ThoiHan = DateTime.UtcNow.AddMinutes(10),
                TrangThai = "Pending"
            };
            _context.MaXacThuc.Add(ma);
            await _context.SaveChangesAsync(cancellationToken);
            return Result<object>.Success();
        }
        catch (Exception)
        {
            return Result<object>.Failure(["Xác thực email thất bại."]);
        }

    }
    private async Task<bool> GuiEmailXacThuc(string email, string maXacThuc)
    {
        try
        {
            var fromAddress = new MailAddress(_configuration.GetSection("EmailSettings")?["FromEmail"] ?? "", "Công ty TNHH Phát triển phần mềm Nhất Tâm");
            var toAddress = new MailAddress(email);
            string fromPassword = _configuration.GetSection("EmailSettings")?["FromPassword"] ?? "";
            string subject = "Xác thực tài khoản NTSOFT-ID";
            string body = $@"
                <!DOCTYPE html>
                <html lang='vi'>
                <head>
                    <meta charset='UTF-8'>
                    <style>
                        body {{
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            background-color: #f4f6f8;
                            margin: 0;
                            padding: 0;
                        }}
                        .email-container {{
                            max-width: 600px;
                            margin: 40px auto;
                            background-color: #ffffff;
                            padding: 30px;
                            border-radius: 10px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                        }}
                        .header {{
                            text-align: center;
                            padding-bottom: 20px;
                        }}
                        .logo {{
                            width: 120px;
                        }}
                        .title {{
                            font-size: 24px;
                            color: #2c3e50;
                            margin-top: 10px;
                        }}
                        .content {{
                            font-size: 16px;
                            color: #444;
                            line-height: 1.6;
                        }}
                        .code-box {{
                            background-color: #eaf4ff;
                            border: 2px dashed #3498db;
                            font-size: 28px;
                            font-weight: bold;
                            color: #3498db;
                            padding: 15px;
                            text-align: center;
                            margin: 25px 0;
                            border-radius: 8px;
                            letter-spacing: 4px;
                        }}
                        .cta-button {{
                            display: inline-block;
                            background-color: #3498db;
                            color: #fff !important;
                            padding: 12px 24px;
                            border-radius: 6px;
                            text-decoration: none;
                            font-weight: 600;
                            font-size: 16px;
                        }}
                        .footer {{
                            text-align: center;
                            font-size: 13px;
                            color: #888;
                            margin-top: 30px;
                        }}
                    </style>
                </head>
                <body>
                    <div class='email-container'>
                       
                        <div class='content'>
                            <p>Chào bạn,</p>
                            <p>Chúng tôi đã nhận được yêu cầu đăng ký tài khoản từ địa chỉ email này.</p>
                            <p>Vui lòng sử dụng mã xác thực bên dưới để tiếp tục:</p>

                            <div class='code-box'>{maXacThuc}</div>

                            <p>Mã xác thực này có hiệu lực trong vòng <strong>10 phút</strong>.</p>
                            <p>Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này.</p>
                        </div>
                        <div class='footer'>
                            &copy; {DateTime.Now.Year} NHATTAMID2025. Mọi quyền được bảo lưu.
                        </div>
                    </div>
                </body>
                </html>
                ";

            var smtp = new SmtpClient
            {
                Host = "smtp.gmail.com", // ví dụ: smtp.gmail.com
                Port = 587,
                EnableSsl = true,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                Credentials = new NetworkCredential(fromAddress.Address, fromPassword),
                Timeout = 20000,
            };

            using var message = new MailMessage(fromAddress, toAddress)
            {
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };
            await smtp.SendMailAsync(message);
            return true;
        }
        catch
        {
            return false;
        }
    }


}
