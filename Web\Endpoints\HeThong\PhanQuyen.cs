﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.PhanQuyen.DTOs;
using NHATTAMID2025.Application.HeThong.PhanQuyen.Queries;

namespace NHATTAMID2025.Web.Endpoints.HeThong;

public class PhanQuyen : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapGet(GetPhanQuyenById, "/getbyid/{Id}")
        ;
    }

    public async Task<Result<List<PhanQuyenDto>>?> GetPhanQuyenById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetPhanQuyenByNhomIDQuery(id));
}
