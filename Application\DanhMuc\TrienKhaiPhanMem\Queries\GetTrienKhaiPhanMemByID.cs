﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Queries;

public record GetTrienKhaiPhanMemByIDQuery(
    string TrienKhaiPhanMemID
    ) : IRequest<Result<List<TrienKhaiPhanMemDto>>?>;


public class GetTrienKhaiPhanMemByIDQueryValidator : AbstractValidator<GetTrienKhaiPhanMemByIDQuery>
{
    public GetTrienKhaiPhanMemByIDQueryValidator()
    {
    }
}
public class GetTrienKhaiPhanMemByIDQueryHandler : IRequestHandler<GetTrienKhaiPhanMemByIDQuery, Result<List<TrienKhaiPhanMemDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetTrienKhaiPhanMemByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<TrienKhaiPhanMemDto>>?> Handle(GetTrienKhaiPhanMemByIDQuery request, CancellationToken cancellationToken)
    {
        
        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@TrienKhaiPhanMemID", request.TrienKhaiPhanMemID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_TrienKhaiPhanMem_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<TrienKhaiPhanMemDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private TrienKhaiPhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TrienKhaiPhanMemDto
        {
            TrienKhaiPhanMemID = reader.GetGuid(reader.GetOrdinal("TrienKhaiPhanMemID")),
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
            UrlTruyCap = reader["UrlTruyCap"] as string,
            MoTa = reader["MoTa"] as string,
            NgayTrienKhai = reader["NgayTrienKhai"] as string ,
            TrangThai = reader["TrangThai"] as string
        };
    }
}
