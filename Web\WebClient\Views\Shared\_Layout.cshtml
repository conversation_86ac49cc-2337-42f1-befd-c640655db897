﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <meta name="api-base-url" content="@ViewBag.ApiBaseUrl" />
    <link href="~/TablerTheme/libs/fontawesome/css/all.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/jquery/css/jquery-ui.custom.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/jquery/css/jquery-ui.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/boxicons-2.1.4/css/boxicons.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/select2/select2.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/toastr/toastr.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/demo.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/tabler-vendors.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/tabler.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/datepicker/bootstrap-datepicker3.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/daterangepicker/daterangepicker.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/confirm/dist/jquery-confirm.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/tabulator/dist/css/tabulator.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/tabulator/dist/css/tabulator_custom.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/style-custom.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/dropzone/dist/dropzone.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/dropzone/dist/dropzone.css" rel="stylesheet" asp-append-version="true" />
	<script src="~/TablerTheme/libs/jquery/jquery-3.6.3.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/tabulator/dist/js/tabulator.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/dropzone/dist/dropzone-min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/dropzone/dist/dropzone-min.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntslibrary.js" asp-append-version="true"></script>
	<script src="~/dungchung/customs.js" asp-append-version="true"></script>


    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/4.3.0/css/fixedColumns.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tom-select@2.3.1/dist/js/tom-select.complete.min.js"></script>
    <script src="https://cdn.datatables.net/rowgroup/1.3.0/js/dataTables.rowGroup.min.js"></script> <!-- Thêm JS cho rowGroup -->
    <link rel="stylesheet" href="https://cdn.datatables.net/rowgroup/1.3.0/css/rowGroup.bootstrap5.min.css"> <!-- Thêm CSS cho rowGroup -->
    <link href="~/css/customsdatatable.css" rel="stylesheet" />
    <!-- Modern Fonts & Styles -->
    <style>
        body {
            font-family: "Inter", sans-serif;
            background-color: #f5f7fb;
        }

     
        .user-info {
            padding: 1rem;
            text-align: center;
        }

            .user-info img {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                object-fit: cover;
                margin-bottom: 0.5rem;
            }

            .user-info h5 {
                margin: 0;
                font-size: 1rem;
                font-weight: 600;
            }

    

        .bg-sidebar {
            background-color: #ffffff;
            border-right: 1px solid #e3e6ef;
            min-height: 100vh;
        }

        .sidebar-header {
            padding: 1rem 1rem 0.5rem;
            font-size: 1.25rem;
            font-weight: 700;
            text-align: center;
            color: #206bc4;
        }

        .sidebar {
            height: 100vh;
            background: #fff;
            border-right: 1px solid #e3e6f0;
            display: flex;
            flex-direction: column;
            padding-top: 1rem;
        }

        .sidebar-header {
            font-weight: 700;
            font-size: 1.25rem;
            padding: 0 1.25rem;
            margin-bottom: 1rem;
            color: #206bc4;
            user-select: none;
        }

        .user-info {
            text-align: center;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid #e3e6f0;
            margin-bottom: 1.5rem;
        }

            .user-info img {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                margin-bottom: 0.5rem;
            }

            .user-info h5 {
                font-weight: 600;
                margin: 0;
                color: #2c3e50;
            }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            color: #576574;
            border-left: 3px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
        }

            .nav-link:hover {
                background-color: #f1f3f5;
                color: #206bc4;
                border-left-color: #206bc4;
                text-decoration: none;
            }

            .nav-link.active {
                color: #206bc4;
                font-weight: 700;
                border-left-color: #206bc4;
                background-color: #e7f1ff;
            }

        .submenu-toggle {
            justify-content: space-between;
        }

        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .submenu-toggle[aria-expanded="true"] .toggle-icon {
            transform: rotate(90deg);
        }

        .submenu {
            display: none;
            flex-direction: column;
            margin-top: 0.3rem;
            margin-bottom: 0.8rem;
            padding-left: 1.5rem; /* căn lề con */
            margin-left: -12px; /* thụt ra ngoài trái 12px */
            gap: 0.4rem;
        }

            .submenu.show {
                display: flex;
            }

            /* Icon mũi tên menu con nhỏ hơn, thụt sát trái */
            .submenu .nav-link svg.icon-tabler-chevron-right {
                margin-left: 0.25rem;
                stroke: #748c94;
            }

        .toggle-icon {
            margin-left: auto;
            transition: transform 0.3s ease;
        }

            .toggle-icon.rotate {
                transform: rotate(90deg);
            }

        .nav-link.active {
            background-color: #f1f3f5;
            color: #206bc4;
            border-left: 3px solid #206bc4; /* tạo border-left */
            text-decoration: none;
        }








    </style>
</head>
<body class="hold-transition layout-footer-fixed layout-fixed theme-light">

    <div class="page">
        <div class="row g-0">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 bg-sidebar">
                <div class="user-info">
                    <img src="https://ui-avatars.com/api/?name=Nguyen+Nam" alt="Avatar người dùng" class="avatar avatar-xl rounded-circle mx-auto mb-3">
                    <h4 class="mb-1">Nguyễn Nam</h4>
                    <div class="text-muted small">Chào mừng bạn đến với hệ thống <span class="fw-bold text-primary">NTSOFT ID</span></div>
                </div>
             
                <div class="list-group list-group-flush">
                    <a href="#" class="nav-link active">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-user" width="10" height="10" viewBox="0 0 24 24" stroke-width="1.5" stroke="#206bc4" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <circle cx="12" cy="7" r="4" />
                            <path d="M6 21v-2a4 4 0 0 1 8 0v2" />
                        </svg>
                        Thông tin cá nhân
                    </a>

                    <div class="nav-link submenu-toggle" role="button" tabindex="0" aria-expanded="false" aria-controls="submenu-danhmuc">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-folder" width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="#576574" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M3 7h5l2 3h11v8a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-11a2 2 0 0 1 2 -2" />
                        </svg>
                        Danh mục
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-chevron-right toggle-icon" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="#576574" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <polyline points="9 6 15 12 9 18" />
                        </svg>
                    </div>

                    <div class="submenu" id="submenu-danhmuc" aria-labelledby="toggle-danhmuc">
                        <a href="#" class="nav-link">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-chevron-right" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="#748c94" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <polyline points="9 6 15 12 9 18" />
                            </svg>
                            Loại tài khoản
                        </a>
                        <a href="#" class="nav-link">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-chevron-right" width="20" height="20" viewBox="0 0 24 24" stroke-width="2" stroke="#748c94" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <polyline points="9 6 15 12 9 18" />
                            </svg>
                            Nhóm người dùng
                        </a>
                    </div>
                    <a href="#" class="nav-link">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock" width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="#576574" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <rect x="5" y="11" width="14" height="10" rx="2" />
                            <circle cx="12" cy="16" r="1" />
                            <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                        </svg>
                        Bảo mật
                    </a>

                    <a href="#" class="nav-link">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-file-text" width="20" height="20" viewBox="0 0 24 24" stroke-width="1.5" stroke="#576574" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M14 3v4a1 1 0 0 0 1 1h4" />
                            <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z" />
                            <line x1="9" y1="9" x2="10" y2="9" />
                            <line x1="9" y1="13" x2="15" y2="13" />
                            <line x1="9" y1="17" x2="15" y2="17" />
                        </svg>
                        Chính sách riêng tư
                    </a>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-md-9 col-lg-10">

                @RenderBody()
            </div>
        </div>
    </div>


    <script>
        var apiBaseUrl = document
        .querySelector('meta[name="api-base-url"]')
        .getAttribute("content");
    </script>
	<script src="~/TablerTheme/libs/select2/select2.full.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/jquery/jquery-ui.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/bootstrap/dist/js/bootstrap.bundle.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/datepicker/bootstrap-datepicker.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/moment/moment.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/datepicker/bootstrap-datetimepicker.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/daterangepicker/daterangepicker.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/toastr/toastr.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/confirm/js/jquery-confirm.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/confirm/dist/jquery-confirm.min.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntsplugin.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntsvalidate.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
