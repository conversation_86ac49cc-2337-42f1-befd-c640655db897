﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands;

public record DeleteKeyBanQuyenCommand(
    string KeyID
    ) : IRequest<Result<KeyBanQuyenDto>?>;

public class DeleteCommandValidator : AbstractValidator<DeleteKeyBanQuyenCommand>
{
    public DeleteCommandValidator()
    {
    }
}

public class DeleteKeyBanQuyenCommandHandler : IRequestHandler<DeleteKeyBanQuyenCommand, Result<KeyBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteKeyBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<KeyBanQuyenDto>?> Handle(DeleteKeyBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@KeyID", request.KeyID)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KeyBanQuyen_Delete",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<KeyBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private KeyBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KeyBanQuyenDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
        };
    }
}
