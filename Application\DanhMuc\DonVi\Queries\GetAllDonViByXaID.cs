﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.DonVi.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonVi.Queries;

public record GetAllDonViByXaIDQuery(
    string XaID
    ) : IRequest<Result<List<DonViDto>>?>;


public class GetAllDonViByXaIDQueryValidator : AbstractValidator<GetAllDonViByXaIDQuery>
{
    public GetAllDonViByXaIDQueryValidator()
    {
    }
}
public class GetAllDonViByXaIDQueryHandler : IRequestHandler<GetAllDonViByXaIDQuery, Result<List<DonViDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllDonViByXaIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DonViDto>>?> Handle(GetAllDonViByXaIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@XaID", DungChung.NormalizationGuid(request.XaID))
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DonVi_GetAllByXaID",
                   MapFromReader,
                   true,
                   parameters);

            return Result<List<DonViDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private DonViDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViDto
        {
            DonViID = reader.GetGuid(reader.GetOrdinal("DonViID")),
            MaDonVi = reader["MaDonVi"] as string,
            MaQHNS = reader["MaQHNS"] as string,
            TenDonVi = reader["TenDonVi"] as string,
            Email = reader["Email"] as string,
            DiaChi = reader["DiaChi"] as string,
            DienThoai = reader["DienThoai"] as string,
            Website = reader["Website"] as string,
            MaSoThue = reader["Website"] as string,
            TinhID = reader.IsDBNull(reader.GetOrdinal("TinhID"))
                     ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            XaID = reader.IsDBNull(reader.GetOrdinal("XaID"))
                   ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("XaID")),
            TenXa = reader["TenXa"] as string,
            MaXa = reader["MaXa"] as string,
            DonViID_Cha = reader.IsDBNull(reader.GetOrdinal("DonViID_Cha"))
                          ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("DonViID_Cha")),
            MaDonViCha = reader["MaDonViCha"] as string,
            TenDonVi_Cha = reader["TenDonVi_Cha"] as string,
        };
    }
}
