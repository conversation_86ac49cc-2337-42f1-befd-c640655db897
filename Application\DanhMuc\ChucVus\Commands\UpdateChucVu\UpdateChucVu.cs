﻿using Microsoft.Data.SqlClient;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;

namespace NHATTAMID2025.Application.ChucVus.Commands.UpdateChucVu;

public record UpdateChucVuCommand(Guid ChucVuID, string MaChucVu, string TenChucVu, string? DienGiai, bool NgungTD) : IRequest<Result<object>>;


public class UpdateChucVuCommandValidator : AbstractValidator<UpdateChucVuCommand>
{
    public UpdateChucVuCommandValidator()
    {
    }
}

public class UpdateChucVuCommandHandler : IRequestHandler<UpdateChucVuCommand, Result<object>>
{
    private readonly IApplicationDbContext _context;

    public UpdateChucVuCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<object>> Handle(UpdateChucVuCommand request, CancellationToken cancellationToken)
    {
        var sql = "EXEC sp_ChucVu_Update @ChucVuID, @MaChucVu, @TenChucVu, @DienGiai, @NgungTD";

        var parameters = new[]
        {
            new SqlParameter("@ChucVuID", request.ChucVuID),
            new SqlParameter("@MaChucVu", request.MaChucVu),
            new SqlParameter("@TenChucVu", request.TenChucVu),
            new SqlParameter("@DienGiai", request.DienGiai ?? (object)DBNull.Value),
            new SqlParameter("@NgungTD", request.NgungTD)
        };

        try
        {
            await _context.ExecuteSqlRawAsync(sql, parameters);
            return Result<object>.Success();
        }
        catch (Exception ex)
        {
            return Result<object>.Failure(new[] { ex.Message });
        }
    }
}
