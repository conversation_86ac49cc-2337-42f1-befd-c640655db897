﻿using Microsoft.AspNetCore.Mvc;

namespace NHATTAMID2025.Web.WebClient.Controllers.DanhMuc;
[ApiExplorerSettings(IgnoreApi = true)]

public class DanhMucController : Controller
{
    [Route("{controller}/{action}")]
    public IActionResult TrienKhaiPhanMem()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    public IActionResult PhanMem()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    public IActionResult Tinh()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    public IActionResult NguoiDung()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    public IActionResult GoiBanQuyen()
    {
        return View();
    }
}
