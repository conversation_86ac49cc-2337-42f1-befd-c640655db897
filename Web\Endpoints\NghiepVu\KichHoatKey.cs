﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.Queries;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.Commands;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class KichHoatKey : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(CreateKichHoatKey, "/create")
            .MapGet(GetKichHoatKeyById, "/getbyid/{Id}/{ten}")
            .MapGet(GetKichHoatKeyByEmail, "/getbyemail/{Id}")
        ;
    }
    public async Task<Result<KichHoatKeyDto>?> CreateKichHoatKey(ISender sender, [FromBody] CreateKichHoatKeyCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<List<KichHoatKeyDto>>?> GetKichHoatKeyById(ISender sender, [FromRoute] string id, [FromRoute] string ten)
    => await sender.Send(new GetKichHoatKeyByIDQuery(id, ten));

    public async Task<Result<List<KichHoatKeyDto>>?> GetKichHoatKeyByEmail(ISender sender, [FromRoute] string id)
  => await sender.Send(new GetKichHoatKeyByEmailQuery(id));
}
