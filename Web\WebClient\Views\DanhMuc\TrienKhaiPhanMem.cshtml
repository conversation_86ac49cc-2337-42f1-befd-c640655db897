﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}


<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/4.3.0/css/fixedColumns.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
    /* ======= Modal ======= */
    .modal-content {
        background-color: #ffffff;
        border-radius: 0.75rem;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        border: 1px solid #e3e6f0;
    }

    .modal-header, .modal-footer {
        background-color: #f9fafb;
        border-color: #e3e6f0;
    }




  
</style>

<div class="card" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">TRIỂN KHAI PHẦN MỀM</h2>
        </div>
        <div class="card-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-phanmem" onclick="targetonclick()">
                Thêm mới
            </button>
        </div>
    </div>
    <div class="card-body" >
        <table id="phanMemTable1" class="table table-bordered table-hover custom-table" style="width: 100%">
            <thead>
                <tr>
                    <th>Tên Tỉnh</th>
                    <th>Tên phần mềm</th>
                    <th>Link truy cập</th>
                    <th>Ngày triển khai</th>
                    <th>Trạng thái</th>
                    <th>Mô tả</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
</div>


<div class="modal modal-blur fade" id="modal-phanmem" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document" style="max-width:40%">
        <form class="modal-content" id="formPhanMem"  action="#" method="post">
            <div class="modal-header">
                <h5 class="modal-title">Thêm mới phần mềm</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

                <div class="row">
                    <!-- Tên phần mềm -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Tên Phần Mềm</label>
                        <select class="form-select" id="TenPhanMem" name="TenPhanMem" required></select>
                    </div>

                    <!-- Tên tỉnh -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Tên Tỉnh</label>
                        <select class="form-select" id="TenTinh" name="TenTinh" required></select>
                    </div>

                    <!-- Url truy cập -->
                    <div class="col-md-12 mb-3">
                        <label class="form-label">URL Truy Cập</label>
                        <input type="text" class="form-control" id="UrlTruyCap" name="UrlTruyCap" placeholder="https://example.com" required>
                    </div>

                    <!-- Ngày triển khai -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Ngày Triển Khai</label>
                        <input type="date" class="form-control" id="NgayTrienKhai" name="NgayTrienKhai">
                    </div>

                    <!-- Trạng thái -->
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Trạng Thái</label>
                        <select class="form-select" id="TrangThai" name="TrangThai">
                            <option value="1">Hoạt động</option>
                            <option value="0">Ngưng hoạt động</option>
                            <option value="2">Bảo trì</option>
                        </select>
                    </div>

                    <!-- Mô tả -->
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Mô Tả</label>
                        <textarea class="form-control" id="MoTa" name="MoTa" rows="3"></textarea>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-link link-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="submit" class="btn btn-primary">Lưu</button>
            </div>
        </form>
    </div>
</div>
<input type="hidden" id="TrienKhaiPhanMemID" />
<script src="~/scripts/danhmuc/trienkhaiphanmem.js"></script>