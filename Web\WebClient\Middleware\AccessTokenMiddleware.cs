﻿using Microsoft.AspNetCore.Authorization;

namespace NHATTAMID2025.Web.WebClient.Middleware;

public class AccessTokenMiddleware
{
    private readonly RequestDelegate _next;

    public AccessTokenMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // Only check for protected routes (you can customize this logic)
        var endpoint = context.GetEndpoint();
        var hasAuthorize = endpoint?.Metadata?.GetMetadata<AuthorizeAttribute>() != null;

        // Example: Only apply to paths starting with /hethong (except /hethong/login)
        if (hasAuthorize && !context.User.Identity?.IsAuthenticated == true)
        {
            var isHtml = context.Request.Headers["Accept"].Any(h => h!= null && h.Contains("text/html"));
            if (isHtml)
            {
                context.Response.Redirect("/hethong/dangnhap");
                return;
            }
        }

        await _next(context);
    }
}
