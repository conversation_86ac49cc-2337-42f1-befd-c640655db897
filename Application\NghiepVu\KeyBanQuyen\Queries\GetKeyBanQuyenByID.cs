﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Queries;
internal class GetKeyBanQuyenByID
{
}
public record GetKeyBanQuyenByIDQuery(
    string KeyID
    ) : IRequest<Result<List<KeyBanQuyenDto>>?>;


public class GetKeyBanQuyenByIDQueryValidator : AbstractValidator<GetKeyBanQuyenByIDQuery>
{
    public GetKeyBanQuyenByIDQueryValidator()
    {
    }
}
public class GetKeyBanQuyenByIDQueryHandler : IRequestHandler<GetKeyBanQuyenByIDQuery, Result<List<KeyBanQuyenDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetKeyBanQuyenByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<KeyBanQuyenDto>>?> Handle(GetKeyBanQuyenByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@KeyID", request.KeyID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KeyBanQuyen_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<KeyBanQuyenDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private KeyBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KeyBanQuyenDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
            TrienKhaiPhanMemID = reader["TrienKhaiPhanMemID"] as string,
            GoiBanQuyenID = reader.GetGuid(reader.GetOrdinal("GoiBanQuyenID")),
            NgayTao = reader["NgayTao"] as string,
            NgayHetHan = reader["NgayHetHan"] as string,
            SoLanKichHoat = reader["SoLanKichHoat"] != DBNull.Value ? Convert.ToInt32(reader["SoLanKichHoat"]) : 0,
            Khoa = reader["Khoa"] == DBNull.Value ? null : reader.GetBoolean(reader.GetOrdinal("Khoa")),
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
            MaKey = reader["MaKey"] as string,
            TenUser = reader["TenUser"] as string,
            TenPhanMem = reader["TenPhanMem"] as string,
            TenGoi = reader["TenGoi"] as string
        };
    }
}
