﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.DonVi.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonVi.Commands;
public record UpdateDonViCommand(
    string DonViID,
    string MaDonVi,
    string MaQHNS,
    string TenDonVi,
    string Email,
    string DiaChi,
    string DienThoai,
    string Website,
    string MaSoThue,
    string DonViID_Cha,
    string TinhID,
    string XaID,
    string DangSD
    ) : IRequest<Result<DonViDto>?>;

public class UpdateDonViCommandValidator : AbstractValidator<UpdateDonViCommand>
{
    private readonly IApplicationDbContext _context;
    public UpdateDonViCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.DonViID)
            .NotEmpty().WithMessage("DonViID không được để trống.");
        RuleFor(cmd => cmd.MaDonVi)
            .NotEmpty().WithMessage("Mã đơn vị không được để trống.");
        RuleFor(cmd => cmd.TenDonVi)
            .NotEmpty().WithMessage("Tên đơn vị không được để trống.");
        RuleFor(cmd => cmd)
              .MustAsync(BeUniqueMaDonVi)
              .WithMessage(cmd => $"Mã đơn vị '{cmd.MaDonVi}' đã được sử dụng.");
    }


    private async Task<bool> BeUniqueMaDonVi(UpdateDonViCommand cmd, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(cmd.MaDonVi))
            return true;

        return !await _context.DonVi
            .AnyAsync(u => u.MaDonVi == cmd.MaDonVi && u.DonViID != DungChung.NormalizationGuid(cmd.DonViID), cancellationToken);
    }
}

public class UpdateDonViCommandHandler : IRequestHandler<UpdateDonViCommand, Result<DonViDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateDonViCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<DonViDto>?> Handle(UpdateDonViCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@DonViID", DungChung.NormalizationGuid(request.DonViID)),
            new Microsoft.Data.SqlClient.SqlParameter("@MaDonVi", request.MaDonVi),
            new Microsoft.Data.SqlClient.SqlParameter("@MaQHNS", request.MaQHNS),
            new Microsoft.Data.SqlClient.SqlParameter("@TenDonVi", request.TenDonVi),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@DiaChi", request.DiaChi),
            new Microsoft.Data.SqlClient.SqlParameter("@DienThoai", request.DienThoai),
            new Microsoft.Data.SqlClient.SqlParameter("@Website", request.Website),
            new Microsoft.Data.SqlClient.SqlParameter("@MaSoThue", request.MaSoThue),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViID_Cha", DungChung.NormalizationGuid(request.DonViID_Cha)),
            new Microsoft.Data.SqlClient.SqlParameter("@TinhID", DungChung.NormalizationGuid(request.TinhID)),
            new Microsoft.Data.SqlClient.SqlParameter("@XaID", DungChung.NormalizationGuid(request.XaID)),
            new Microsoft.Data.SqlClient.SqlParameter("@DangSD", DungChung.NormalizationBoolean(request.DangSD))
        };

        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DonVi_Update",
                   MapFromReader,
                   true,
                   parameters);
            return Result<DonViDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private DonViDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViDto
        {
            DonViID = reader.GetGuid(reader.GetOrdinal("DonViID")),
        };
    }
}

