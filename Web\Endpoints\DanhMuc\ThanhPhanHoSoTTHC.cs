﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.DanhMuc.ThanhPhanHoSoTTHCs.DTOs;
using NHATTAMID2025.Application.ThanhPhanHoSoTTHCs.Queries.GetAllThanhPhanHoSoTTHC;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class ThanhPhanHoSoTTHC : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllThanhPhanHoSoTTHC, "/getall");
    }

    public async Task<PaginatedList<ThanhPhanHoSoTTHCDto>> GetAllThanhPhanHoSoTTHC(
        ISender sender,
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize,
        [FromQuery] string? maThuTucHanhChinh = null)
    {
        var query = new GetAllThanhPhanHoSoTTHCQuery(pageNumber, pageSize, maThuTucHanhChinh);
        return await sender.Send(query);
    }
}
