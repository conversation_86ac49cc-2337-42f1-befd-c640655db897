﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.Commands;
using NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.DTOs;
using NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class ThongTinNguoiDung : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(UpdateThongTinNguoiDung, "/update")
            .MapGet(GetThongTinNguoiDungById, "/getbyid/{Id}")
        ;
    }

    public async Task<Result<ThongTinNguoiDungDto>?> UpdateThongTinNguoiDung(ISender sender, [FromBody] UpdateThongTinNguoiDungCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<ThongTinNguoiDungDto>>?> GetThongTinNguoiDungById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetThongTinNguoiDungByIDQuery(id));
}
