﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}

<style>


    .form-switch .form-check-input {
        transition: all 0.2s ease-in-out;
    }

        .form-switch .form-check-input:checked {
            background-color: #0054a6; /* <PERSON><PERSON><PERSON> xanh dương đậm khi bật */
        }

        .form-switch .form-check-input:not(:checked) {
            background-color: #d63939; /* Màu đỏ khi tắt */
        }

 

    .highlight-section {
        background-color: #f8f9fa; /* Nền sáng nhẹ */
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #0054a6; /* Viền trái xanh dương nổi bật */
    }
</style>


<div class="card" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">NGƯỜI DÙNG</h2>
        </div>
        <div class="card-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-user" onclick="targetonclick()">
                Thêm mới
            </button>
        </div>
    </div>
    <div class="card-body">
        <div id="filter-section" class="row">
            <div class="col-md-2 mb-3">
                <label for="filter-loaiUsers">Loại người dùng</label>
                <select id="filter-loaiUsers" class="form-select form-select-sm">
                    <option value="">Tất cả</option>
                    <option value="USER">USER</option>
                    <option value="GUEST">GUEST</option>
                    <option value="ADMIN">ADMIN</option>
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <label for="filter-tenDangNhap">Tên đăng nhập</label>
                <input type="text" id="filter-tenDangNhap" class="form-control form-control-sm" placeholder="Tìm...">
            </div>
            <div class="col-md-2 mb-3">
                <label for="filter-email">Email</label>
                <input type="text" id="filter-email" class="form-control form-control-sm" placeholder="Tìm...">
            </div>
            <div class="col-md-2 mb-3">
                <label for="filter-soDienThoai">Số điện thoại</label>
                <input type="text" id="filter-soDienThoai" class="form-control form-control-sm" placeholder="Tìm...">
            </div>
            <div class="col-md-2 mb-3">
                <label for="filter-donViCongTac">Đơn vị công tác</label>
                <input type="text" id="filter-donViCongTac" class="form-control form-control-sm" placeholder="Tìm...">
            </div>
            <div class="col-md-2 mb-3">
                <label for="filter-DangSD">Trạng thái</label>
                <select id="filter-DangSD" class="form-select form-select-sm">
                    <option value="">Tất cả</option>
                    <option value="1">Hoạt động</option>
                    <option value="0">Đã khóa</option>
                </select>
            </div>
        </div>
        <table id="phanMemTable" class="table table-bordered table-hover custom-table" style="width: 100%">
            <thead>
                <tr>
                    <th>Nhóm người dùng</th>
                    <th>Tên người dùng</th>
                    <th>Email</th>
                    <th>Số điện thoại</th>
                    <th>Đơn vị công tác</th>
                    <th>Trạng thái</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
</div>

<div class="modal modal-blur fade" id="modal-user" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style="max-width:35%">
        <div class="modal-content">
            <form id="userForm">

                <div class="modal-header">
                    <h5 class="modal-title">Thông tin người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-1" >
                        <!-- Nhóm 1: Thông tin tài khoản -->
                        <div class="col-12" style="margin-top: -12px; display:none">
                            <h4 class="mt-2">1. Thông tin tài khoản</h4>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Tên người dùng</label>
                            <input type="text" class="form-control" name="TenDangNhap"  id="TenDangNhap" placeholder="Nhập tên người dùng" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Mật khẩu</label>
                            <div class="input-group input-group-flat" data-toggle-password>
                                <input type="password" name="MatMa" id="MatMa" class="form-control" placeholder="Nhập lại mật khẩu" required>
                                <span class="input-group-text">
                                    <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip" data-password-toggle>
                                        <!-- Icon con mắt -->
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-show d-none" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0"></path>
                                            <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6"></path>
                                        </svg>
                                        <!-- Icon con mắt gạch chéo (ẩn) -->
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-hide" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                            <path d="M3 3l18 18" />
                                            <path d="M10.584 10.587a2.99 2.99 0 0 0 2.829 2.828" />
                                            <path d="M9.37 5.365c.996-.248 2.065-.365 3.13-.365 3.5 0 6.5 2 9 6 -1.098 1.757 -2.354 3.147 -3.768 4.168" />
                                            <path d="M6.443 6.429c-1.426.96 -2.692 2.35 -3.77 4.071 2.5 4 5.5 6 9 6 1.108 0 2.207-.193 3.261-.574" />
                                        </svg>
                                    </a>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Nhập lại mật mã</label>

                            <div class="input-group input-group-flat" data-toggle-password>
                                <input type="password" name="NhapLaiMatMa" id="NhapLaiMatMa" class="form-control" placeholder="Nhập lại mật khẩu" required>
                                <span class="input-group-text">
                                    <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip" data-password-toggle>
                                        <!-- Icon con mắt -->
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-show d-none" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0"></path>
                                            <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6"></path>
                                        </svg>
                                        <!-- Icon con mắt gạch chéo (ẩn) -->
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-hide" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                            <path d="M3 3l18 18" />
                                            <path d="M10.584 10.587a2.99 2.99 0 0 0 2.829 2.828" />
                                            <path d="M9.37 5.365c.996-.248 2.065-.365 3.13-.365 3.5 0 6.5 2 9 6 -1.098 1.757 -2.354 3.147 -3.768 4.168" />
                                            <path d="M6.443 6.429c-1.426.96 -2.692 2.35 -3.77 4.071 2.5 4 5.5 6 9 6 1.108 0 2.207-.193 3.261-.574" />
                                        </svg>
                                    </a>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">Phân loại tài khoản</label>
                            <select id="LoaiUsers" name="LoaiUsers" class="form-select" required>
                            </select>
                        </div>

                        <!-- Nhóm 2: Thông tin cá nhân -->
                        <div class="col-12"><h4 class="mt-2" style="display:none">2. Thông tin cá nhân</h4></div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Họ và tên</label>
                            <input type="text" class="form-control" name="HoVaTen" id="HoVaTen" placeholder="Nhập họ và tên">
                        </div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Ngày sinh</label>
                            <input type="date" class="form-control" name="NgaySinh" id="NgaySinh">
                        </div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Giới tính</label>
                            <select id="GioiTinh" name="GioiTinh" class="form-select">
                            </select>
                        </div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Email</label>
                            <input type="email" class="form-control" name="Email" id="Email" placeholder="Nhập email">
                        </div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Số điện thoại</label>
                            <input type="text" class="form-control" name="SoDienThoai" id="SoDienThoai" placeholder="Nhập số điện thoại">
                        </div>
                        <div class="col-md-6" style="display:none">
                            <label class="form-label">Địa chỉ</label>
                            <input type="text" class="form-control" name="DiaChi" id="DiaChi" placeholder="Nhập địa chỉ">
                        </div>

                        <!-- Nhóm 3: Công tác -->
                        <div class="col-md-12">
                            <label class="form-label">Đơn vị công tác</label>
                            <input type="text" class="form-control" name="DonViCongTac" id="DonViCongTac" placeholder="Nhập đơn vị công tác">
                        </div>
                
                        
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between align-items-center flex-wrap w-100">
                    <!-- Bên trái: Checkbox Khóa tài khoản -->
                    <div class="">
                    </div>
                    <!-- Bên phải: Nút Lưu và Hủy -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-device-floppy me-1"></i> Lưu
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="ti ti-x me-1"></i> Hủy
                        </button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>




<!-- Modal chỉnh sửa -->
<div class="modal modal-blur fade" id="userInfoModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="userInfoModalForm">
            <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Security Alert -->
                    <div class="alert alert-warning d-flex align-items-center" role="alert">
                        <div class="flex-shrink-0 me-3 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #fff3cd; border-radius: 50%;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-lock text-warning" width="24" height="24"
                                 viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
                                 stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <rect x="5" y="11" width="14" height="10" rx="2" />
                                <circle cx="12" cy="16" r="1" />
                                <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="alert-title mb-1">Cảnh báo bảo mật</h4>
                            <div class="text-secondary">
                                Bạn không được phép chỉnh sửa thông tin cá nhân của người dùng. Tuy nhiên, bạn vẫn có thể thay đổi trạng thái khóa tài khoản nếu cần.
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Loại người dùng</label>
                        <p id="loaiNguoiDung" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Tên đăng nhập</label>
                        <p id="tenDangNhap" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <p id="email" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện thoại</label>
                        <p id="soDienThoai" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đơn vị công tác</label>
                        <p id="donViCongTac" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mật khẩu</label>
                        <p id="matKhau" class="text-muted">********</p>
                    </div>

                <div class="mb-3 highlight-section">
                    <label class="form-label">Trạng thái tài khoản</label>
                    <div class="">
                        <label class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="KhoaTaiKhoan" name="KhoaTaiKhoan" checked>
                            <span class="status-label" id="modal-statusLabel">Tài khoản đang hoạt động</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Lưu
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
            </div>
            </form>

        </div>
    </div>
</div>
<input type="hidden" id="UserID" />
<script src="~/scripts/danhmuc/nguoidung.js"></script>
