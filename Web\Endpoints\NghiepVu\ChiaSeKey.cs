﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;

public class ChiaSeKey : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(CreateChiaSeKey, "/create")
            .MapGet(GetChiaSeKeyByKeyID, "/getByKeyID/{Id}")
             .MapGet(GetChiaSeKeyByID, "/getByID/{KeyID}/{ChiaSeKeyID}")
             .MapPost(DeleteChiaSeKey, "/delete")
              .MapPost(UpdateChiaSeKey, "/update")
        ;
    }
    public async Task<Result<ChiaSeKeyDto>?> CreateChiaSeKey(ISender sender, [FromBody] CreateChiaSeKeyCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<List<ChiaSeKeyDto>>?> GetChiaSeKeyByKeyID(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetChiaSeKeyByKeyIDQuery(id));
    public async Task<Result<List<ChiaSeKeyDto>>?> GetChiaSeKeyByID(ISender sender, [FromRoute] string KeyID, [FromRoute] string  ChiaSeKeyID)
     => await sender.Send(new GetChiaSeKeyByIDQuery(KeyID, ChiaSeKeyID));
    public async Task<Result<ChiaSeKeyDto>?> DeleteChiaSeKey(ISender sender, [FromBody] DeleteChiaSeKeyCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<ChiaSeKeyDto>?> UpdateChiaSeKey(ISender sender, [FromBody] UpdateChiaSeKeyCommand command)
    {
        return await sender.Send(command);
    }
}
