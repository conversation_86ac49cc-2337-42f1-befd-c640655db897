﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands;

public record DeleteTrienKhaiPhanMemCommand(
    string TrienKhaiPhanMemID
    ) : IRequest<Result<TrienKhaiPhanMemDto>?>;

public class DeleteCommandValidator : AbstractValidator<DeleteTrienKhaiPhanMemCommand>
{
    public DeleteCommandValidator()
    {
    }
}

public class DeleteTrienKhaiPhanMemCommandHandler : IRequestHandler<DeleteTrienKhaiPhanMemCommand, Result<TrienKhaiPhanMemDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteTrienKhaiPhanMemCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<TrienKhaiPhanMemDto>?> Handle(DeleteTrienKhaiPhanMemCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TrienKhaiPhanMemID", request.TrienKhaiPhanMemID)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_TrienKhaiPhanMem_Delete",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<TrienKhaiPhanMemDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TrienKhaiPhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TrienKhaiPhanMemDto
        {
            TrienKhaiPhanMemID = reader.GetGuid(reader.GetOrdinal("TrienKhaiPhanMemID")),
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
            UrlTruyCap = reader["UrlTruyCap"] as string,
            MoTa = reader["MoTa"] as string
        };
    }
}
