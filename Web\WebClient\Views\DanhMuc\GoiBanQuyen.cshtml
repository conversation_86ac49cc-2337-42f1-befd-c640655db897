﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}

<style>


    .form-switch .form-check-input {
        transition: all 0.2s ease-in-out;
    }

        .form-switch .form-check-input:checked {
            background-color: #0054a6; /* <PERSON><PERSON><PERSON> xanh dương đậm khi bật */
        }

        .form-switch .form-check-input:not(:checked) {
            background-color: #d63939; /* <PERSON><PERSON>u đỏ khi tắt */
        }

 

    .highlight-section {
        background-color: #f8f9fa; /* Nền sáng nhẹ */
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #0054a6; /* Viền trái xanh dương nổi bật */
    }


    #ThoiHanLoaiinput .ts-wrapper.form-select.single {
        height: 30px !important;
        min-height: 30px !important;
        padding: 0 !important;
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
        margin-left:0px;
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        display: flex;
        align-items: center;
        border: 1px solid #dce9f5;
        background-color: #fff;
        font-size: 13px;
    }


/* ✅ Khi focus (được click vào) */
#ThoiHanLoaiinput .ts-wrapper.form-select.single.focus {
   border-color: #83b7e8 !important;
}
</style>


<div class="card" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">GÓI BẢN QUYỀN</h2>
        </div>
        <div class="card-actions">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-goiBanQuyen" onclick="targetonclick()">
                Thêm mới
            </button>
        </div>
    </div>
    <div class="card-body">
      
        <table id="phanMemTable" class="table table-bordered table-hover custom-table" style="width: 100%">
            <thead>
                <tr>
                     <th>Thời hạn</th>
                    <th>Tên gói</th>
                    <th>Thiết bị tối đa</th>
                    <th>Giá tiền</th>
                    <th>Người dùng</th>
                    <th>Mô tả</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
       

    </div>
</div>

<div class="modal modal-blur fade" id="modal-goiBanQuyen" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document" style="max-width:42%">
        <div class="modal-content">
            <form id="goiBanQuyenForm">

                <div class="modal-header">
                    <h5 class="modal-title">Thông tin gói bản quyền</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-1" >
                        <div class="col-md-6">
                            <label class="form-label">Mã gói</label>
                            <input type="text" class="form-control" name="MaGoi" id="MaGoi" placeholder="Nhập mã gói" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Tên gói</label>
                            <input type="text" class="form-control" name="TenGoi" id="TenGoi" placeholder="Nhập tên gói" required>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label required">Thời hạn sử dụng</label>
                            <div class="input-group input-group-flat" id="ThoiHanLoaiinput">
                                <select class="form-select form-select-sm"  id="ThoiHanLoai" style="max-width: 100px;" required>
                                </select>
                                <span class="input-group-text bg-white border-start-0 border-end-0 px-2" style="padding-right: 0rem !important; padding-left: 0.25rem !important;"></span>

                                <span class="input-group-text bg-white border-start-0 border-end-0 px-2" style="padding-right: .0rem !important; padding-left: .1rem !important;"></span>
                                <input type="number" class="form-control border-start-0" name="ThoiHanNgay" id="ThoiHanNgay"
                                       placeholder="Nhập số ngày" min="0" required />
                            </div>
                        </div>
                      
                        <div class="col-md-6">
                            <label class="form-label">Giới hạn số thiết bị có thể kích hoạt</label>
                            <input type="text" class="form-control" name="SoThietBiToiDa" id="SoThietBiToiDa" placeholder="Nhập số lượng thiết bị có thể kích hoạt" required>
                        </div>


                        <div class="col-md-6">
                            <label class="form-label">Giá tiền</label>
                            <input type="text" class="form-control text-end" name="GiaTien" id="GiaTien" placeholder="(VNĐ)" >
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Gói dành cho</label>
                            <select id="LoaiNguoiDungID" name="LoaiNguoiDungID" class="form-select" required>
                            </select>
                        </div>

                        <div class="col-md-12">
                            <label class="form-label">Mô tả</label>
                            <textarea class="form-control" id="MoTa" name="MoTa" rows="3"></textarea>
                        </div>
                       
                        
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between align-items-center flex-wrap w-100">
                    <!-- Bên trái: Checkbox Khóa tài khoản -->
                    <div class="">
                    </div>
                    <!-- Bên phải: Nút Lưu và Hủy -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="ti ti-device-floppy me-1"></i> Lưu
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="ti ti-x me-1"></i> Hủy
                        </button>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>
<input type="hidden" id="GoiBanQuyenID" />
<script src="~/scripts/danhmuc/goibanquyen.js"></script>
