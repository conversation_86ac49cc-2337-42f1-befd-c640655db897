﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.CreateNhanVien;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.DeleteNhanVien;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.UpdateNhanVien;
using NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetAllNhanViens;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetNhanVienById;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;


public class NhanViens : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(Create, "/")
            .MapPut(Update, "{id}")
            .MapDelete(Delete, "{id}")
            .MapGet(GetById, "{id}")
            .MapGet(GetAll, "/");
    }

    public async Task<Guid> Create(ISender sender, CreateNhanVienCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Unit> Update(ISender sender, [FromRoute] Guid id, UpdateNhanVienCommand command)
    {
        if (id != command.NhanVienID) throw new BadHttpRequestException("ID mismatch");
        return await sender.Send(command);

    }

    public async Task<Result<object>> Delete(ISender sender, [FromRoute] Guid id)
    {
        var command = new DeleteNhanVienCommand() { NhanVienID = id};
        return await sender.Send(command);
    }

    public async Task<ActionResult<NhanVienDto?>> GetById(ISender sender, [FromRoute] Guid id)
        => await sender.Send(new GetNhanVienByIdQuery(id));

    public async Task<List<NhanVienDto>> GetAll(ISender sender)
        => await sender.Send(new GetAllNhanViensQuery());
}
