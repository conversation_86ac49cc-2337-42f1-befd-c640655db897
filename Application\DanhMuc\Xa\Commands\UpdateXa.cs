﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.Xa.Commands;
public record UpdateXaCommand(
    string XaID,
    string TinhID,
    string MaXa,
    string TenXa,
    string MoTa,
    string DangSD
    ) : IRequest<Result<XaDto>?>;

public class UpdateCommandValidator : AbstractValidator<UpdateXaCommand>
{
    private readonly IApplicationDbContext _context;
    public UpdateCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.XaID)
             .NotEmpty().WithMessage("XaID không được để trống.");
        RuleFor(cmd => cmd.MaXa)
            .NotEmpty().WithMessage("Mã xã không được để trống.");
        RuleFor(cmd => cmd.TenXa)
            .NotEmpty().WithMessage("Tên xã không được để trống.");
        RuleFor(cmd => cmd)
              .MustAsync(BeUniqueMaXa)
              .WithMessage(cmd => $"Mã xã '{cmd.MaXa}' đã được sử dụng.");
    }


    private async Task<bool> BeUniqueMaXa(UpdateXaCommand cmd, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(cmd.MaXa))
            return true;

        return !await _context.Xa
            .AnyAsync(u => u.MaXa == cmd.MaXa && u.XaID != DungChung.NormalizationGuid(cmd.XaID), cancellationToken);
    }
}

public class UpdateXaCommandHandler : IRequestHandler<UpdateXaCommand, Result<XaDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateXaCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }
    public async Task<Result<XaDto>?> Handle(UpdateXaCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@XaID", DungChung.NormalizationGuid(request.XaID)),
            new Microsoft.Data.SqlClient.SqlParameter("@TinhID",DungChung.NormalizationGuid(request.TinhID)),
            new Microsoft.Data.SqlClient.SqlParameter("@MaXa", request.MaXa),
            new Microsoft.Data.SqlClient.SqlParameter("@TenXa", request.TenXa),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@DangSD", DungChung.NormalizationBoolean(request.DangSD))
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                       "sp_Xa_Update",
                       MapFromReader,
                       true,
                       parameters);
            return Result<XaDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private XaDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new XaDto
        {
            XaID = reader.GetGuid(reader.GetOrdinal("XaID")),
        };
    }
}
