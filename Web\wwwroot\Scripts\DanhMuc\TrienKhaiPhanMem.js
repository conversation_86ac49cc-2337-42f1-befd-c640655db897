﻿var danhSachPhanMem;
var danhSachTinh;
var ThaoTac;
$(document).ready( async function () {
  var PhanMem = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/PhanMem/getall', null)
  if (PhanMem.succeeded) {
    danhSachPhanMem = PhanMem.result;
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + PhanMem.errors)
  }
  var Tinh = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/Tinh/getall', null)
  if (Tinh.succeeded) {
    danhSachTinh = Tinh.result;
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + Tinh.errors)
  }
  LoadLuoi()
  document.getElementById('modal-phanmem').addEventListener('show.bs.modal', loadCombos);
});
function targetonclick() {
  ThaoTac = 'Them'
  resetForm("formPhanMem")
}
async function LoadLuoi() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/TrienKhaiPhanMem/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#phanMemTable1').DataTable().destroy();
    $('#phanMemTable1').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },
     
      columns: [
        { data: 'tenTinh', width: "120px" },
        { data: 'tenPhanMem', width: "150px" },
        {
          data: 'urlTruyCap',
          width: "200px",
          render: function (data) {
            return `<a href="${data}" target="_blank">${data}</a>`;
          }
        },
        { data: 'ngayTrienKhai', className: "text-center", width: "120px" },
        {
          data: 'trangThai',

          width: "120px",
          render: function (data) {
            switch (data) {
              case '1':
                return '<span class="badge bg-green-lt text-green">Hoạt động</span>';
              case '0':
                return '<span class="badge bg-red-lt text-red">Ngưng hoạt động</span>';
              case '2':
                return '<span class="badge bg-yellow-lt text-yellow-dark">Bảo trì</span>';
            }
          }
        },
        {
          data: 'moTa',
          width: "300px",
          render: function (data, type, row, meta) {
            if (!data) return '';

            const maxLength = 50;
            const shortText = data.length > maxLength ? data.substring(0, maxLength) + '...' : data;

            if (data.length <= maxLength) {
              return `<span>${data}</span>`;
            }

            return `
            <span class="short-text">${shortText}</span>
            <span class="full-text d-none">${data}</span>
            <a href="javascript:void(0)" class="toggle-moTa" style="color:#3182ce; font-size:12px; margin-left:6px;">Xem thêm</a>
        `;
          }
        },
     
        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "100px",
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.trienKhaiPhanMemID}')">
                                            <i class="fa fa-pencil text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.trienKhaiPhanMemID}')">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
    setTimeout(() => {
      table.columns.adjust().draw(false);
    }, 300);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

function loadCombos() {
  const selectPM = document.getElementById("TenPhanMem");
  const selectTinh = document.getElementById("TenTinh");

  selectPM.innerHTML = `<option value="">-- Chọn phần mềm --</option>` +
    danhSachPhanMem.map(p => `<option value="${p.phanMemID}">${p.tenPhanMem} (${p.maPhanMem})</option>`).join("");

  selectTinh.innerHTML = `<option value="">-- Chọn tỉnh --</option>` +
    danhSachTinh.map(t => `<option value="${t.tinhID}">${t.tenTinh} (${t.maTinh})</option>`).join("");
}
$('#formPhanMem').on('submit', async function (e) {
  e.preventDefault();
  if (ThaoTac == 'Them') { 
    const data = {
      PhanMemID: document.getElementById("TenPhanMem").value,
      TinhID: document.getElementById("TenTinh").value,
      UrlTruyCap: document.getElementById("UrlTruyCap").value,
      NgayTrienKhai: document.getElementById("NgayTrienKhai").value,
      TrangThai: document.getElementById("TrangThai").value,
      MoTa: document.getElementById("MoTa").value,
    };
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/TrienKhaiPhanMem/create', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Thêm mới dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-phanmem').modal('hide');
        setTimeout(() => {
          $('body').removeClass('modal-open');
          $('.modal-backdrop').remove();
        }, 100); // đợi hiệu ứng fade
      }
    } catch (error) {
      NTS.canhbao("Thêm mới dữ liệu thất bại!");
    }
  }

  if (ThaoTac == 'Sua') {
    const data = {
      TrienKhaiPhanMemID: document.getElementById("TrienKhaiPhanMemID").value,
      PhanMemID: document.getElementById("TenPhanMem").value,
      TinhID: document.getElementById("TenTinh").value,
      UrlTruyCap: document.getElementById("UrlTruyCap").value,
      NgayTrienKhai: document.getElementById("NgayTrienKhai").value,
      TrangThai: document.getElementById("TrangThai").value,
      MoTa: document.getElementById("MoTa").value,
    };
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/TrienKhaiPhanMem/update', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Cập nhật dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-phanmem').modal("hide")
      }
    } catch (error) {
      NTS.canhbao("Cập nhật dữ liệu thất bại!");
    }
  }
 
});
async function deleteRow(id) {
  // Icon SVG Tabler cảnh báo (triangle-exclamation)
  const tablerWarningSVG = `
    <svg class="tabler-icon-warning" xmlns="http://www.w3.org/2000/svg" 
         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
         stroke-linejoin="round" stroke-linecap="round" style="width:72px; height:72px; margin: 0 auto 15px auto; stroke:#f59e0b;">
      <path d="M12 9v4"></path>
      <path d="M12 17h.01"></path>
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    </svg>
  `;

  const confirmed = await Swal.fire({
    title: 'Bạn có chắc muốn xóa?',
    text: "Hành động này sẽ không thể hoàn tác!",
    icon: 'warning', // vẫn phải có icon để vùng icon xuất hiện
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    reverseButtons: true,
    didOpen: () => {
      // Thay thế icon mặc định bằng SVG Tabler
      const iconElem = document.querySelector('.swal2-icon.swal2-warning');
      if (iconElem) {
        iconElem.innerHTML = tablerWarningSVG;
        iconElem.style.background = 'none'; // bỏ nền mặc định
        iconElem.style.border = 'none'; // bỏ viền mặc định
      }
    }
  });

  if (confirmed.isConfirmed) {
    const data = {
      TrienKhaiPhanMemID: id
    };

    try {
      const response = await NTS.getAjaxAPIAsync(
        'POST',
        window.location.origin + '/api/TrienKhaiPhanMem/delete',
        data
      );
      if (response.succeeded == false && response.errors.length > 0) {
        NTS.canhbao(response.errors[0]);
      } else {
        if (response.succeeded == true) {
          await Swal.fire('Đã xóa!', 'Bản ghi đã được xóa thành công.', 'success');
          LoadLuoi();
        }
      }
    } catch (error) {
      Swal.fire('Lỗi!', 'Không thể xóa bản ghi.', 'error');
    }
  }
}

async function editRow(id) {
  ThaoTac='Sua'
  $('#modal-phanmem').modal("show")
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/TrienKhaiPhanMem/getbyid/${id}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    $('#TenPhanMem').val(originalData[0].phanMemID);
    $('#TenTinh').val(originalData[0].tinhID);
    $('#UrlTruyCap').val(originalData[0].urlTruyCap);
    try {
      const originalDate = originalData[0].ngayTrienKhai;
      // Tách ngày, tháng, năm từ chuỗi gốc (dd/MM/yyyy)
      const [day, month, year] = originalDate.split("/");
      // Chuyển về định dạng yyyy-MM-dd
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      $('#NgayTrienKhai').val(formattedDate);
    } catch {
      $('#NgayTrienKhai').val('');
    }
   
    $('#TrangThai').val(originalData[0].trangThai);
    $('#MoTa').val(originalData[0].moTa);
    $('#TrienKhaiPhanMemID').value(originalData[0].trienKhaiPhanMemID);

  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }

}


$('#phanMemTable1').on('click', '.toggle-moTa', function () {
  const $row = $(this).closest('td');
  const $short = $row.find('.short-text');
  const $full = $row.find('.full-text');

  if ($full.hasClass('d-none')) {
    $short.addClass('d-none');
    $full.removeClass('d-none');
    $(this).text('Ẩn bớt');
  } else {
    $short.removeClass('d-none');
    $full.addClass('d-none');
    $(this).text('Xem thêm');
  }
});