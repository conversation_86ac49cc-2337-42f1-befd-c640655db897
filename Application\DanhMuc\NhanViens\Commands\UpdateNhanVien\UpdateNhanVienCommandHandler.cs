﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Domain.Entities;

namespace NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.UpdateNhanVien;
public class UpdateNhanVienCommandHandler : IRequestHandler<UpdateNhanVienCommand, Unit>
{
    private readonly IApplicationDbContext _context;

    public UpdateNhanVienCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Unit> Handle(UpdateNhanVienCommand request, CancellationToken cancellationToken)
    {
        var entity = await _context.NhanVien.FindAsync(request.NhanVienID);

        if (entity == null) throw new NotFoundException(nameof(NhanVien), request.NhanVienID.ToString());

        entity.MaNhanVien = request.MaNhanVien;
        entity.TenNhanVien = request.TenNhanVien;
        entity.ChucVu = request.ChucVu;
        entity.DienGiai = request.DienGiai;
        entity.NgungTD = request.NgungTD;

        await _context.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }

}
