﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs;
public class KichHoatKeyDto
{
    public Guid KichHoatID { get; set; }
    public Guid? KeyID { get; set; }
    public Guid? UserID { get; set; }
    public string? TenThietBi { get; set; }
    public string? IPKichHoat { get; set; }
    public DateTime? NgayKichHoat { get; set; }
    public string? HeDieuHanh { get; set; }
    public string? TrangThai { get; set; }
    public DateTime? NgayhetHan { get; set; }
    public string? <PERSON><PERSON>i<PERSON>ich<PERSON>oat { get; set; }
    public string? EmailKichHoat { get; set; }
    public string? DonVi<PERSON>ichHoat { get; set; }
    public string? Link { get; set; }
    public string? TenPhanMem { get; set; }
    public string? MoTa { get; set; }
}
