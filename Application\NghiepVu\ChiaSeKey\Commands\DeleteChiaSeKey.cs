﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Commands;

public record DeleteChiaSeKeyCommand(
    string ChiaSeKeyID
    ) : IRequest<Result<ChiaSeKeyDto>?>;

public class DeleteCommandValidator : AbstractValidator<DeleteChiaSeKeyCommand>
{
    public DeleteCommandValidator()
    {
    }
}

public class DeleteChiaSeKeyCommandHandler : IRequestHandler<DeleteChiaSeKeyCommand, Result<ChiaSeKeyDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteChiaSeKeyCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<ChiaSeKeyDto>?> Handle(DeleteChiaSeKeyCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@ChiaSeKeyID", request.ChiaSeKeyID)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ChiaSeKey_Delete",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<ChiaSeKeyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            ChiaSeKeyID = reader.GetGuid(reader.GetOrdinal("ChiaSeKeyID")),
        };
    }
}
