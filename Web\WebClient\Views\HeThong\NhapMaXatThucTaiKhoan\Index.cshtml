﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}

<style>
    body {
        background: linear-gradient(135deg, #ffffff 0%, #2baaff 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }



    .register-container {
        max-width: 400px;
        margin: 40px auto;
        background: #fff;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .logo-row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10px;
    }

        .logo-row img {
            height: 40px;
            margin: 0 10px;
        }

    h2 {
        font-size: 24px;
        font-weight: 600;
        text-align: center;
    }

    .form-label {
        font-weight: 500;
    }

    .row.g-2 > .col {
        flex: 1;
    }

    .password-rules {
        font-size: 13px;
        color: #4caf50;
        margin-top: 10px;
    }

        .password-rules li {
            margin-bottom: 4px;
        }

    .btn-social img {
        height: 20px;
        margin-right: 8px;
    }

    .btn-social {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 1px solid #ddd;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin: 0 4px;
    }

    .text-muted a {
        color: #0d6efd;
        text-decoration: none;
    }

    
    .btn-primary {
        --tblr-btn-border-color: transparent;
        --tblr-btn-hover-border-color: transparent;
        --tblr-btn-active-border-color: transparent;
        --tblr-btn-color: var(--tblr-primary-fg);
        --tblr-btn-bg: #74bdff;
        --tblr-btn-hover-color: var(--tblr-primary-fg);
        --tblr-btn-hover-bg: rgba(var(--tblr-primary-rgb), .8);
        --tblr-btn-active-color: var(--tblr-primary-fg);
        --tblr-btn-active-bg: rgba(var(--tblr-primary-rgb), .8);
        --tblr-btn-disabled-bg: var(--tblr-primary);
        --tblr-btn-disabled-color: var(--tblr-primary-fg);
        --tblr-btn-box-shadow: var(--tblr-box-shadow-input);
    }

    .btn-check:checked + .btn, .btn.active, .btn.show, .btn:first-child:active, :not(.btn-check) + .btn:active {
        color: #664d92;
        background-color: var(--tblr-btn-active-bg);
        border-color: #ddd7e7;
        box-shadow: var(--tblr-btn-active-shadow);
    }

</style>

<div class="register-container">
    <div class="verify-container" id="verifyFormContainer">
        <h2>Xác nhận Email</h2>
        <p class="text-muted">Chúng tôi đã gửi mã xác nhận tới địa chỉ email của bạn.</p>

        <form id="verifyForm">
            <div class="mb-3">
                <div class="d-flex justify-content-center gap-2 mb-3" id="codeInputs">
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                </div>
            </div>

            <button type="submit" class="btn btn-success w-100">Xác nhận</button>

            <p class="text-center mt-2">
                Không nhận được mã?
                <a href="#" id="resendCode">Gửi lại mã</a>
            </p>
        </form>
    </div>
</div>
<script src="~/scripts/NhapMaXatThucTaiKhoan.js"></script>