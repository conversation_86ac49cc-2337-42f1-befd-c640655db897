﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;

namespace NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
internal class XatThucQuenMatKhau
{
}
public record XatThucQuenMatKhauCommand(string Email, string MaXacThuc) : IRequest<Result<object>?>;
public class XatThucQuenMatKhauValidator : AbstractValidator<XatThucQuenMatKhauCommand>
{
    private readonly IApplicationDbContext _context;
    public XatThucQuenMatKhauValidator(IApplicationDbContext context)
    {
        _context = context;
    }
}
public class XatThucQuenMatKhauCommandHandler : IRequestHandler<XatThucQuenMatKhauCommand, Result<object>?>
{
    private readonly IApplicationDbContext _context;
    public XatThucQuenMatKhauCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }
    public async Task<Result<object>?> Handle(XatThucQuenMatKhauCommand request, CancellationToken cancellationToken)
    {
        var ma = await _context.MaXacThuc
                    .Where(x => x.Email == request.Email && x.MaXacThuc == request.MaXacThuc)
                    .OrderByDescending(x => x.ThoiGianTao)
                    .FirstOrDefaultAsync();

        if (ma == null)
            return Result<object>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<object>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<object>.Failure(["Mã xác thực đã hết hạn."]);
        ma.TrangThai = "Used";
        return Result<object>.Success();
    }
  
}
