﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetNhanVienById;
public class GetNhanVienByIdHandler : IRequestHandler<GetNhanVienByIdQuery, NhanVienDto?>
{
    private readonly IApplicationDbContext _context;

    public GetNhanVienByIdHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<NhanVienDto?> Handle(GetNhanVienByIdQuery request, CancellationToken cancellationToken)
    {
        return await _context.NhanVien
            .Where(x => x.NhanVienID == request.NhanVienID)
            .Select(x => new NhanVienDto
            {
                NhanVienID = x.NhanVienID,
                <PERSON><PERSON>han<PERSON>ien = x.<PERSON><PERSON>,
                Ten<PERSON><PERSON><PERSON>ien = x.Ten<PERSON>han<PERSON>ien,
                ChucVu = x.ChucVu,
                DienGiai = x.DienGiai,
                NgungTD = x.NgungTD
            }).FirstOrDefaultAsync(cancellationToken);
    }
}
