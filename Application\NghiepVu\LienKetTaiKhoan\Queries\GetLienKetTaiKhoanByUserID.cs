﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Queries;
internal class GetLienKetTaiKhoanByUserID
{
}
public record GetLienKetTaiKhoanByUserIDQuery(
    string UserID
    ) : IRequest<Result<List<LienKetTaiKhoanDto>>?>;


public class GetLienKetTaiKhoanByUserIDQueryValidator : AbstractValidator<GetLienKetTaiKhoanByUserIDQuery>
{
    public GetLienKetTaiKhoanByUserIDQueryValidator()
    {
    }
}
public class GetLienKetTaiKhoanByUserIDQueryHandler : IRequestHandler<GetLienKetTaiKhoanByUserIDQuery, Result<List<LienKetTaiKhoanDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetLienKetTaiKhoanByUserIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<LienKetTaiKhoanDto>>?> Handle(GetLienKetTaiKhoanByUserIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@UserIDNtsoft", request.UserID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_LienKetTaiKhoan_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<LienKetTaiKhoanDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private LienKetTaiKhoanDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new LienKetTaiKhoanDto
        {
            LienKetTaiKhoanID = reader.GetGuid(reader.GetOrdinal("LienKetTaiKhoanID")),
            Link = reader["Link"] as string,
            TenPhanMem = reader["TenPhanMem"] as string,
            MoTa = reader["MoTa"] as string
        };
    }
}
