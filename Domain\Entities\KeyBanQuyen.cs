﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Domain.Entities;
public class KeyBan<PERSON><PERSON>en
{
    [Key]
    public Guid KeyID { get; set; }
    public string? <PERSON><PERSON><PERSON> { get; set; }
    public Guid? TrienKhaiPhanMemID { get; set; }
    public Guid? GoiBanQuyenID { get; set; }
    public DateTime? NgayTao { get; set; }
    public DateTime? NgayHetHan { get; set; }
    public int? SoLan<PERSON>ichHoat { get; set; }
    public bool? Khoa { get; set; }
    public string? TrangThai { get; set; }
}
