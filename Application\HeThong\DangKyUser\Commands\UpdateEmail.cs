﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using Azure.Core;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using NHATTAMID2025.Domain.Entities;
using WEB_DLL;

namespace NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
internal class UpdateEmail
{
}
public record CreateUpdateEmailCommand(
    string TenDangNhap,
    string MatMa,
    string Email,
    string MaXacThuc
    ) : IRequest<Result<UsersDangKyDto>?>;
public class CreateUpdateEmailCommandValidator : AbstractValidator<CreateUpdateEmailCommand>
{
    private readonly IApplicationDbContext _context;
    public CreateUpdateEmailCommandValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd)
            .NotEmpty().WithMessage("Tên đăng nhập không được để trống.")
            .MustAsync(BeValidTenDangNhapVaMatMa)
            .WithMessage(cmd => $"Tên đăng nhập '{cmd.TenDangNhap}' hoặc mật mã không đúng.");
        RuleFor(cmd => cmd.Email)
            .NotEmpty().WithMessage("Email không được để trống.")
            .MustAsync(BeUniquEmail)
            .WithMessage(cmd => $"Email '{cmd.Email}' đã được sử dụng.");
    }
    private async Task<bool> BeValidTenDangNhapVaMatMa(CreateUpdateEmailCommand cmd, CancellationToken cancellationToken)
    {
        string keyMaHoaMatKhau = "rateAnd2012";
        if (string.IsNullOrWhiteSpace(cmd.TenDangNhap) || string.IsNullOrWhiteSpace(cmd.MatMa))
            return false;

        return await _context.Users.AnyAsync(u =>
            u.TenDangNhap == cmd.TenDangNhap && u.MatMa == ntsSecurity._mEncrypt(cmd.MatMa, keyMaHoaMatKhau, true) ,
            cancellationToken);
    }

    private async Task<bool> BeUniquEmail(string email, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(email))
            return false; // nếu trống thì không hợp lệ

        // Trả về TRUE nếu chưa tồn tại (hợp lệ)
        return !await _context.Users
            .AnyAsync(u => u.Email == email, cancellationToken);
    }

}

public class CreateUpdateEmailCommandHandler : IRequestHandler<CreateUpdateEmailCommand, Result<UsersDangKyDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateUpdateEmailCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<UsersDangKyDto>?> Handle(CreateUpdateEmailCommand request, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(request.Email))
        {
            throw new Exception("Email trong request đang bị null hoặc rỗng!");
        }
        var ma = await _context.MaXacThuc
                         .Where(x => x.Email == request.Email && x.MaXacThuc == request.MaXacThuc)
                         .OrderByDescending(x => x.ThoiGianTao)
                         .FirstOrDefaultAsync();

        if (ma == null)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã hết hạn."]);

        ma.TrangThai = "Used";

        string keyMaHoaMatKhau = "rateAnd2012";
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TenDangNhap", request.TenDangNhap),
            new Microsoft.Data.SqlClient.SqlParameter("@MatMa",ntsSecurity._mEncrypt( request.MatMa.Trim(), keyMaHoaMatKhau, true )),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email)
        };
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                "sp_CapNhatEmail_Update",
                MapFromReader,
                true,
                parameters
            );
            return Result<UsersDangKyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return Result<UsersDangKyDto>.Failure(["Xác thực email thất bại."]);
        }

    }
    private UsersDangKyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new UsersDangKyDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID"))
        };
    }
  

}
