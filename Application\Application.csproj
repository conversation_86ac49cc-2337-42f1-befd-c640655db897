﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RootNamespace>NHATTAMID2025.Application</RootNamespace>
    <AssemblyName>NHATTAMID2025.Application</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" />
    <PackageReference Include="AutoMapper" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" />
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DanhMuc\ChucVus\Commands\DeleteChucVu\" />
    <Folder Include="DanhMuc\ChucVus\Commands\UpdateChucVu\" />
    <Folder Include="DanhMuc\ChucVus\Queries\" />
    <Folder Include="DanhMuc\KeyBanQuyen\Commands\" />
    <Folder Include="DanhMuc\KeyBanQuyen\Queries\" />
    <Folder Include="DanhMuc\KichHoatKey\Commands\" />
    <Folder Include="DanhMuc\KichHoatKey\Queries\" />
    <Folder Include="DanhMuc\LinhVucs\Commnads\" />
    <Folder Include="DanhMuc\LoaiNguoiDung\Commands\" />
    <Folder Include="DanhMuc\PhanMem\Commands\" />
    <Folder Include="DanhMuc\ThanhPhanHoSoTTHCs\Commands\" />
    <Folder Include="DanhMuc\ThuTucHanhChinhs\Commands\CreateThuTucHanhChinh\" />
    <Folder Include="DanhMuc\ThuTucHanhChinhs\Commands\DeleteThuTucHanhChinh\" />
    <Folder Include="DanhMuc\ThuTucHanhChinhs\Commands\UpdateThuTucHanhChinh\" />
    <Folder Include="DanhMuc\ThuTucHanhChinhs\Queries\GetThuTucHanhChinhById\" />
    <Folder Include="DanhMuc\Tinh\Commands\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="WEB_DLL">
      <HintPath>lib\WEB_DLL.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
