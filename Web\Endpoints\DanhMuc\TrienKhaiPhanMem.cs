﻿using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.ChucVus.Queries.GetChucVuById;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.Commands;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.Queries;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.DeleteNhanVien;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.UpdateNhanVien;
using NHATTAMID2025.Application.DanhMuc.NhanViens.DTOs;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetAllNhanViens;
using NHATTAMID2025.Application.DanhMuc.NhanViens.Queries.GetNhanVienById;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Queries;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class TrienKhaiPhanMem : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(DeleteTrienKhaiPhanMem, "/delete")
            .MapPost(UpdateTrienKhaiPhanMem, "/update")
            .MapPost(CreateTrienKhaiPhanMem, "/create")
            .MapGet(GetAllTrienKhaiPhanMem, "/getall")
            .MapGet(GetTrienKhaiPhanMemById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<TrienKhaiPhanMemDto>?> CreateTrienKhaiPhanMem(ISender sender, [FromBody] CreateTrienKhaiPhanMemCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<TrienKhaiPhanMemDto>?> UpdateTrienKhaiPhanMem(ISender sender, [FromBody] UpdateTrienKhaiPhanMemCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<TrienKhaiPhanMemDto>?> DeleteTrienKhaiPhanMem(ISender sender, [FromBody] DeleteTrienKhaiPhanMemCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<TrienKhaiPhanMemDto>>?> GetAllTrienKhaiPhanMem(ISender sender)
     => await sender.Send(new GetAllTrienKhaiPhanMemCommand());
    public async Task<Result<List<TrienKhaiPhanMemDto>>?> GetTrienKhaiPhanMemById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetTrienKhaiPhanMemByIDQuery(id));

}
