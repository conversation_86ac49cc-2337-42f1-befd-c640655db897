﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.DangKyGiayPhep.Commands;
internal class UpdateDangKyGiayPhep
{
}
public record UpdateDangKyGiayPhepCommand(
    string DangKyGiayPhepID,
    string TrangThai,
    string NoiDungLienHe
    ) : IRequest<Result<DangKyGiayPhepDto>?>;

public class UpdateDangKyGiayPhepCommandValidator : AbstractValidator<UpdateDangKyGiayPhepCommand>
{
    public UpdateDangKyGiayPhepCommandValidator()
    {
    }
}

public class UpdateDangKyGiayPhepCommandHandler : IRequestHandler<UpdateDangKyGiayPhepCommand, Result<DangKyGiayPhepDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateDangKyGiayPhepCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<DangKyGiayPhepDto>?> Handle(UpdateDangKyGiayPhepCommand request, CancellationToken cancellationToken)
    {
        var parameters = new[]
        {
             new Microsoft.Data.SqlClient.SqlParameter("@DangKyGiayPhepID", DungChung.NormalizationGuid(request.DangKyGiayPhepID)),
             new Microsoft.Data.SqlClient.SqlParameter("@TrangThai", request.TrangThai),
             new Microsoft.Data.SqlClient.SqlParameter("@NoiDungLienHe", request.NoiDungLienHe),
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyGiayPhep_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<DangKyGiayPhepDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private DangKyGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DangKyGiayPhepDto
        {
            DangKyGiayPhepID = reader.GetGuid(reader.GetOrdinal("DangKyGiayPhepID")),
        };
    }
}

