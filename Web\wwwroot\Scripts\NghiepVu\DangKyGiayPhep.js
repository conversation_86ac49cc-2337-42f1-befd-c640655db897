﻿var ThaoTac;

$(document).ready( function () {

 
  LoadLuoi()

});


async function LoadLuoi() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/dangkyGiayPhep/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#keyBanQuyenTable').DataTable().destroy();
    $('#keyBanQuyenTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },
      columns: [
        {
          data: 'trangThai',
          width: "160px",
          render: function (data, type, row) {
            if (data == 0) {
              // Chưa liên hệ: nền vàng nhạt, chữ vàng đậm
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-warning" 
                   style="background-color: #fff8e1;">
                <i class="fas fa-phone-alt me-1"></i><span id="trangthai${row.dangKyGiayPhepID}">Chưa liên hệ</span>
              </span>`;
            } else if (data == 1) {
              // Đã liên hệ: nền xanh nhạt, chữ xanh đậm
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-success" 
                   style="background-color: #e6f4ea;">
                <i class="fas fa-check-circle me-1"></i><span id="trangthai${row.dangKyGiayPhepID}">Đã liên hệ</span>
              </span>`;
            } else {
              // Không rõ: nền xám nhạt, chữ xám
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-muted" 
                   style="background-color: #f0f0f0;">
                <i class="fas fa-question-circle me-1"></i><span id="trangthai${row.dangKyGiayPhepID}">Không rõ</span>
              </span>`;
            }
          }
        },
        {
          data: 'loaiDangKy',
          width: "180px",
          render: function (data, type, row) {
            if (data == 1) {
              // Đăng ký mới: nền xanh biển nhạt
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-primary"
                   style="background-color: #e0f0ff;">
                <i class="fas fa-user-plus me-1"></i>Đăng ký mới
              </span>`;
            } else if (data == 2) {
              // Gia hạn & nâng cấp: nền tím nhạt
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-purple"
                   style="background-color: #f3e8ff;">
                <i class="fas fa-arrow-up me-1"></i>Gia hạn & nâng cấp
              </span>`;
            } else {
              // Không rõ: nền xám nhạt
              return `<span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-muted"
                   style="background-color: #f0f0f0;">
                <i class="fas fa-question-circle me-1"></i>Không rõ
              </span>`;
            }
          }
        },
        { data: 'tenTaiKhoan', width: "90px" },
        { data: 'tenPhanMem', width: "90px" },
        {
          data: 'tenGoi',
          width: "140px",
          render: function (data, type, row) {
            if (!data) {
              return '';
            }

            return `
              <span class="d-inline-flex align-items-center px-3 py-1 rounded-pill fw-semibold text-dark" 
                    style="background-color: #e0f0ff;">
                <i class="fas fa-crown me-1 text-warning"></i> ${data}
              </span>`;
          }
        },
        { data: 'noiDung', width: "90px" },
        { data: 'ngayDangKy', width: "90px" },

        {
          data: 'noiDungLienHe',
          width: "300px",
          render: function (data, type, row, meta) {
            if (!data) return '';

            const maxLength = 40;
            const shortText = data.length > maxLength ? data.substring(0, maxLength) + '...' : data;

            if (data.length <= maxLength) {
              return `<span id="noidungtraodoi${row.dangKyGiayPhepID}">${data}</span>`;
            }

            return `
            <span class="short-text">${shortText}</span>
            <span class="full-text d-none" id="noidungtraodoi${row.dangKyGiayPhepID}">${data}</span>
            <a href="javascript:void(0)" class="toggle-moTa" style="color:#3182ce; font-size:12px; margin-left:6px;">Xem thêm</a>
        `;
          }
        },
        { data: 'ngayLienHe', width: "90px" },
        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "90px",
          className: 'align-middle text-start',
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.dangKyGiayPhepID}')">
                                            <i class="fa fa-comments text-success"></i>
                                        </button>
                                      
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}


async function editRow(id) {
  ResetPasswordVisibility()
  $('#DangKyGiayPhepID').value(id)
  $('#KhoaTaiKhoan').value(($('#trangthai' + id).text() == "Đã liên hệ" ? true : false))
  $('#noidungLienHe').value($('#noidungtraodoi' + id).text())

  
  const statusLabel = document.getElementById('modal-statusLabel');
  if ($('#KhoaTaiKhoan').value()) {
    statusLabel.textContent = 'Đã liên hệ';
    statusLabel.style.color = '#1d9a34'; // Xanh dương khi kích hoạt
  } else {
    statusLabel.textContent = 'Chưa liên hệ';
    statusLabel.style.color = '#f59f00'; // Đỏ khi khóa
  }
  
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/NguoiDung/getbyid/${localStorage.getItem("userID")}`, {});
  if (response.succeeded) {
    var originalData = response.result;

    $('#tenDangNhap').text(originalData[0].tenDangNhap) 
    $('#hovaten').text(originalData[0].hoVaTen)
    $('#soDienThoai').text(originalData[0].soDienThoai)
    $('#email').text(originalData[0].email)
    $('#donViCongTac').text(originalData[0].donViCongTac)

  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
  $('#userInfoModal').modal("show")
}
document.getElementById('KhoaTaiKhoan').addEventListener('change', function () {
  const statusLabel = document.getElementById('modal-statusLabel');
  if (this.checked) {
    statusLabel.textContent = 'Đã liên hệ';
    statusLabel.style.color = '#1d9a34'; // Xanh dương khi kích hoạt
  } else {
    statusLabel.textContent = 'Chưa liên hệ';
    statusLabel.style.color = '#f59f00'; // Đỏ khi khóa
  }
});
$('#KhoaTaiKhoan').on('change', function () {
  const isLocked = $(this).is(':checked');
  $('#modal-statusLabel').text(isLocked ? " Đã liên hệ" : "Chưa liên hệ");
}); 



$('#userInfoModalForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    DangKyGiayPhepID: $('#DangKyGiayPhepID').value(),
    TrangThai: ($('#KhoaTaiKhoan').value() == true ? '1' : '0'),
    NoiDungLienHe: $('#noidungLienHe').value(),
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/DangKyGiayPhep/update', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Xử lý yêu cầu khách hàng thành công!");
      }
      LoadLuoi()
      $('#userInfoModal').modal('hide');
      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Xử lý yêu cầu khách hàng thất bại!");
  }
});

$('#keyBanQuyenTable').on('click', '.toggle-moTa', function () {
  const $row = $(this).closest('td');
  const $short = $row.find('.short-text');
  const $full = $row.find('.full-text');

  if ($full.hasClass('d-none')) {
    $short.addClass('d-none');
    $full.removeClass('d-none');
    $(this).text('Ẩn bớt');
  } else {
    $short.removeClass('d-none');
    $full.addClass('d-none');
    $(this).text('Xem thêm');
  }
});