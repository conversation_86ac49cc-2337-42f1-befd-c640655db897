﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.ChucVus.Commands.Create;
using NHATTAMID2025.Application.ChucVus.Commands.DeleteChucVu;
using NHATTAMID2025.Application.ChucVus.Commands.UpdateChucVu;
using NHATTAMID2025.Application.ChucVus.Queries.GetAllChucVu;
using NHATTAMID2025.Application.ChucVus.Queries.GetChucVuById;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;


namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class ChucVuController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(CreateChucVu, "/")
            .MapPut(UpdateChucVu, "{id}")
            .MapDelete(DeleteChucVu, "{id}")
            .MapGet(GetChucVuById, "{id}")
            .MapGet(GetAllChucVu, "/");
    }

    public async Task<Result<object>> CreateChucVu(ISender sender, [FromBody] CreateChucVuCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<object>> UpdateChucVu(ISender sender, [FromRoute] Guid id, [FromBody] UpdateChucVuCommand command)
    {
        if (id != command.ChucVuID) throw new BadHttpRequestException("ID mismatch");
        return await sender.Send(command);

    }

    public async Task<Result<object>> DeleteChucVu(ISender sender, [FromRoute] Guid id)
    {
        var command = new DeleteChucVuCommand(id);
        return await sender.Send(command);
    }

    public async Task<ChucVuDto?> GetChucVuById(ISender sender, [FromRoute] Guid id)
        => await sender.Send(new GetChucVuByIdQuery(id));

    public async Task<List<ChucVuDto>> GetAllChucVu(ISender sender)
        => await sender.Send(new GetAllChucVuQuery());
}
