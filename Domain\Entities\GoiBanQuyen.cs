﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Domain.Entities;
public class GoiBan<PERSON>uyen
{
    [Key]
    public Guid GoiBanQuyenID { get; set; }
    public string? <PERSON><PERSON><PERSON> { get; set; }
    public string? TenGoi { get; set; }
    public string? MoTa { get; set; }
    public int? ThoiHanNgay { get; set; }
    public int? SoThietBiToiDa { get; set; }
    public Guid? LoaiNguoiDungID { get; set; }
    public decimal? GiaTien { get; set; }
    public bool? KichHoat { get; set; }
}
