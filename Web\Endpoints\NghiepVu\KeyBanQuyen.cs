﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Commands;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Queries;

namespace NHATTAMID2025.Web.Endpoints.NghiepVu;
public class KeyBanQuyen : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(DeleteKeyBanQuyen, "/delete")
            .MapPost(UpdateKeyBanQuyen, "/update")
            .MapPost(Create<PERSON>eyBanQuyen, "/create")
            .MapGet(GetAllKeyBanQuyen, "/getall")
            .MapGet(GetKeyBanQuyenById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<KeyBanQuyenDto>?> CreateKeyBanQuyen(ISender sender, [FromBody] CreateKeyBanQuyenCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<KeyBanQuyenDto>?> UpdateKeyBanQuyen(ISender sender, [FromBody] UpdateKeyBanQuyenCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<KeyBanQuyenDto>?> DeleteKeyBanQuyen(ISender sender, [FromBody] DeleteKeyBanQuyenCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<KeyBanQuyenDto>>?> GetAllKeyBanQuyen(ISender sender)
     => await sender.Send(new GetAllKeyBanQuyenCommand());
    public async Task<Result<List<KeyBanQuyenDto>>?> GetKeyBanQuyenById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetKeyBanQuyenByIDQuery(id));
}
