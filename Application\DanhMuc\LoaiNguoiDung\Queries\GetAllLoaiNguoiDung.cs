﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.LoaiNguoiDung.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.LoaiNguoiDung.Queries;
public record GetAllLoaiNguoiDungCommand : IRequest<Result<List<LoaiNguoiDungDto>>?>;

public class GetAllLoaiNguoiDungCommandValidator : AbstractValidator<GetAllLoaiNguoiDungCommand>
{
    public GetAllLoaiNguoiDungCommandValidator()
    {
    }
}

public class GetAllLoaiNguoiDungCommandHandler : IRequestHandler<GetAllLoaiNguoiDungCommand, Result<List<LoaiNguoiDungDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllLoaiNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<LoaiNguoiDungDto>>?> Handle(GetAllLoaiNguoiDungCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_LoaiNguoiDung_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<LoaiNguoiDungDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private LoaiNguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new LoaiNguoiDungDto
        {
            LoaiNguoiDungID = reader.GetGuid(reader.GetOrdinal("LoaiNguoiDungID")),
            MaLoaiNguoiDung = reader["MaLoaiNguoiDung"] as string,
            TenLoaiNguoiDung = reader["TenLoaiNguoiDung"] as string,
        };

    }
}

