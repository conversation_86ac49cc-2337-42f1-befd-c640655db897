﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.LienKetTaiKhoan.Commands;
internal class CreateLienKetTaiKhoan
{
}
public record CreateLienKetTaiKhoanCommand(
    string Email,
    string UserIDLienKet,
    string TenDNLienKet,
    string Link,
    string code
    ) : IRequest<Result<LienKetTaiKhoanDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateLienKetTaiKhoanCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateLienKetTaiKhoanCommandHandler : IRequestHandler<CreateLienKetTaiKhoanCommand, Result<LienKetTaiKhoanDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateLienKetTaiKhoanCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<LienKetTaiKhoanDto>?> Handle(CreateLienKetTaiKhoanCommand request, CancellationToken cancellationToken)
    {
        var ma = await _context.MaXacThuc.Where(x => x.Email == request.Email + request.UserIDLienKet + request.Link && x.MaXacThuc.ToLower() == request.code.ToLower().Trim())
                            .OrderByDescending(x => x.ThoiGianTao)
                            .FirstOrDefaultAsync();
        if (ma == null)
            return Result<LienKetTaiKhoanDto>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<LienKetTaiKhoanDto>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<LienKetTaiKhoanDto>.Failure(["Mã xác thực đã hết hạn."]);
        ma.TrangThai = "Used";
        await _context.SaveChangesAsync(cancellationToken);

        string tenThietBi = Environment.MachineName;
        string ipKichHoat = GetLocalIPAddress();
        string heDieuHanh = System.Runtime.InteropServices.RuntimeInformation.OSDescription;
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@UserIDLienKet", request.UserIDLienKet),
            new Microsoft.Data.SqlClient.SqlParameter("@TenDNLienKet", request.TenDNLienKet),
            new Microsoft.Data.SqlClient.SqlParameter("@TenThietBiLienKet", tenThietBi),
            new Microsoft.Data.SqlClient.SqlParameter("@IPLienKet", ipKichHoat),
            new Microsoft.Data.SqlClient.SqlParameter("@HeDieuHanh", heDieuHanh),
            new Microsoft.Data.SqlClient.SqlParameter("@Link", request.Link)
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_LienKetTaiKhoan_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<LienKetTaiKhoanDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }
    string GetLocalIPAddress()
    {
        var host = Dns.GetHostEntry(Dns.GetHostName());
        foreach (var ip in host.AddressList)
        {
            if (ip.AddressFamily == AddressFamily.InterNetwork)
            {
                return ip.ToString();
            }
        }
        return "127.0.0.1"; // fallback
    }

    private LienKetTaiKhoanDto MapFromReader(System.Data.Common.DbDataReader reader)
    {

        return new LienKetTaiKhoanDto
        {
            LienKetTaiKhoanID = reader.GetGuid(reader.GetOrdinal("LienKetTaiKhoanID"))
        };
    }
}
