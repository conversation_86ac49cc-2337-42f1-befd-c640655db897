﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Authentication;
using NHATTAMID2025.Web.WebClient.Class;

namespace NHATTAMID2025.Web.WebClient.Controllers.HeThong;

[ApiExplorerSettings(IgnoreApi = true)]
[Route("hethong/{controller}")]
public class DangKyUserController : Controller
{
    public IActionResult Index()
    {
        return View();
    }
    [Route("getall")]
    public ExecPermiss GetAll()
    {
        ExecPermiss ep = new ExecPermiss();
        TokenResponse token = new TokenResponse();
        ep.Result = token.ToString() ?? "";
        return ep; 
    }
}
