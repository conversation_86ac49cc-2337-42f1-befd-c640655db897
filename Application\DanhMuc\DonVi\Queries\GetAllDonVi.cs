﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.DonVi.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonVi.Queries;
internal class GetAllDonVi
{
}
public record GetAllDonViCommand : IRequest<Result<List<DonViDto>>?>;

public class GetAllDonViCommandValidator : AbstractValidator<GetAllDonViCommand>
{
    public GetAllDonViCommandValidator()
    {
    }
}

public class GetAllDonViCommandHandler : IRequestHandler<GetAllDonViCommand, Result<List<DonViDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllDonViCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<DonViDto>>?> Handle(GetAllDonViCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DonVi_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<DonViDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private DonViDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViDto
        {
            DonViID = reader.GetGuid(reader.GetOrdinal("DonViID")),
            MaDonVi = reader["MaDonVi"] as string,
            MaQHNS = reader["MaQHNS"] as string,
            TenDonVi = reader["TenDonVi"] as string,
            Email = reader["Email"] as string,
            DiaChi = reader["DiaChi"] as string,
            DienThoai = reader["DienThoai"] as string,
            Website = reader["Website"] as string,
            MaSoThue = reader["MaSoThue"] as string,
            TinhID = reader.IsDBNull(reader.GetOrdinal("TinhID"))
                     ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("TinhID")),
            TenTinh = reader["TenTinh"] as string,
            MaTinh = reader["MaTinh"] as string,
            XaID = reader.IsDBNull(reader.GetOrdinal("XaID"))
                   ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("XaID")),
            TenXa = reader["TenXa"] as string,
            MaXa = reader["MaXa"] as string,
            DonViID_Cha = reader.IsDBNull(reader.GetOrdinal("DonViID_Cha"))
                          ? (Guid?)null : reader.GetGuid(reader.GetOrdinal("DonViID_Cha")),
            MaDonViCha = reader["MaDonViCha"] as string,
            TenDonVi_Cha = reader["TenDonVi_Cha"] as string,
        };
    }
}

