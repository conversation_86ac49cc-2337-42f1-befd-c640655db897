﻿using System.Data;
using System.Data.Common;
using System.Reflection;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using NHATTAMID2025.Application.Common.Interfaces;

using NHATTAMID2025.Domain.Entities;
using NHATTAMID2025.Infrastructure.Identity;

namespace NHATTAMID2025.Infrastructure.Data;

public class ApplicationDbContext : DbContext, IApplicationDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options) { }


    public DbSet<NhanVien> NhanVien => Set<NhanVien>();
    public DbSet<ChucVu> ChucVu => Set<ChucVu>();
    public DbSet<DonViTinh> DonViTinh => Set<DonViTinh>();
    public DbSet<Users> Users => Set<Users>();
    public DbSet<MaXacThucEntity> MaXacThuc => Set<MaXacThucEntity>();
    public DbSet<TrienKhaiPhanMem> TrienKhaiPhanMem => Set<TrienKhaiPhanMem>();
    public DbSet<PhanMem> PhanMem => Set<PhanMem>();
    public DbSet<LoaiNguoiDung> LoaiNguoiDung => Set<LoaiNguoiDung>();
    public DbSet<GoiBanQuyen> GoiBanQuyen => Set<GoiBanQuyen>();
    public DbSet<KeyBanQuyen> KeyBanQuyen => Set<KeyBanQuyen>();
    public DbSet<KichHoatKey> KichHoatKey => Set<KichHoatKey>();
    public DbSet<Tinh> Tinh => Set<Tinh>();
    public DbSet<DonVi> DonVi => Set<DonVi>();
    public DbSet<ChiaSeKey> ChiaSeKey => Set<ChiaSeKey>();
    public DbSet<ThanhPhanHoSoTTHC> ThanhPhanHoSoTTHC => Set<ThanhPhanHoSoTTHC>();
    public DbSet<ThuTucHanhChinh> ThuTucHanhChinh => Set<ThuTucHanhChinh>();
    public DbSet<LinhVuc> LinhVuc => Set<LinhVuc>();
    public DbSet<DangKyGiayPhep> DangKyGiayPhep => Set<DangKyGiayPhep>();
    public DbSet<LienKetTaiKhoan> LienKetTaiKhoan => Set<LienKetTaiKhoan>();
    public DbSet<PhanQuyen> PhanQuyen => Set<PhanQuyen>();
    public DbSet<Xa> Xa => Set<Xa>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity User nếu cần (ví dụ: khóa chính, bảng, cột,...)
        modelBuilder.Entity<Users>(entity =>
        {
            entity.HasKey(e => e.UserID);
            entity.Property(e => e.TenDangNhap).HasMaxLength(50);
            entity.Property(e => e.MatMa).HasMaxLength(256);
            // Các cấu hình khác nếu cần
        });

        modelBuilder.Entity<LinhVuc>(entity =>
        {
            entity.HasNoKey();
        });
    }

    /// <summary>
    /// Executes a raw SQL command against the database.
    /// </summary>
    public async Task<int> ExecuteSqlRawAsync(string sql, params object[] parameters)
    {
        // Use EF Core's Database property to execute raw SQL commands.
        return await Database.ExecuteSqlRawAsync(sql, parameters);
    }
    public async Task<List<T>> ExecuteSqlQueryRawAsync<T>(
    string sql,
    Func<DbDataReader, T> map,
    bool proc = false,
    params DbParameter[] parameters
    )
    {
        var conn = Database.GetDbConnection();
        await conn.OpenAsync();
        using var cmd = conn.CreateCommand();
        cmd.CommandText = sql;
        if (proc) cmd.CommandType = CommandType.StoredProcedure;
        cmd.Parameters.AddRange(parameters);
        using var reader = await cmd.ExecuteReaderAsync();
        var list = new List<T>();
        while (await reader.ReadAsync())
        {
            list.Add(map(reader));
        }
        await Database.CloseConnectionAsync();
        return list;
    }

}
