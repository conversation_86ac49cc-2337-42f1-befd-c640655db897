﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_LayoutInfo";
}

<style>
    body {
        background: linear-gradient(135deg, #ffffff 0%, #2baaff 100%);
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-position: center;
        font-family: 'Segoe UI', sans-serif;
    }

 

    .register-container {
        max-width: 400px;
        margin: 40px auto;
        background: #fff;
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 0 10px rgba(0,0,0,0.05);
    }

    .logo-row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10px;
    }

        .logo-row img {
            height: 40px;
            margin: 0 10px;
        }

    h2 {
        font-size: 24px;
        font-weight: 600;
        text-align: center;
    }

    .form-label {
        font-weight: 500;
    }

    .row.g-2 > .col {
        flex: 1;
    }

    .password-rules {
        font-size: 13px;
        color: #4caf50;
        margin-top: 10px;
    }

        .password-rules li {
            margin-bottom: 4px;
        }

    .btn-social img {
        height: 20px;
        margin-right: 8px;
    }

    .btn-social {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 1px solid #ddd;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin: 0 4px;
    }

    .text-muted a {
        color: #0d6efd;
        text-decoration: none;
    }


    .btn-primary {
        --tblr-btn-border-color: transparent;
        --tblr-btn-hover-border-color: transparent;
        --tblr-btn-active-border-color: transparent;
        --tblr-btn-color: var(--tblr-primary-fg);
        --tblr-btn-bg: #74bdff;
        --tblr-btn-hover-color: var(--tblr-primary-fg);
        --tblr-btn-hover-bg: rgba(var(--tblr-primary-rgb), .8);
        --tblr-btn-active-color: var(--tblr-primary-fg);
        --tblr-btn-active-bg: rgba(var(--tblr-primary-rgb), .8);
        --tblr-btn-disabled-bg: var(--tblr-primary);
        --tblr-btn-disabled-color: var(--tblr-primary-fg);
        --tblr-btn-box-shadow: var(--tblr-box-shadow-input);
    }

    .btn-check:checked + .btn, .btn.active, .btn.show, .btn:first-child:active, :not(.btn-check) + .btn:active {
        color: #664d92;
        background-color: var(--tblr-btn-active-bg);
        border-color: #ddd7e7;
        box-shadow: var(--tblr-btn-active-shadow);
    }


</style>

<div class="register-container">
   

    <form id="registerForm" >
        <div class="logo-row">
            <img src="/Images/logo-calender.png" alt="NTSOFT Logo" />
        </div>
        <h2 style="font-size: 24px;
    font-weight: bold;
    font-style: normal;
    font-stretch: normal;
    line-height: 1.21;
    letter-spacing: normal;
    text-align: center;
color: #4d5156;">
            Tạo tài khoản
        </h2>
        <p class="text-center text-muted">Một tài khoản sử dụng tất cả ứng dụng NTSOFT</p>
        <div class="row g-2 mb-3">
            <div class="col">
                <input type="text" id="hoTen" class="form-control" placeholder="Họ và tên" required>
            </div>
            <div class="col">



                <div class="btn-group w-100" role="group" style="margin-bottom: -11px; box-shadow: 0 0 0 rgba(24, 36, 51, 0.06);">
                    <input type="radio" class="btn-check" name="gioitinh" id="btn-radio-toolbar-1" value="Nam" checked="">
                    <label for="btn-radio-toolbar-1" class="btn btn-icon" title="Nam">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-gender-male" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" style="color: #24922b;">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <circle cx="11" cy="13" r="7" />
                            <line x1="21" y1="3" x2="14.65" y2="9.35" />
                            <line x1="21" y1="3" x2="15" y2="3" />
                            <line x1="21" y1="3" x2="21" y2="9" />
                        </svg>
                    </label>
                    <input type="radio" class="btn-check" name="gioitinh" id="btn-radio-toolbar-2" value="Nữ">
                    <label for="btn-radio-toolbar-2" class="btn btn-icon" title="Nữ">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color:#fa3aff">
                            <circle cx="12" cy="6" r="4"></circle>
                            <line x1="12" y1="10" x2="12" y2="22"></line>
                            <line x1="9" y1="19" x2="15" y2="19"></line>
                        </svg>
                        
                    </label>
                    
                </div>



            </div>
        </div>

        <div class="mb-3">
            <input type="text" id="email" class="form-control" placeholder="Email" required>
        </div>

        <div class="mb-3">
            <input type="text" id="tel" class="form-control" placeholder="Số điện thoại" required>
        </div>

        <div class="mb-3">
            <input type="text" id="tenDangNhap" class="form-control" placeholder="Tên người dùng" required>
        </div>

    
        <div class="mb-3">
            <div class="input-group input-group-flat" data-toggle-password>
                <input type="password" id="password" class="form-control" placeholder="Mật khẩu" required />
                <span class="input-group-text">
                    <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip" data-password-toggle>
                        <!-- Icon con mắt -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-show d-none" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0"></path>
                            <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6"></path>
                        </svg>
                        <!-- Icon con mắt gạch chéo (ẩn) -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-hide" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M3 3l18 18" />
                            <path d="M10.584 10.587a2.99 2.99 0 0 0 2.829 2.828" />
                            <path d="M9.37 5.365c.996-.248 2.065-.365 3.13-.365 3.5 0 6.5 2 9 6 -1.098 1.757 -2.354 3.147 -3.768 4.168" />
                            <path d="M6.443 6.429c-1.426.96 -2.692 2.35 -3.77 4.071 2.5 4 5.5 6 9 6 1.108 0 2.207-.193 3.261-.574" />
                        </svg>
                    </a>
                </span>
            </div>
        </div>

        <div class="mb-3">
            <div class="input-group input-group-flat" data-toggle-password>
                <input type="password" id="nhaplaipassword" class="form-control" placeholder="Nhập lại mật khẩu" required />
                <span class="input-group-text">
                    <a href="#" class="link-secondary" title="Hiện mật khẩu" data-bs-toggle="tooltip" data-password-toggle>
                        <!-- Icon con mắt -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-show d-none" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                            <path d="M15 12a3 3 0 1 0 -6 0 3 3 0 0 0 6 0"></path>
                            <path d="M2 12c2.5 -4 6.5 -6 10 -6s7.5 2 10 6c-2.5 4 -6.5 6 -10 6s-7.5 -2 -10 -6"></path>
                        </svg>
                        <!-- Icon con mắt gạch chéo (ẩn) -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon eye-hide" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M3 3l18 18" />
                            <path d="M10.584 10.587a2.99 2.99 0 0 0 2.829 2.828" />
                            <path d="M9.37 5.365c.996-.248 2.065-.365 3.13-.365 3.5 0 6.5 2 9 6 -1.098 1.757 -2.354 3.147 -3.768 4.168" />
                            <path d="M6.443 6.429c-1.426.96 -2.692 2.35 -3.77 4.071 2.5 4 5.5 6 9 6 1.108 0 2.207-.193 3.261-.574" />
                        </svg>
                    </a>
                </span>
            </div>
        </div>

        <ul class="password-rules list-unstyled">
        </ul>

        <p class="text-muted small mt-2">
            Nhấn đăng ký, bạn đã đồng ý với
            <a href="#">Chính sách quyền riêng tư</a>
        </p>

        <button type="submit" class="btn btn-primary w-100">Đăng ký</button>

        <p class="text-center mt-3 mb-2">Đã có tài khoản? <a href="/hethong/dangnhap">Đăng nhập</a></p>
      
        
    </form>
    <div class="verify-container d-none" id="verifyFormContainer">
        <h2>Xác nhận Email</h2>
        <p class="text-muted">Chúng tôi đã gửi mã xác nhận tới địa chỉ email của bạn.</p>

        <form id="verifyForm">
            <div class="mb-3">
                <div class="d-flex justify-content-center gap-2 mb-3" id="codeInputs">
                    <input type="text" style="text-align:center" maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center"  maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center"  maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center"  maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center"  maxlength="1" class="code-input form-control" />
                    <input type="text" style="text-align:center"  maxlength="1" class="code-input form-control" />
                </div>
            </div>

            <button type="submit" class="btn btn-success w-100">Xác nhận</button>

            <p class="text-center mt-2">
                Không nhận được mã?
                <a href="#" id="resendCode">Gửi lại mã</a>
            </p>
        </form>
    </div>
</div>


<script src="~/scripts/dangkyuser.js"></script>