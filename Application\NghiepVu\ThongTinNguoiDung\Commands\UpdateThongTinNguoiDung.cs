﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.Commands;
public record UpdateThongTinNguoiDungCommand(
    string UserID,
    string Avatar,
    string HoVaTen,
    string GioiTinh,
    string NgaySinh,
    string SoDienThoai,
    string Email,
    string DiaChi,
    string DonViCongTac,
    string CarNoiDung,
    string DonViCongTacID
    ) : IRequest<Result<ThongTinNguoiDungDto>?>;

public class UpdateThongTinNguoiDungCommandValidator : AbstractValidator<UpdateThongTinNguoiDungCommand>
{
    public UpdateThongTinNguoiDungCommandValidator()
    {
    }
}

public class UpdateThongTinNguoiDungCommandHandler : IRequestHandler<UpdateThongTinNguoiDungCommand, Result<ThongTinNguoiDungDto>?>
{
    private readonly IApplicationDbContext _context;

    public UpdateThongTinNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<ThongTinNguoiDungDto>?> Handle(UpdateThongTinNguoiDungCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@UserID", DungChung.NormalizationGuid(request.UserID)),
             new Microsoft.Data.SqlClient.SqlParameter("@Avatar", request.Avatar),
            new Microsoft.Data.SqlClient.SqlParameter("@HoVaTen", request.HoVaTen),
            new Microsoft.Data.SqlClient.SqlParameter("@GioiTinh", request.GioiTinh),
             new Microsoft.Data.SqlClient.SqlParameter("@NgaySinh",DungChung.NormalizationDateTime(request.NgaySinh)),
            new Microsoft.Data.SqlClient.SqlParameter("@SoDienThoai",request.SoDienThoai),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@DiaChi", request.DiaChi),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViCongTac", request.DonViCongTac),
            new Microsoft.Data.SqlClient.SqlParameter("@CarNoiDung", request.CarNoiDung),
              new Microsoft.Data.SqlClient.SqlParameter("@DonViCongTacID", DungChung.NormalizationGuid(request.DonViCongTacID)),
            
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ThongTinNguoiDung_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<ThongTinNguoiDungDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private ThongTinNguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ThongTinNguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
        };
    }
}

