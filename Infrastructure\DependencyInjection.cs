﻿using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;
using NHATTAMID2025.Domain.Constants;
using NHATTAMID2025.Infrastructure.Data;
using NHATTAMID2025.Infrastructure.Data.Interceptors;
using NHATTAMID2025.Infrastructure.Identity;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection");

        Guard.Against.Null(connectionString, message: "Connection string 'DefaultConnection' not found.");

        services.AddScoped<ISaveChangesInterceptor, AuditableEntityInterceptor>();
        services.AddScoped<ISaveChangesInterceptor, DispatchDomainEventsInterceptor>();

        services.AddDbContext<ApplicationDbContext>((sp, options) =>
        {
            options.AddInterceptors(sp.GetServices<ISaveChangesInterceptor>());

            options.UseSqlServer(connectionString);
        });

        services.AddScoped<IApplicationDbContext>(provider => provider.GetRequiredService<ApplicationDbContext>());

        services.AddScoped<ApplicationDbContextInitialiser>();

        // Lấy phần cấu hình JWT từ file appsettings.json (hoặc nguồn cấu hình khác)
        var jwtSection = configuration.GetSection("Jwt");

        // Mã hóa khóa bí mật JWT từ cấu hình
        var key = Encoding.UTF8.GetBytes(jwtSection["Key"] ?? "");

        // Thêm dịch vụ xác thực (Authentication) sử dụng JWT Bearer
        services.AddAuthentication(options =>
        {
            // Thiết lập scheme mặc định để xác thực và xử lý challenge là JWT Bearer
            options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
        })
        .AddJwtBearer(options =>
        {
            options.TokenValidationParameters = new TokenValidationParameters
            {
                // Bắt buộc phải kiểm tra chữ ký của token
                ValidateIssuerSigningKey = true,

                // Thiết lập khóa bí mật để xác thực token
                IssuerSigningKey = new SymmetricSecurityKey(key),

                // Kiểm tra thông tin Issuer trong token có hợp lệ không
                ValidateIssuer = true,
                ValidIssuer = jwtSection["Issuer"],

                // Không bắt buộc kiểm tra Audience (có thể bật nếu cần)
                ValidateAudience = false,
                ValidAudience = jwtSection["Audience"],

                // Bắt buộc kiểm tra thời gian sống (hết hạn) của token
                ValidateLifetime = true,

                // Không cho phép thời gian "trễ" khi hết hạn (mặc định là 5 phút)
                ClockSkew = TimeSpan.Zero
            };


            options.Events = new JwtBearerEvents
            {
                OnMessageReceived = context =>
                {
                    var accessToken = context.Request.Cookies["access_token"];
                    if (!string.IsNullOrEmpty(accessToken))
                    {
                        context.Token = accessToken;
                    }
                    return Task.CompletedTask;
                }
            };
        });

        services.AddAuthorizationBuilder();

        

        services.AddSingleton(TimeProvider.System);
        ////services.AddTransient<IIdentityService, IdentityService>();

        services.AddAuthorization(options =>
            options.AddPolicy(Policies.CanPurge, policy => policy.RequireRole(Roles.Administrator)));

        return services;
    }
}
