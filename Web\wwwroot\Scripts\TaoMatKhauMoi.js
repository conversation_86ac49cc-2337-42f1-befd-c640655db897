﻿const baseUrl = "https://localhost:5001"; // 👈 sửa lại đúng địa chỉ baseUrl của bạn
  $('#registerForm').on('submit', async function (e) {

    e.preventDefault();
  debugger
  NTS.loadding()
  // Validate đơn giản
  const data = {
    Email: sessionStorage.getItem('resetEmail'),
    MatMaMoi: $('#passwordmoi').val(),
    NhapLaiMatMaMoi: $('#passwordnhaplai').val(),
    MaXatNhan: sessionStorage.getItem('resetMaXatNhan')
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', baseUrl + '/api/TaoMatKhauMoiController', data);
    $('#registerForm').addClass('d-none');
    $('#verifyFormContainer').removeClass('d-none');
  } catch (error) {
    if (error && error.responseJSON && error.responseJSON.errors) {
      const errors = error.responseJSON.errors;

      // Lấy phần tử UL chứa các quy tắc mật khẩu
      const $rulesList = $('.password-rules');
      $rulesList.empty(); // Xóa các quy tắc cũ (mặc định)

      // Duyệt tất cả các lỗi
      for (let field in errors) {
        const fieldErrors = errors[field];

        fieldErrors.forEach(msg => {
          const $li = $('<li></li>').text('❌ ' + msg).css('color', 'red');
          $rulesList.append($li);
        });
      }
    } else {
      alert('Đăng ký thất bại. Lỗi không xác định.');
      console.error(error);
    }

  }
  NTS.unloadding()
});