﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs;
using WEB_DLL;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace NHATTAMID2025.Application.NghiepVu.KichHoatKey.Commands;

public record CreateKichHoatKeyCommand(
    string code,
    string MaKey,
    string Link,
    string Email,
    string HoTenKichHoat,
    string DonViCongTac
    ) : IRequest<Result<KichHoatKeyDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateKichHoatKeyCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateKichHoatKeyCommandHandler : IRequestHandler<CreateKichHoatKeyCommand, Result<KichHoatKeyDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateKichHoatKeyCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<KichHoatKeyDto>?> Handle(CreateKichHoatKeyCommand request, CancellationToken cancellationToken)
    {
        var ma = await _context.MaXacThuc.Where(x => x.Email == request.Email && x.MaXacThuc.ToLower() == request.code.ToLower().Trim())
                            .OrderByDescending(x => x.ThoiGianTao)
                            .FirstOrDefaultAsync();

        if (ma == null)
            return Result<KichHoatKeyDto>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<KichHoatKeyDto>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<KichHoatKeyDto>.Failure(["Mã xác thực đã hết hạn."]);
        ma.TrangThai = "Used";
        await _context.SaveChangesAsync(cancellationToken);

        string tenThietBi = Environment.MachineName;
        string ipKichHoat = GetLocalIPAddress();
        string heDieuHanh = System.Runtime.InteropServices.RuntimeInformation.OSDescription;
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@MaKey", request.MaKey),
            new Microsoft.Data.SqlClient.SqlParameter("@NguoiKichHoat", request.HoTenKichHoat),
            new Microsoft.Data.SqlClient.SqlParameter("@EmailKichHoat", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViKichHoat", request.DonViCongTac),
            new Microsoft.Data.SqlClient.SqlParameter("@Link", request.Link),
            new Microsoft.Data.SqlClient.SqlParameter("@TenThietBi", tenThietBi ?? (object)DBNull.Value),
            new Microsoft.Data.SqlClient.SqlParameter("@IPKichHoat", ipKichHoat ?? (object)DBNull.Value),
            new Microsoft.Data.SqlClient.SqlParameter("@HeDieuHanh", heDieuHanh ?? (object)DBNull.Value)
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KichHoatKey_Create",
                   MapFromReader,
                   true,
                   parameters);
            return Result<KichHoatKeyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }
    string GetLocalIPAddress()
    {
        var host = Dns.GetHostEntry(Dns.GetHostName());
        foreach (var ip in host.AddressList)
        {
            if (ip.AddressFamily == AddressFamily.InterNetwork)
            {
                return ip.ToString();
            }
        }
        return "127.0.0.1"; // fallback
    }

    private KichHoatKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
  
        return new KichHoatKeyDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
            NgayKichHoat = reader.IsDBNull(reader.GetOrdinal("NgayKichHoat"))
                     ? null
                     : reader.GetDateTime(reader.GetOrdinal("NgayKichHoat")),
            NgayhetHan = reader.IsDBNull(reader.GetOrdinal("NgayhetHan"))
                    ? null
                    : reader.GetDateTime(reader.GetOrdinal("NgayHetHan"))
        };
    }
}
