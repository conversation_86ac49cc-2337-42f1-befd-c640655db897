﻿using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.ChucVus.DTOs;

namespace NHATTAMID2025.Application.ChucVus.Queries.GetChucVuById;

public record GetChucVuByIdQuery(Guid ChucVuID) : IRequest<ChucVuDto?>;


public class GetChucVuByIdQueryValidator : AbstractValidator<GetChucVuByIdQuery>
{
    public GetChucVuByIdQueryValidator()
    {
    }
}

public class GetChucVuByIdQueryHandler : IRequestHandler<GetChucVuByIdQuery, ChucVuDto?>
{
    private readonly IApplicationDbContext _context;

    public GetChucVuByIdQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<ChucVuDto?> Handle(GetChucVuByIdQuery request, CancellationToken cancellationToken)
    {
        const string spGetById = "sp_ChucVu_GetById";

        var param = new Microsoft.Data.SqlClient.SqlParameter("ChucVuID", request.ChucVuID);

        var list = await _context.ExecuteSqlQueryRawAsync(
            spGetById,
            MapFromReader,
            true,
            param
        );

        return list.FirstOrDefault();
    }

    private ChucVuDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChucVuDto
        {
            ChucVuID = reader.GetGuid(reader.GetOrdinal("ChucVuID")),
            MaChucVu = reader["MaChucVu"] as string,
            TenChucVu = reader["TenChucVu"] as string,
            DienGiai = reader["DienGiai"] as string,
            NgungTD = reader.IsDBNull(reader.GetOrdinal("NgungTD")) ? null : reader.GetBoolean(reader.GetOrdinal("NgungTD"))
        };
    }
}
