﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}

<style>
    .form-switch .form-check-input {
        transition: all 0.2s ease-in-out;
    }

    .form-switch .form-check-input:not(:checked) {
            background-color: #f59f00; /* <PERSON><PERSON>u đỏ khi tắt */
    }
    .highlight-section {
        background-color: #f8f9fa; /* N<PERSON>n sáng nhẹ */
        padding: 10px;
        border-radius: 5px;
        border-left: 4px solid #0054a6; /* Viền trái xanh dương nổi bật */
    }
    #ThoiHanLoaiinput .ts-wrapper.form-select.single {
        height: 30px !important;
        min-height: 30px !important;
        padding: 0 !important;
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
        margin-left:0px;
        border-top-right-radius: 0px !important;
        border-bottom-right-radius: 0px !important;
        display: flex;
        align-items: center;
        border: 1px solid #dce9f5;
        background-color: #fff;
        font-size: 13px;
    }

    /* ✅ Khi focus (đượ<PERSON> click vào) */
    #ThoiHanLoaiinput .ts-wrapper.form-select.single.focus {
       border-color: #83b7e8 !important;
    }

    .form-switch .form-check-input:checked {
        background-color: #40a863;
    }
</style>
<style>
    .badge.bg-success {
        background-color: #4caf50; /* màu xanh lá tươi, dễ chịu */
    }
    .badge.bg-danger {
        background-color: #e57373; /* màu đỏ nhạt dịu mắt */
    }
    /* icon select + padding giữ nguyên */
    .input-icon .form-select.ps-10 {
        padding-left: 2.75rem !important;
    }
    .input-icon-addon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
    }

</style>

<div class="card cardview" style="border-radius: 0 !important;">
    <div class="card-header">
        <div class="search-box">
            <h2 class="navbar-brand mb-0">YÊU CẦU GIA HẠN & NÂNG CẤP GÓI</h2>
        </div>
        <div class="card-actions">
           
        </div>
    </div>
    <div class="card-body">
        <table id="keyBanQuyenTable" class="table table-bordered table-hover custom-table" style="width: 100%">
            <thead>
                <tr>
                    <th>Trạng thái</th>
                    <th>Hình thức</th>
                    <th>Tài khoản</th>
                    <th>Tên phần mềm</th>
                    <th>Tên gói</th>
                    <th>Yêu cầu</th>
                    <th>Ngày yêu cầu</th>
                    <th>Nội dung trao đổi</th>
                    <th>Ngày trao đổi</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody id="table-body"></tbody>
        </table>
    </div>
</div>


<div class="modal modal-blur fade" id="userInfoModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="userInfoModalForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="alert alert-warning d-flex align-items-start" role="alert">
                        <i class="fas fa-info-circle me-2 mt-1 text-warning" style="font-size: 20px;"></i>
                        <div>
                            <h4 class="alert-title mb-1 text-warning">Thông báo hỗ trợ khách hàng</h4>
                            <div class="text-secondary">
                                Bạn đang xem thông tin yêu cầu <strong>đăng ký</strong> hoặc <strong>nâng cấp gói phần mềm</strong> từ khách hàng.<br>
                                Hãy kiểm tra thông tin và <strong>liên hệ khách hàng</strong> để xác nhận và hỗ trợ theo đúng quy trình.
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Tên đăng nhập</label>
                        <p id="tenDangNhap" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Họ và tên</label>
                        <p id="hovaten" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <p id="email" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện thoại</label>
                        <p id="soDienThoai" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đơn vị công tác</label>
                        <p id="donViCongTac" class="text-muted"></p>
                    </div>

                    <div class="mb-3 highlight-section">
                        <label class="form-label">Trạng thái xử lý</label>
                        <div class="">
                            <label class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="KhoaTaiKhoan" name="KhoaTaiKhoan" >
                                <span class="status-label" id="modal-statusLabel">Chưa liên hệ</span>
                            </label>
                            <div class="form-text text-muted mt-1">
                                Đánh dấu khi bạn đã liên hệ với khách hàng để xác nhận yêu cầu.
                            </div>
                            <div class="mb-3" style="margin-top:4px">
                                <label class="form-label">Thông tin ghi nhận sau khi trao đổi với khách hàng</label>
                                <textarea class="form-control" rows="3" id="noidungLienHe" placeholder="Nhập nội dung ghi nhận sau khi trao đổi với khách hàng." required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Lưu
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>

<!-- Hidden để lưu ID -->
<input type="hidden" id="DangKyGiayPhepID" />
<script src="~/scripts/nghiepvu/DangKyGiayPhep.js"></script>
