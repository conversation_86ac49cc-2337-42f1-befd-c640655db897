﻿#filter-panel {
    transition: width 0.3s ease, opacity 0.3s ease;
    width: 20vw; /* default visible width */
    opacity: 1;
}

.filter-hidden {
    width: 0;
    opacity: 0;
    overflow: hidden; /* hides inner content when collapsed */
}


body {
    font-family: sans-serif;
}

.filter-container {
    display: flex;
    flex-direction: column;
    max-width: 300px;
    margin-bottom: 20px;
    gap: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

    .filter-group label {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .filter-group select,
    .filter-group input[type="text"] {
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}
.tabulator .tabulator-cell,
.tabulator .tabulator-col {
    border-right: none !important;
}
/* Base style for tab buttons */
.nav-tabs .nav-link {
    /*background-color: #f8f9fa;*/ /* Light background */
    color: #102D4F; /* Dark text */
    border: none; /* Remove default border */
    border-bottom: 2px solid transparent; /* Reserve space for the underline */
    padding: 10px 15px;
    font-size: 1rem;
    transition: background-color 0.2s ease, border-bottom 0.2s ease;
}

/* Ensure no extra bottom margins interfering */
.nav-tabs {
    margin-bottom: 0;
}


.nav-tabs .nav-link.active {
    background-color: transparent;
        color: #007bff; /* Change to your desired text color */
        border-bottom: 2px solid #007bff !important; /* Sets the underline or border to the same color */
}