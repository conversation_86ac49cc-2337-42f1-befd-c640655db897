﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NHATTAMID2025.Domain.Entities;
public class LienKetTaiKhoan
{
    [Key]
    public Guid LienKetTaiKhoanID { get; set; }

    public Guid UserIDNtsoft { get; set; }

    public string? UserIDLienKet { get; set; }

    public string? TenDNLienKet { get; set; }

    public DateTime? NgayLienKet { get; set; }

    public Guid TrienKhaiPhanMemID { get; set; }

    public string? TenThietBiLienKet { get; set; }

    public string? IPLienKet { get; set; }

    public string? HeDieuHanh { get; set; }

    public string? Link { get; set; }
    public string? EmailLienKet { get; set; }
}
