﻿using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.ThanhPhanHoSoTTHCs.DTOs;
using MediatR;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using NHATTAMID2025.Application.DanhMuc.LinhVucs.DTOs;
using NHATTAMID2025.Application.LinhVucs.Queries.GetAllLinhVuc;

namespace NHATTAMID2025.Application.ThanhPhanHoSoTTHCs.Queries.GetAllThanhPhanHoSoTTHC;

public record GetAllThanhPhanHoSoTTHCQuery(
    int? PageNumber,
    int? PageSize,
    string? MaThuTucHanhChinh
) : IRequest<PaginatedList<ThanhPhanHoSoTTHCDto>>;

public class GetAllThanhPhanHoSoTTHCQueryValidator : AbstractValidator<GetAllThanhPhanHoSoTTHCQuery>
{
    public GetAllThanhPhanHoSoTTHCQueryValidator()
    {
        RuleFor(x => x.PageNumber).GreaterThanOrEqualTo(0);
        RuleFor(x => x.PageSize).GreaterThanOrEqualTo(0).LessThanOrEqualTo(1000);
    }
}

public class GetAllThanhPhanHoSoTTHCQueryHandler
    : IRequestHandler<GetAllThanhPhanHoSoTTHCQuery, PaginatedList<ThanhPhanHoSoTTHCDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllThanhPhanHoSoTTHCQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<PaginatedList<ThanhPhanHoSoTTHCDto>> Handle(GetAllThanhPhanHoSoTTHCQuery request, CancellationToken cancellationToken)
    {
        var query = _context.ThanhPhanHoSoTTHC.AsNoTracking();

        if (!string.IsNullOrWhiteSpace(request.MaThuTucHanhChinh))
        {
            var thuTucId = await _context.ThuTucHanhChinh
                .Where(t => t.MaThuTucHanhChinh == request.MaThuTucHanhChinh)
                .Select(t => t.ThuTucHanhChinhID)
                .FirstOrDefaultAsync(cancellationToken);

            if (thuTucId != Guid.Empty)
            {
                query = query.Where(x => x.ThuTucHanhChinhID == thuTucId);
            }
            else
            {
                return new PaginatedList<ThanhPhanHoSoTTHCDto>(
                    new List<ThanhPhanHoSoTTHCDto>(), 0, request.PageNumber ?? 1, request.PageSize ?? 0
                );
            }
        }

        var totalCount = await query.CountAsync(cancellationToken);

        bool noPaging = !request.PageNumber.HasValue || !request.PageSize.HasValue
                        || request.PageNumber <= 0 || request.PageSize <= 0;

        var baseQuery = query
            .OrderBy(x => x.TenThanhPhanHoSoTTHC)
            .Select(x => new ThanhPhanHoSoTTHCDto
            {
                ThanhPhanHoSoTTHCID = x.ThanhPhanHoSoTTHCID,
                ThuTucHanhChinhID = x.ThuTucHanhChinhID,
                TenThanhPhanHoSoTTHC = x.TenThanhPhanHoSoTTHC,
                TenTepDinhKem = x.TenTepDinhKem,
                DuongDanTepDinhKem = x.DuongDanTepDinhKem,
                SoBanChinh = x.SoBanChinh,
                SoBanSao = x.SoBanSao
            });

        if (noPaging)
        {
            var allItems = await baseQuery.ToListAsync(cancellationToken);
            return new PaginatedList<ThanhPhanHoSoTTHCDto>(allItems, totalCount, 1, totalCount);
        }

        var items = await baseQuery
            .Skip((request.PageNumber!.Value - 1) * request.PageSize!.Value)
            .Take(request.PageSize.Value)
            .ToListAsync(cancellationToken);

        return new PaginatedList<ThanhPhanHoSoTTHCDto>(items, totalCount, request.PageNumber.Value, request.PageSize.Value);
    }

}

public class PaginatedList<T>
{
    public List<T> Items { get; }
    public int TotalCount { get; }
    public int PageNumber { get; }
    public int PageSize { get; }
    public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);

    public PaginatedList(List<T> items, int totalCount, int pageNumber, int pageSize)
    {
        Items = items;
        TotalCount = totalCount;
        PageNumber = pageNumber;
        PageSize = pageSize;
    }
}
