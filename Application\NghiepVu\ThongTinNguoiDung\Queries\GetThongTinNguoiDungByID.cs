﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ThongTinNguoiDung.Queries;
internal class GetThongTinNguoiDungByID
{
}
public record GetThongTinNguoiDungByIDQuery(
    string UserID
    ) : IRequest<Result<List<ThongTinNguoiDungDto>>?>;


public class GetThongTinNguoiDungByIDQueryValidator : AbstractValidator<GetThongTinNguoiDungByIDQuery>
{
    public GetThongTinNguoiDungByIDQueryValidator()
    {
    }
}
public class GetThongTinNguoiDungByIDQueryHandler : IRequestHandler<GetThongTinNguoiDungByIDQuery, Result<List<ThongTinNguoiDungDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetThongTinNguoiDungByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<ThongTinNguoiDungDto>>?> Handle(GetThongTinNguoiDungByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ThongTinNguoiDung_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<ThongTinNguoiDungDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private ThongTinNguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ThongTinNguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
            TenDangNhap = reader["TenDangNhap"] as string,
            HoVaTen = reader["HoVaTen"] as string,
            GioiTinh = reader["GioiTinh"] as string,
            NgaySinh = reader["NgaySinh"] as string,
            SoDienThoai = reader["SoDienThoai"] as string,
            Email = reader["Email"] as string,
            DiaChi = reader["DiaChi"] as string,
            DonViCongTac = reader["DonViCongTac"] as string,
            Avatar = reader["Avatar"] as string,
            DonViCongTacID = reader.GetGuid(reader.GetOrdinal("DonViCongTacID")),
        };
    }
}
