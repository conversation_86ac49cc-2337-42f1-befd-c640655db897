﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;


public class NguoiDung : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapPost(DeleteNguoiDung, "/delete")
            .MapPost(UpdateNguoiDung, "/update")
            .MapPost(CreateNguoiDung, "/create")
            .MapGet(GetAllNguoiDung, "/getall")
            .MapGet(GetNguoiDungById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<NguoiDungDto>?> CreateNguoiDung(ISender sender, [FromBody] CreateNguoiDungCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<NguoiDungDto>?> UpdateNguoiDung(ISender sender, [FromBody] UpdateNguoiDungCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<NguoiDungDto>?> DeleteNguoiDung(ISender sender, [FromBody] DeleteNguoiDungCommand command)
    {
        return await sender.Send(command);
    }

    public async Task<Result<List<NguoiDungDto>>?> GetAllNguoiDung(ISender sender)
     => await sender.Send(new GetAllNguoiDungCommand());
    public async Task<Result<List<NguoiDungDto>>?> GetNguoiDungById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetNguoiDungByIDQuery(id));

}
