﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.Tinh.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class Tinh : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllTinh, "/getall")
            .MapGet(GetTinhById, "/getbyid/{Id}")
        ;
    }
    public async Task<Result<List<TinhDto>>?> GetAllTinh(ISender sender)
    => await sender.Send(new GetAllTinhCommand());
    public async Task<Result<List<TinhDto>>?> GetTinhById(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetTinhByIDQuery(id));
}
