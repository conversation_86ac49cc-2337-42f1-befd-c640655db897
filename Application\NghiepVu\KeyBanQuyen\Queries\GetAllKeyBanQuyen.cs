﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KeyBanQuyen.Queries;

public record GetAllKeyBanQuyenCommand : IRequest<Result<List<KeyBanQuyenDto>>?>;

public class GetAllKeyBanQuyenCommandValidator : AbstractValidator<GetAllKeyBanQuyenCommand>
{
    public GetAllKeyBanQuyenCommandValidator()
    {
    }
}

public class GetAllKeyBanQuyenCommandHandler : IRequestHandler<GetAllKeyBanQuyenCommand, Result<List<KeyBanQuyenDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetAllKeyBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<KeyBanQuyenDto>>?> Handle(GetAllKeyBanQuyenCommand request, CancellationToken cancellationToken)
    {
        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KeyBanQuyen_GetAll",
                   MapFromReader,
                   false,
                   Array.Empty<System.Data.Common.DbParameter>()
               );


            return Result<List<KeyBanQuyenDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private KeyBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KeyBanQuyenDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
            TrienKhaiPhanMemID = reader["TrienKhaiPhanMemID"] as string,
            NgayTao = reader["NgayTao"] as string,
            NgayHetHan = reader["NgayHetHan"] as string,
            SoLanKichHoat = reader["SoLanKichHoat"] != DBNull.Value ? Convert.ToInt32(reader["SoLanKichHoat"]) : 0,
            Khoa = reader["Khoa"] == DBNull.Value ? null : reader.GetBoolean(reader.GetOrdinal("Khoa")),
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
            TenUser = reader["TenUser"] as string,
            TenPhanMem = reader["TenPhanMem"] as string,
            TenGoi = reader["TenGoi"] as string,
            MaKey = reader["MaKey"] as string
        };
    }
}

