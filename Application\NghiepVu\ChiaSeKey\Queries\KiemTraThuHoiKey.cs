﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.NghiepVu.ChiaSeKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ChiaSeKey.Queries;

public record KiemTraThuHoiKeyQuery(
    string MaKey
    ) : IRequest<Result<List<ChiaSeKeyDto>>?>;


public class KiemTraThuHoiKeyQueryValidator : AbstractValidator<KiemTraThuHoiKeyQuery>
{
    public KiemTraThuHoiKeyQueryValidator()
    {
    }
}
public class KiemTraThuHoiKeyQueryHandler : IRequestHandler<KiemTraThuHoiKeyQuery, Result<List<ChiaSeKeyDto>>?>
{
    private readonly IApplicationDbContext _context;

    public KiemTraThuHoiKeyQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<ChiaSeKeyDto>>?> Handle(KiemTraThuHoiKeyQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@MaKey", DungChung.NormalizationGuid(request.MaKey))
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KiemTraThuHoiKey_GetByKeyID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<ChiaSeKeyDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private ChiaSeKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ChiaSeKeyDto
        {
            TrangThai = reader["TrangThai"] as string
        };
    }
}
