﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Domain.Entities;

namespace NHATTAMID2025.Application.DanhMuc.NhanViens.Commands.DeleteNhanVien;
public class DeleteNhanVienCommandHandler : IRequestHandler<DeleteNhanVienCommand,Result<object>>
{
    private readonly IApplicationDbContext _context;

    public DeleteNhanVienCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<object>> Handle(DeleteNhanVienCommand request, CancellationToken cancellationToken)
    {
        var entity = await _context.NhanVien.FindAsync(request.NhanVienID);
        if (entity == null) throw new NotFoundException(nameof(NhanVien), request.NhanVienID.ToString());

        _context.NhanVien.Remove(entity);
        await _context.SaveChangesAsync(cancellationToken);
        return Result<object>.Success();
    }

}
