﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using FluentValidation;
using NHATTAMID2025.Application.Authentication;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using WEB_DLL;
namespace NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
internal class CreateDangKyUsers
{
}
public class ApiException : Exception
{
    public int StatusCode { get; set; }
    public ApiException(string message, int statusCode = 400) : base(message)
    {
        StatusCode = statusCode;
    }
}
public record CreateDangKyUsersCommand(
    string TenDangNhap, 
    string MatMa,
    string NhapLaiMatMa,
    string HoVaTen,
    string GioiTinh,
    string SoDienThoai,
    string Email,
    string Dia<PERSON>hi,
    string DonViCongTac,
    string MaXacThuc
    ) : IRequest<Result<UsersDangKyDto>?>;
public class CreateCommandValidator : AbstractValidator<CreateDangKyUsersCommand>
{
    private readonly IApplicationDbContext _context;
    public CreateCommandValidator(IApplicationDbContext context)
    {
        _context = context;

        RuleFor(cmd => cmd.TenDangNhap)
            .NotEmpty().WithMessage("Tên đăng nhập không được để trống.")
            .MustAsync(BeUniqueTenDangNhap)
            .WithMessage(cmd => $"Tên đăng nhập '{cmd.TenDangNhap}' đã được sử dụng.");
        RuleFor(cmd => cmd.Email)
            .NotEmpty().WithMessage("Email không được để trống.")
            .MustAsync(BeUniquEmail)
            .WithMessage(cmd => $"Email '{cmd.Email}' đã được sử dụng.");
        RuleFor(cmd => cmd.SoDienThoai)
           .NotEmpty().WithMessage("Số điện thoại không được để trống.")
           .MustAsync(BeUniquSoDienThoai)
           .WithMessage(cmd => $"Số điện thoại '{cmd.SoDienThoai}' đã được sử dụng.");
        RuleFor(cmd => cmd.MatMa)
           .NotEmpty().WithMessage("Mật khẩu không được để trống.")
           .MinimumLength(8).WithMessage("Mật khẩu phải có ít nhất 8 ký tự.")
           .Matches("[A-Z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái in hoa.")
           .Matches("[a-z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái thường.")
           .Matches("[0-9]").WithMessage("Mật khẩu phải có ít nhất một chữ số.")
           .Matches("[^a-zA-Z0-9]").WithMessage("Mật khẩu phải có ít nhất một ký tự đặc biệt.");
        RuleFor(cmd => cmd.NhapLaiMatMa)
           .NotEmpty().WithMessage("Vui lòng nhập lại mật khẩu.")
           .Equal(cmd => cmd.MatMa).WithMessage("Mật khẩu nhập lại không trùng khớp.");
    }

    private async Task<bool> BeUniqueTenDangNhap(string tenDangNhap, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(tenDangNhap))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.TenDangNhap == tenDangNhap, cancellationToken);
    }

    private async Task<bool> BeUniquEmail(string Email, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(Email))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.Email == Email, cancellationToken);
    }

    private async Task<bool> BeUniquSoDienThoai(string SoDienThoai, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(SoDienThoai))
            return true; // Có thể tùy chọn kiểm tra NotEmpty ở rule phía trên

        return !await _context.Users
            .AnyAsync(u => u.SoDienThoai == SoDienThoai, cancellationToken);
    }
}
public class CreateDangKyUsersCommandHandler : IRequestHandler<CreateDangKyUsersCommand, Result<UsersDangKyDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateDangKyUsersCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<UsersDangKyDto>?> Handle(CreateDangKyUsersCommand request, CancellationToken cancellationToken)
    {
        var ma = await _context.MaXacThuc
                        .Where(x => x.Email == request.Email && x.MaXacThuc == request.MaXacThuc)
                        .OrderByDescending(x => x.ThoiGianTao)
                        .FirstOrDefaultAsync();

        if (ma == null)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã hết hạn."]);
        ma.TrangThai = "Used";
        await _context.SaveChangesAsync(cancellationToken);
        var id = Guid.NewGuid();
        string keyMaHoaMatKhau = "rateAnd2012";
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TenDangNhap", request.TenDangNhap),
            new Microsoft.Data.SqlClient.SqlParameter("@MatMa", ntsSecurity._mEncrypt( request.MatMa.Trim(), keyMaHoaMatKhau, true )),
            new Microsoft.Data.SqlClient.SqlParameter("@NhapLaiMatMa", ntsSecurity._mEncrypt( request.NhapLaiMatMa.Trim(), keyMaHoaMatKhau, true)),
            new Microsoft.Data.SqlClient.SqlParameter("@HoVaTen", request.HoVaTen),
            new Microsoft.Data.SqlClient.SqlParameter("@GioiTinh", request.GioiTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@SoDienThoai", request.SoDienThoai),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@DiaChi", request.DiaChi),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViCongTac", request.DonViCongTac)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_DangKyUser_Create",
                   MapFromReader,
                   true,
                   parameters
               );

            return Result<UsersDangKyDto>.Success(dto.FirstOrDefault());

        }
        catch (Exception)
        {
            return Result<UsersDangKyDto>.Failure(["Đăng ký thất bại."]);
        }
    }


    private UsersDangKyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new UsersDangKyDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID"))
        };
    }
}
