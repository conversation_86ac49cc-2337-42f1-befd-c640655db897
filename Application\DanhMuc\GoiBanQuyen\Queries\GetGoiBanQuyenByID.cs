﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Queries;
public record GetGoiBanQuyenByIDQuery(
    string GoiBanQuyenID
    ) : IRequest<Result<List<GoiBanQuyenDto>>?>;


public class GetGoiBanQuyenByIDQueryValidator : AbstractValidator<GetGoiBanQuyenByIDQuery>
{
    public GetGoiBanQuyenByIDQueryValidator()
    {
    }
}
public class GetGoiBanQuyenByIDQueryHandler : IRequestHandler<GetGoiBanQuyenByIDQuery, Result<List<GoiBanQuyenDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetGoiBanQuyenByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<GoiBanQuyenDto>>?> Handle(GetGoiBanQuyenByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@GoiBanQuyenID", DungChung.NormalizationGuid(request.GoiBanQuyenID))
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_GoiBanQuyen_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<GoiBanQuyenDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private GoiBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new GoiBanQuyenDto
        {
            GoiBanQuyenID = reader.GetGuid(reader.GetOrdinal("GoiBanQuyenID")),
            MaGoi = reader["MaGoi"] as string,
            TenGoi = reader["TenGoi"] as string,
            MoTa = reader["MoTa"] as string,
            ThoiHanNgay = reader["ThoiHanNgay"] != DBNull.Value ? Convert.ToInt32(reader["ThoiHanNgay"]) : 0,
            SoThietBiToiDa = reader["SoThietBiToiDa"] != DBNull.Value ? Convert.ToInt32(reader["SoThietBiToiDa"]) : 0,
            LoaiNguoiDungID = reader.GetGuid(reader.GetOrdinal("LoaiNguoiDungID")),
            GiaTien = reader["GiaTien"] != DBNull.Value ? Convert.ToDecimal(reader["GiaTien"]) : 0

        };
    }
}
