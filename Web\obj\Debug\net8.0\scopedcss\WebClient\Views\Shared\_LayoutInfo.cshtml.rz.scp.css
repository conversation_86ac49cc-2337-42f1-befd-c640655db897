/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-w6x9hphj0w] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-w6x9hphj0w] {
  color: #0077cc;
}

.btn-primary[b-w6x9hphj0w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-w6x9hphj0w], .nav-pills .show > .nav-link[b-w6x9hphj0w] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-w6x9hphj0w] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-w6x9hphj0w] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-w6x9hphj0w] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-w6x9hphj0w] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-w6x9hphj0w] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
