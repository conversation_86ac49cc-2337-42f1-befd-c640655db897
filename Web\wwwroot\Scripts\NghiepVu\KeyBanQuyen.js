﻿var ThaoTac;

$(document).ready(async function () {

  await loadMultiSelect({
    url: window.location.origin + '/api/trienkhaiphanmem/getall',
    selectId: 'TrienKhaiPhanMemID',
    fields: {
      valueField: 'trienKhaiPhanMemID',
      labelField1: 'tenPhanMem',
      labelField2: 'tenTinh'
    },
    multi: true
  });

  var danhsachnguoidung = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/nguoidung/getall', null)
  if (danhsachnguoidung.succeeded) {
    tablerSelect("NguoiDungID", danhsachnguoidung.result, "tenDangNhap", "userID", "");
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + Tinh.errors)
  }


  var danhsachgoibanquyen = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/goibanquyen/getall', null)
  if (danhsachgoibanquyen.succeeded) {
    tablerSelect("GoiBanQuyenID", danhsachgoibanquyen.result, "tenGoi", "goiBanQuyenID", "");
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + Tinh.errors)
  }
  LoadLuoi()

});


$('#capKeyForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    KeyID: document.getElementById("KeyID").value,
    MaKey: document.getElementById("MaKey").value,
    TrienKhaiPhanMemID: JSON.stringify(document.getElementById("TrienKhaiPhanMemID").tomselect.getValue()),
    GoiBanQuyenID: document.getElementById("GoiBanQuyenID").value,
    NgayTao: document.getElementById("NgayTao").value,
    NgayHetHan: document.getElementById("NgayHetHan").value,
    SoLanKichHoat: document.getElementById("SoLanKichHoat").value,
    UserID: document.getElementById("NguoiDungID").value,
    Khoa: ($('#TrangThai').value() == true ? false : true),
  };

  if (ThaoTac == 'Them') {
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/keybanquyen/create', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Thêm mới dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-capKey').modal('hide');
        setTimeout(() => {
          $('body').removeClass('modal-open');
          $('.modal-backdrop').remove();
        }, 100); // đợi hiệu ứng fade
      }
    } catch (error) {
      NTS.canhbao("Thêm mới dữ liệu thất bại!");
    }
  }
  if (ThaoTac == 'Sua') {
    try {
      const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/keybanquyen/update', data);
      if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
        NTS.canhbao(resDangKy.errors[0])
      } else {
        if (resDangKy.succeeded == true) {
          NTS.thanhcong("Cập nhật dữ liệu thành công!");
          LoadLuoi()
        }
        $('#modal-capKey').modal('hide');
        setTimeout(() => {
          $('body').removeClass('modal-open');
          $('.modal-backdrop').remove();
        }, 100); // đợi hiệu ứng fade
      }
    } catch (error) {
      NTS.canhbao("Cập nhật dữ liệu thất bại!");
    }
  }
});


async function LoadLuoi() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + '/api/keybanquyen/getall', null);
  if (response.succeeded) {
    var originalData = response.result;
    $('#keyBanQuyenTable').DataTable().destroy();
    $('#keyBanQuyenTable').DataTable({
      data: originalData,
      scrollX: false,
      scrollCollapse: false,
      autoWidth: false,
      responsive: false,
      fixedColumns: {
        rightColumns: 1, leftColumns: 0
      },
      columns: [

        {
          data: 'khoa',
          width: '150px',
          className: 'align-middle text-start',
          render: function (data, type, row) {
            if (data === null || data === undefined) {
              return '<span class="text-muted fst-italic">No package</span>';
            }

            if (data ===  true) {
              return `
        <div class="d-flex align-items-center gap-2">
          <i class="fas fa-lock text-danger"></i>
          <span class="text-danger font-weight-medium" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
          ">
            Key khóa
          </span>
        </div>
      `;
            }

            if (data === false) {
              return `
        <div class="d-flex align-items-center gap-2">
          <i class="fas fa-unlock text-success"></i>
          <span class="text-success font-weight-medium" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
          ">
            Key mở
          </span>
        </div>
      `;
            }

            return `<span class="text-muted fst-italic">Không xác định</span>`;
          }
        },

        {
          data: 'maKey',
          width: '50px',
          className: 'align-middle text-center',
          render: function (data, type, row) {
            if (!data) return '<span class="text-muted fst-italic">No key</span>';
            const inputId = `copyKey_${row.keyID}`;
            return `
      <div class="d-inline-flex justify-content-center align-items-center gap-2">
        <span id="${inputId}" style="
        display: none;
          background-color: #eef2ff;
          color: #1e40af;
          padding: 4px 8px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          font-size: 0.825rem;
          max-width: 100px;
          overflow-x: auto;
          white-space: nowrap;
        ">${data}</span>
         <button class="btn btn-sm btn-light border" title="Sao chép mã key" onclick="copyToClipboardText('${inputId}')">
          <i class="fas fa-copy text-primary"></i>
        </button>
      </div>
    `;
          }
        }

,
        { data: 'tenUser', width: "150px", className: 'align-middle text-start', },
        {
          data: 'tenPhanMem',
          className: 'align-middle text-start',
          width: '300px',
          render: function (data, type, row) {
            if (!data || typeof data !== 'string') return '<span class="text-muted">No software</span>';

            // Split the data string and create badges for each software item
            const softwareItems = data.split(';').filter(item => item.trim() !== '');

            if (softwareItems.length === 0) return '<span class="text-muted">No software</span>';

            return softwareItems
              .map(item => `
        <div class="d-flex align-items-center gap-2 mb-2">
          <i class="fa-solid fa-laptop-code text-green" ></i>
          <span class="badge bg-green-lt text-green font-weight-medium" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            line-height: 1.2;
            white-space: nowrap;
          ">
            ${item.trim()}
          </span>
        </div>
      `)
              .join('');
          }
        },
        {
          data: 'tenGoi',
          width: '150px',
          className: 'align-middle text-start',
          render: function (data, type, row) {
            if (!data) return '<span class="text-muted fst-italic">No package</span>';
            return `
        <div class="d-flex align-items-center gap-2">
          <i class="fas fa-fire text-danger " ></i>
          <span class="text-blue font-weight-medium" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
          ">
            ${data}
          </span>
        </div>
      `;
          }
        },
        {
          data: 'ngayTao',
          width: '150px',
          className: 'align-middle text-start',
          render: function (data, type, row) {
            if (!data) return '<span class="text-muted fst-italic">No date</span>';
            return `
        <div class="d-flex align-items-center gap-2">
          <i class="fas fa-calendar-plus text-green" ></i>
          <span class="text-muted" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
          ">
            ${data}
          </span>
        </div>
      `;
          }
        },
        {
          data: 'ngayHetHan',
          width: '150px',
          className: 'align-middle text-start',
          render: function (data, type, row) {
            if (!data) return '<span class="text-muted fst-italic">No expiry</span>';
            return `
        <div class="d-flex align-items-center gap-2">
          <i class="fas fa-calendar-times text-danger" ></i>
          <span class="text-muted" style="
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
          ">
            ${data}
          </span>
        </div>
      `;
          }
        },
        {
          data: 'soLanKichHoat',
          width: "50px",
          className: 'align-middle text-start',
          render: function (data, type, row) {
            const icon = `<i class="fas fa-laptop-code text-primary me-1"></i>`;
            const soLuong = isNaN(data) || data == 0
              ? `<span style="font-weight: 500; color: #0d6efd;">0</span>`
              : `<span style="font-weight: 500; color: #0d6efd;">${data}</span>`;
            return `${icon} ${soLuong} <span style="color: #6c757d; ">thiết bị</span>`;
          }
        },

        {
          data: null,
          className: "text-center",
          orderable: false,
          width: "90px",
          className: 'align-middle text-start',
          render: function (data, type, row) {
            return `
                                    <div class="action-buttons">
                                        <button class="btn btn-sm" onclick="editRow('${row.keyID}')">
                                            <i class="fa fa-pencil text-primary"></i>
                                        </button>
                                        <button class="btn btn-sm" onclick="deleteRow('${row.keyID}')">
                                            <i class="fa fa-trash text-danger"></i>
                                        </button>
                                    </div>`;
          }
        }
      ],
      pageLength: 15,
      lengthMenu: [15, 50, 100, 200],
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/vi.json',
        search: ""
      }
    });
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}
function targetonclick() {
  ThaoTac = 'Them'
  resetForm("capKeyForm")
  $('#SoLanKichHoat').value(0)
  $('#TrangThai').value(1)

  
}


async function deleteRow(id) {
  // Icon SVG Tabler cảnh báo (triangle-exclamation)
  const tablerWarningSVG = `
    <svg class="tabler-icon-warning" xmlns="http://www.w3.org/2000/svg" 
         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5"
         stroke-linejoin="round" stroke-linecap="round" style="width:72px; height:72px; margin: 0 auto 15px auto; stroke:#f59e0b;">
      <path d="M12 9v4"></path>
      <path d="M12 17h.01"></path>
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    </svg>
  `;

  const confirmed = await Swal.fire({
    title: 'Bạn có chắc muốn xóa?',
    text: "Hành động này sẽ không thể hoàn tác!",
    icon: 'warning', // vẫn phải có icon để vùng icon xuất hiện
    showCancelButton: true,
    confirmButtonText: 'Đồng ý',
    cancelButtonText: 'Hủy',
    reverseButtons: true,
    didOpen: () => {
      // Thay thế icon mặc định bằng SVG Tabler
      const iconElem = document.querySelector('.swal2-icon.swal2-warning');
      if (iconElem) {
        iconElem.innerHTML = tablerWarningSVG;
        iconElem.style.background = 'none'; // bỏ nền mặc định
        iconElem.style.border = 'none'; // bỏ viền mặc định
      }
    }
  });

  if (confirmed.isConfirmed) {
    const data = {
      KeyID: id
    };

    try {
      const response = await NTS.getAjaxAPIAsync(
        'POST',
        window.location.origin + '/api/keybanquyen/delete',
        data
      );
      if (response.succeeded == false && response.errors.length > 0) {
        NTS.canhbao(response.errors[0]);
      } else {
        if (response.succeeded == true) {
          await Swal.fire('Đã xóa!', 'Bản ghi đã được xóa thành công.', 'success');
          LoadLuoi();
        }
      }
    } catch (error) {
      Swal.fire('Lỗi!', 'Không thể xóa bản ghi.', 'error');
    }
  }
}

async function editRow(id) {
  ThaoTac = 'Sua'
  $('#modal-capKey').modal("show")
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/keybanquyen/getbyid/${id}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    document.getElementById("MaKey").value = originalData[0].maKey
    document.getElementById("TrienKhaiPhanMemID").tomselect.setValue(JSON.parse(originalData[0].trienKhaiPhanMemID));

      document.getElementById("GoiBanQuyenID").tomselect.setValue(originalData[0].goiBanQuyenID);
    document.getElementById("NguoiDungID").tomselect.setValue(originalData[0].userID);
    
    $('#TrangThai').value((originalData[0].khoa==true? false: true));
    try {
      const originalDate = originalData[0].ngayTao;
      // Tách ngày, tháng, năm từ chuỗi gốc (dd/MM/yyyy)
      const [day, month, year] = originalDate.split("/");
      // Chuyển về định dạng yyyy-MM-dd
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      $('#NgayTao').val(formattedDate);
    } catch {
      $('#NgayTao').val('');
    }
    try {
      const originalDate = originalData[0].ngayHetHan;
      // Tách ngày, tháng, năm từ chuỗi gốc (dd/MM/yyyy)
      const [day, month, year] = originalDate.split("/");
      // Chuyển về định dạng yyyy-MM-dd
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      $('#NgayHetHan').val(formattedDate);
    } catch {
      $('#NgayHetHan').val('');
    }
    document.getElementById("SoLanKichHoat").value = originalData[0].soLanKichHoat
    $('#KeyID').value(originalData[0].keyID)
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }

}
$('#NguoiDungID').on('change',async function () {
  var selectedValue = $(this).val();
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/NguoiDung/getbyid/${selectedValue}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    try {
      $('#HoTenNguoiDung').text(originalData[0].hoVaTen || "Chưa có")
      $('#EmailNguoiDung').text(originalData[0].email || "Chưa có")
      $('#SoDienThoaiNguoiDung').text(originalData[0].soDienThoai || "Chưa có")
      $('#DiaChiNguoiDung').text(originalData[0].diaChi || "Chưa có")
      $('#TrangThaiNguoiDung').text(originalData[0].dangSD == '1' ? "Đang hoạt động" : "Tài khoản bị khóa")
      document.getElementById("loaiNguoiDung").textContent = originalData[0].loaiUsers || "Chưa có";
      document.getElementById("tenDangNhap").textContent = originalData[0].tenDangNhap || "Chưa có";
      document.getElementById("email").textContent = originalData[0].email || "Chưa có";
      document.getElementById("soDienThoai").textContent = originalData[0].soDienThoai || "Chưa có";
      document.getElementById("donViCongTac").textContent = originalData[0].donViCongTac || "Chưa có";
      document.getElementById("matKhau").textContent = "********"; // Luôn hiển thị ẩn mật khẩu
    } catch { }
 
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
});
$('#GoiBanQuyenID').on('change', async function () {
  var selectedValue = $(this).val();
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/goibanquyen/getbyid/${selectedValue}`, {});
  if (response.succeeded) {
    var originalData = response.result;

    $('#GioiHanKichHoat').value(originalData[0].soThietBiToiDa)
    khoiTaoNgayHetHan("NgayTao", "NgayHetHan", originalData[0].thoiHanNgay);
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
});
function khoiTaoNgayHetHan(idNgayTao, idNgayHetHan, songay) {
  const inputNgayTao = document.getElementById(idNgayTao);
  const inputNgayHetHan = document.getElementById(idNgayHetHan);

  if (!inputNgayTao || !inputNgayHetHan) return;

  // Lấy ngày hiện tại
  const homNay = new Date();
  const yyyy = homNay.getFullYear();
  const mm = String(homNay.getMonth() + 1).padStart(2, '0');
  const dd = String(homNay.getDate()).padStart(2, '0');
  const ngayHienTaiStr = `${yyyy}-${mm}-${dd}`;

  // Set ngày tạo là hôm nay
  inputNgayTao.value = ngayHienTaiStr;

  // Nếu không thời hạn
  if (songay === 0) {
    inputNgayHetHan.value = '';
    inputNgayHetHan.placeholder = 'Không thời hạn';
    return;
  }

  // Tính ngày hết hạn
  const ngayHetHan = new Date(homNay);
  ngayHetHan.setDate(ngayHetHan.getDate() + songay);
  const yyyy2 = ngayHetHan.getFullYear();
  const mm2 = String(ngayHetHan.getMonth() + 1).padStart(2, '0');
  const dd2 = String(ngayHetHan.getDate()).padStart(2, '0');
  const ngayHetHanStr = `${yyyy2}-${mm2}-${dd2}`;

  // Set ngày hết hạn
  inputNgayHetHan.value = ngayHetHanStr;
}

document.getElementById('TrangThai').addEventListener('change', function () {
  const statusLabel = document.getElementById('modal-statusLabel');
  if (this.checked) {
    statusLabel.textContent = 'Mở key';
    statusLabel.style.color = '#0054a6'; // Xanh dương khi kích hoạt
  } else {
    statusLabel.textContent = 'Khóa key';
    statusLabel.style.color = '#d63939'; // Đỏ khi khóa
  }
});

function copyToClipboardText(elementId) {
  debugger
  const el = document.getElementById(elementId);
  if (el) {
    const text = el.innerText || el.textContent;
    navigator.clipboard.writeText(text).then(() => {
      // Bạn có thể thay alert bằng Toast đẹp hơn nếu muốn
      console.log("Đã sao chép: " + text);
    });
  }
}

