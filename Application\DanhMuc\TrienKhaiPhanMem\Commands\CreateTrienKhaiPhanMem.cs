﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.DTOs;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using NHATTAMID2025.Domain.Entities;

namespace NHATTAMID2025.Application.DanhMuc.TrienKhaiPhanMem.Commands;
public record CreateTrienKhaiPhanMemCommand(
    string PhanMemID,
    string TinhID,
    string UrlTruyCap,
    string TrangThai,
    string MoTa,
    string NgayTrienKhai
    ) : IRequest<Result<TrienKhaiPhanMemDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateTrienKhaiPhanMemCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateTrienKhaiPhanMemCommandHandler : IRequestHandler<CreateTrienKhaiPhanMemCommand, Result<TrienKhaiPhanMemDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateTrienKhaiPhanMemCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<TrienKhaiPhanMemDto>?> Handle(CreateTrienKhaiPhanMemCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@PhanMemID", DungChung.NormalizationGuid(request.PhanMemID)),
            new Microsoft.Data.SqlClient.SqlParameter("@TinhID", DungChung.NormalizationGuid(request.TinhID)),
            new Microsoft.Data.SqlClient.SqlParameter("@UrlTruyCap", request.UrlTruyCap),
            new Microsoft.Data.SqlClient.SqlParameter("@TrangThai", request.@TrangThai),
            new Microsoft.Data.SqlClient.SqlParameter("@MoTa", request.MoTa),
            new Microsoft.Data.SqlClient.SqlParameter("@NgayTrienKhai", request.NgayTrienKhai)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_TrienKhaiPhanMem_Create",
                   MapFromReader,
                   true,
                   parameters
               );

      
            return Result<TrienKhaiPhanMemDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private TrienKhaiPhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new TrienKhaiPhanMemDto
        {
            TrienKhaiPhanMemID = reader.GetGuid(reader.GetOrdinal("TrienKhaiPhanMemID")),
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TinhID = reader.GetGuid(reader.GetOrdinal("TinhID")),
            UrlTruyCap = reader["UrlTruyCap"] as string,
            MoTa= reader["MoTa"] as string
        };
    }
}
