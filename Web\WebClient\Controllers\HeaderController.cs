﻿//using Microsoft.AspNetCore.Mvc;
//using System.Net.Http.Json;

//namespace QLBH2025.Web.WebClient.Controllers;
//public class HeaderViewComponent : ViewComponent
//{
//    private readonly HttpClient _httpClientDungChung;
//    private readonly HttpClient _httpClient;

//    public HeaderViewComponent(IHttpClientFactory httpClientFactory)
//    {
//        _httpClient = httpClientFactory.CreateClient("ApiClient");
//        _httpClientDungChung = httpClientFactory.CreateClient("ApiClientDungChung");
//    }
//    public IViewComponentResult Invoke()
//    {
//        ViewBag.clientDungChung = _httpClientDungChung;
//        ViewBag.client = _httpClient;
//        ViewBag.AbsolutePath = HttpContext.Request.Path;

//        return View();
//    }
//}
