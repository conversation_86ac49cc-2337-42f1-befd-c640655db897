﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.GoiBanQuyen.Commands;
public record DeleteGoiBanQuyenCommand(
    string GoiBanQuyenID
    ) : IRequest<Result<GoiBanQuyenDto>?>;

public class DeleteCommandValidator : AbstractValidator<DeleteGoiBanQuyenCommand>
{
    public DeleteCommandValidator()
    {
    }
}

public class DeleteGoiBanQuyenCommandHandler : IRequestHandler<DeleteGoiBanQuyenCommand, Result<GoiBanQuyenDto>?>
{
    private readonly IApplicationDbContext _context;

    public DeleteGoiBanQuyenCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<GoiBanQuyenDto>?> Handle(DeleteGoiBanQuyenCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();

        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@GoiBanQuyenID", request.GoiBanQuyenID)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_GoiBanQuyen_Delete",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<GoiBanQuyenDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private GoiBanQuyenDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new GoiBanQuyenDto
        {
            GoiBanQuyenID = reader.GetGuid(reader.GetOrdinal("GoiBanQuyenID")),
        };
    }
}
