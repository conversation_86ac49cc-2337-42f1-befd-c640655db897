﻿$(document).ready(async function () {
  LoadDuLieuKichHoatKey()
  LoadDuLieuGioiThieuSP()
});
async function LoadDuLieuKichHoatKey() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/KichHoatKey/getbyemail/${localStorage.getItem("userID")}`, {});
  if (response.succeeded) {
    var originalData = response.result;
    if (originalData.length > 0) {
      for (var i = 0; i < originalData.length; i++) {
        var item = originalData[i];
        var html = `  <div class="col-md-6 col-lg-4 ">
            <div class="card p-3">
                <div class="d-flex align-items-center mb-3">
                        <img src="/Images/9391705.png"
                         alt="Logo" class="rounded me-3" width="48" height="48">
                    <div>
                            <h4 class="m-0">${item.tenPhanMem}</h4>
                            <small class="text-muted">${item.moTa}</small>
                    </div>
                </div>
                <div class="d-flex justify-content-between">
                        <a href="https://nhattamsoft.vn/san-pham.html#" target="_blank" class="btn btn-outline-primary w-100 me-2">Chi tiết sản phẩm</a>
                        <a href="${item.link}" target="_blank"  class="btn btn-primary w-auto">Truy cập</a>
                </div>
            </div>
        </div> `;

        $('#DSSanPhamDaKichHoat').append(html);
      }
    } else {
      $('#DSSanPhamDaKichHoat').append(` <div  class="no-data-message " >
            <img src="/Images/nodata.png" alt="Không có dữ liệu" class="nodata-img">
            <p>Không có dữ liệu.</p>
        </div>`);
    }
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

async function LoadDuLieuGioiThieuSP() {
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/PhanMem/gioithieuphanmem/${localStorage.getItem("userID") }`, null);
  if (response.succeeded) {
    var originalData = response.result;
    for (var i = 0; i < originalData.length; i++) {
      var item = originalData[i];
      var html = `  <div class="col-md-6 col-lg-4 ">
            <div class="card p-3">
                <div class="d-flex align-items-center mb-3">
                        <img src="/Images/Copilot_20250613_094016.png"
                         alt="Logo" class="rounded me-3" width="48" height="48">
                    <div>
                            <h4 class="m-0">${item.tenPhanMem}</h4>
                            <small class="text-muted">${item.moTa}</small>
                    </div>
                </div>
                <div class="d-flex justify-content-between">
                        <a href="https://nhattamsoft.vn/san-pham.html#" target="_blank" class="btn btn-outline-primary w-100 me-2">Chi tiết sản phẩm</a>
                        <a id="bnt${item.phanMemID}" onclick="DangKyPhanMem('${item.phanMemID}','${item.tenPhanMem}','${item.moTa}')"  class="btn btn-primary w-auto ${(item.trangThai == "Đã đăng ký" ? "disabled" : "")}" ${(item.trangThai == "Đã đăng ký" ? `aria-disabled="true""` : "")}>Đăng ký</a>
                </div>
            </div>
        </div> `;

      $('#DSGioiThieuSP').append(html);
    }
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}

async function DangKyPhanMem(phanMemID, tenPhanMem, moTa) {
  $('#DangKyModal').modal("show")
  $('#mdTenPhanMem').text(tenPhanMem)
  $('#mdMoTa').text(moTa)
  $('#hfPhanMemID').value(phanMemID)
  var response = await NTS.getAjaxAPIAsync('Get', window.location.origin + `/api/NguoiDung/getbyid/${localStorage.getItem("userID") }`, {});
  if (response.succeeded) {
    var originalData = response.result;

    $('#mdHoTen').text(originalData[0].hoVaTen)
    $('#mdSDT').text(originalData[0].soDienThoai)
    $('#mdEmail').text(originalData[0].email)
    $('#donViCongTac').text(originalData[0].donViCongTac)
  } else {
    NTS.canhbao('Lỗi khi lấy dữ liệu:' + response.errors)
  }
}


$('#DangKyModalForm').on('submit', async function (e) {
  e.preventDefault();
  const data = {
    dangKyGiayPhepID: "",
    phanMemID: $('#hfPhanMemID').value(),
    userID: localStorage.getItem("userID"),
    noiDung: $('#noidung').value(),
    loaiDangKy: "1",
    KeyID: "",
  };
  try {
    const resDangKy = await NTS.getAjaxAPIAsync('POST', window.location.origin + '/api/DangKyGiayPhep/create', data);
    if (resDangKy.succeeded == false && resDangKy.errors.length > 0) {
      NTS.canhbao(resDangKy.errors[0])
    } else {
      if (resDangKy.succeeded == true) {
        NTS.thanhcong("Đăng ký giấy phép sử dụng phần mềm thành công!");
      }
      $('#DangKyModal').modal('hide');

      $('#bnt' + $('#hfPhanMemID').value())
        .addClass('disabled')
        .attr('aria-disabled', 'true')
        .css('pointer-events', 'none');
      setTimeout(() => {
        $('body').removeClass('modal-open');
        $('.modal-backdrop').remove();
      }, 100); // đợi hiệu ứng fade
    }
  } catch (error) {
    NTS.canhbao("Đăng ký giấy phép sử dụng phần mềm thất bại!");
  }
});

