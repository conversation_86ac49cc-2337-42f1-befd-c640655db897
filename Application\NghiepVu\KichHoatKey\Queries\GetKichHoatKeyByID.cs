﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.KichHoatKey.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.KichHoatKey.Queries;
internal class GetKichHoatKeyByID
{
}
public record GetKichHoatKeyByIDQuery(
    string KeyID,
    string TenPhanMem
    ) : IRequest<Result<List<KichHoatKeyDto>>?>;


public class GetKichHoatKeyByIDQueryValidator : AbstractValidator<GetKichHoatKeyByIDQuery>
{
    public GetKichHoatKeyByIDQueryValidator()
    {
    }
}
public class GetKichHoatKeyByIDQueryHandler : IRequestHandler<GetKichHoatKeyByIDQuery, Result<List<KichHoatKeyDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetKichHoatKeyByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<KichHoatKeyDto>>?> Handle(GetKichHoatKeyByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@KeyID", request.KeyID),
                 new Microsoft.Data.SqlClient.SqlParameter("@TenPhanMem", request.TenPhanMem)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_KichHoatKey_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<KichHoatKeyDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private KichHoatKeyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new KichHoatKeyDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
            NguoiKichHoat = reader["NguoiKichHoat"] as string,
            EmailKichHoat = reader["EmailKichHoat"] as string,
            DonViKichHoat = reader["DonViKichHoat"] as string,
            HeDieuHanh = reader["HeDieuHanh"] as string,
            TenThietBi = reader["TenThietBi"] as string,
            NgayKichHoat = reader.IsDBNull(reader.GetOrdinal("NgayKichHoat"))
                     ? null
                     : reader.GetDateTime(reader.GetOrdinal("NgayKichHoat")),
            IPKichHoat = reader["IPKichHoat"] as string,
            Link = reader["Link"] as string
        };
    }
}
