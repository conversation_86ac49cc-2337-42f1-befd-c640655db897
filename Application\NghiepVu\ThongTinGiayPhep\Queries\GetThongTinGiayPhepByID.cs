﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.NghiepVu.ThongTinGiayPhep.DTOs;

namespace NHATTAMID2025.Application.NghiepVu.ThongTinGiayPhep.Queries;
internal class GetThongTinGiayPhepByID
{
}
public record GetThongTinGiayPhepByIDQuery(
    string UserID
    ) : IRequest<Result<List<ThongTinGiayPhepDto>>?>;


public class GetThongTinGiayPhepByIDQueryValidator : AbstractValidator<GetThongTinGiayPhepByIDQuery>
{
    public GetThongTinGiayPhepByIDQueryValidator()
    {
    }
}
public class GetThongTinGiayPhepByIDQueryHandler : IRequestHandler<GetThongTinGiayPhepByIDQuery, Result<List<ThongTinGiayPhepDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetThongTinGiayPhepByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<ThongTinGiayPhepDto>>?> Handle(GetThongTinGiayPhepByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@UserID", request.UserID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_ThongTinGiayPhep_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<ThongTinGiayPhepDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private ThongTinGiayPhepDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new ThongTinGiayPhepDto
        {
            KeyID = reader.GetGuid(reader.GetOrdinal("KeyID")),
            TenGoiBanQuyen = reader["TenGoiBanQuyen"] as string,
            TenPhanMem = reader["TenPhanMem"] as string,
            NgayTao = reader["NgayTao"] as string,
            NgayHetHan = reader["NgayHetHan"] as string,
            SoLanKichHoat = reader.GetInt32(reader.GetOrdinal("SoLanKichHoat")),
            MaKey = reader["MaKey"] as string,
            SoNgayConLai = reader["SoNgayConLai"] as string,
            TrangThai = reader["TrangThai"] as string,
            SoThietBiToiDa = reader.GetInt32(reader.GetOrdinal("SoThietBiToiDa")),
            GiaHan = reader["GiaHan"] as string,
            chiaSeBoi = reader["chiaSeBoi"] as string,
            Loai = reader["Loai"] as string
        };
    }
}
