﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.PhanMem.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.PhanMem.Queries;

public record GetPhanMemByIDQuery(
    string PhanMemID
    ) : IRequest<Result<List<PhanMemDto>>?>;


public class GetPhanMemByIDQueryValidator : AbstractValidator<GetPhanMemByIDQuery>
{
    public GetPhanMemByIDQueryValidator()
    {
    }
}
public class GetPhanMemByIDQueryHandler : IRequestHandler<GetPhanMemByIDQuery, Result<List<PhanMemDto>>?>
{
    private readonly IApplicationDbContext _context;

    public GetPhanMemByIDQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<List<PhanMemDto>>?> Handle(GetPhanMemByIDQuery request, CancellationToken cancellationToken)
    {

        try
        {
            var parameters = new[]
            {
                new Microsoft.Data.SqlClient.SqlParameter("@PhanMemID", request.PhanMemID)
            };
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_PhanMem_GetByID",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<List<PhanMemDto>>.Success(dto.ToList());
        }
        catch (Exception)
        {
            return null;
        }
    }
    private PhanMemDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new PhanMemDto
        {
            PhanMemID = reader.GetGuid(reader.GetOrdinal("PhanMemID")),
            TenPhanMem = reader["TenPhanMem"] as string,
            MaPhanMem = reader["MaPhanMem"] as string,
            MoTa = reader["MoTa"] as string,
            DangSD = reader.IsDBNull(reader.GetOrdinal("DangSD")) ? null : reader.GetBoolean(reader.GetOrdinal("DangSD"))
        };
    }
}
