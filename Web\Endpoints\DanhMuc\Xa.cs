﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.DanhMuc.Tinh.Commands;
using NHATTAMID2025.Application.DanhMuc.Tinh.DTOs;
using NHATTAMID2025.Application.DanhMuc.Xa.Commands;
using NHATTAMID2025.Application.DanhMuc.Xa.DTOs;
using NHATTAMID2025.Application.DanhMuc.Xa.Queries;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class Xa : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllXa, "/getall")
            .MapGet(GetAllXaByID, "/getbyid/{Id}")
            .MapGet(GetAllXaByMa, "/getbymaxa/{MaXa}")
            .MapGet(GetAllXaByMaTinh, "/getbymatinh/{MaTinh}")
            .MapGet(GetAllXaByTinhID, "/getbytinhid/{TinhID}")
            .MapPost(CreateXa, "/create")
            .MapPost(UpdateXa, "/update")
            .MapPost(DeleteXa, "/delete")
        ;
    }
    public async Task<Result<List<XaDto>>?> GetAllXa(ISender sender)
    => await sender.Send(new GetAllXaCommand());
    public async Task<Result<List<XaDto>>?> GetAllXaByID(ISender sender, [FromRoute] string id)
    => await sender.Send(new GetAllXaByIDQuery(id));
    public async Task<Result<List<XaDto>>?> GetAllXaByMa(ISender sender, [FromRoute] string MaXa)
    => await sender.Send(new GetAllXaByMaIDQuery(MaXa));
    public async Task<Result<List<XaDto>>?> GetAllXaByMaTinh(ISender sender, [FromRoute] string MaTinh)
    => await sender.Send(new GetAllXaByMaTinhQuery(MaTinh));

    public async Task<Result<List<XaDto>>?> GetAllXaByTinhID(ISender sender, [FromRoute] string TinhID)
    => await sender.Send(new GetAllXaByTinhIDQuery(TinhID));


    public async Task<Result<XaDto>?> CreateXa(ISender sender, [FromBody] CreateXaCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<XaDto>?> UpdateXa(ISender sender, [FromBody] UpdateXaCommand command)
    {
        return await sender.Send(command);
    }
    public async Task<Result<XaDto>?> DeleteXa(ISender sender, [FromBody] DeleteXaCommand command)
    {
        return await sender.Send(command);
    }
}
