﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <meta name="api-base-url" content="@ViewBag.ApiBaseUrl" />
    <link href="~/TablerTheme/libs/fontawesome/css/all.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/jquery/css/jquery-ui.custom.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/jquery/css/jquery-ui.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/font-awesome/4.7.0/css/font-awesome.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/boxicons-2.1.4/css/boxicons.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/select2/select2.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/toastr/toastr.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/demo.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/tabler-vendors.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/tabler.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/datepicker/bootstrap-datepicker3.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/datepicker/bootstrap-datetimepicker.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/daterangepicker/daterangepicker.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/confirm/dist/jquery-confirm.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/tabulator/dist/css/tabulator.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/tabulator/dist/css/tabulator_custom.min.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/css/style-custom.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/dropzone/dist/dropzone.css" rel="stylesheet" asp-append-version="true" />
	<link href="~/TablerTheme/libs/dropzone/dist/dropzone.css" rel="stylesheet" asp-append-version="true" />
	<script src="~/TablerTheme/libs/jquery/jquery-3.6.3.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/tabulator/dist/js/tabulator.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/dropzone/dist/dropzone-min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/dropzone/dist/dropzone-min.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntslibrary.js" asp-append-version="true"></script>
	<script src="~/dungchung/customs.js" asp-append-version="true"></script>
	<script src="~/dungchung/permission.js" asp-append-version="true"></script>
    @RenderSection("Styles", required: false)
    <style>
        .message-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.4); /* nền mờ */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999; /* đảm bảo nổi trên tất cả */
        }

            .message-loading-overlay .ace-icon {
                width: 48px;
                height: 48px;
            }
    </style>
</head>
<body class ="hold-transition layout-footer-fixed layout-fixed">
	<div class="page">
		<div class="page-wrapper"> 
            <div class="page-body">
				<div class="container-xl">
					<div id="Loadding"> </div>
					@RenderBody()
				</div>
			</div>
		</div>
	</div>
    <script>
        var apiBaseUrl = document
        .querySelector('meta[name="api-base-url"]')
        .getAttribute("content");
    </script>
	<script src="~/TablerTheme/libs/select2/select2.full.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/jquery/jquery-ui.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/bootstrap/dist/js/bootstrap.bundle.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/datepicker/bootstrap-datepicker.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/moment/moment.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/datepicker/bootstrap-datetimepicker.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/daterangepicker/daterangepicker.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/toastr/toastr.min.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/confirm/js/jquery-confirm.js" asp-append-version="true"></script>
	<script src="~/TablerTheme/libs/confirm/dist/jquery-confirm.min.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntsplugin.js" asp-append-version="true"></script>
	<script src="~/dungchung/ntsvalidate.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
