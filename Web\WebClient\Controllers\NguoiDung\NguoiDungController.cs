﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace NHATTAMID2025.Web.WebClient.Controllers.NguoiDung;
[ApiExplorerSettings(IgnoreApi = true)]
[Authorize]
public class NguoiDungController : Controller
{
    [Route("{controller}/{action}")]
    public IActionResult ThongTinNguoiDung()
    {
        return View();
    }

    [Route("{controller}/{action}")]
    public IActionResult ThongTinGiayPhep()
    {
        return View();
    }
    [Route("{controller}/{action}")]
    public IActionResult GioiThieu()
    {
        return View();
    }
    [Route("{controller}/{action}")]
    public IActionResult LienKetTaiKhoan()
    {
        return View();
    }
}
