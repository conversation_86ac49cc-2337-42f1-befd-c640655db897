﻿using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
namespace NHATTAMID2025.Web.Endpoints.HeThong;

public class DangKyUsersController : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .MapPost(CreateDangKyUsers, "/");
    }
    public async Task<Result<UsersDangKyDto>?> CreateDangKyUsers(ISender sender, [FromBody] CreateDangKyUsersCommand command)
    {
        return await sender.Send(command);
    }

}
