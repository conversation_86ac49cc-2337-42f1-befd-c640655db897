﻿@{
    ViewData["Title"] = "Home Page";
    Layout = "_Layout";
}
<style>
    .cardview {
        min-height: 100vh; /* <PERSON><PERSON><PERSON> cao bằng chiều cao màn hình */
        display: flex;
        flex-direction: column;
        border-radius: 0 !important;
    }

   

 

    .banner {
        position: relative;
        overflow: hidden;
        background-size: cover;
        background-position: center;
        height: 300px;
        display: flex;
        align-items: center;
        padding: 2rem;
        color: white;
    }

    .banner-overlay {
        background-color: rgb(46 146 122 / 50%);
        padding: 2rem;
        border-radius: 6px;
    }

    .banner {
        height: 200px;
        padding: 0rem;
    }

    .banner-overlay h2 {
        font-size: 1.5rem;
    }

    .banner-overlay p {
        font-size: 1rem;
    }






    .steps-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 19px;
    }

    .step {
        position: relative;
        border: var(--tblr-card-border-width) solid var(--tblr-card-border-color);
        border-radius: 6px;
        padding: 20px;
        width: 355px;
    }

    .step-index {
        position: absolute;
        top: -18px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgb(255 255 255);
        color: rgb(98 144 216);
        font-weight: bold;
        font-size: 16px;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border: 1px solid rgb(41 114 219);
    }

    .step-content {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .step-image {
        width: 48px;
        height: 48px;
        background-color: #d1f0de;
        background-size: 60%;
        background-repeat: no-repeat;
        background-position: center;
        border-radius: 8px;
        flex-shrink: 0;
    }

    .step-text {
        font-size: 15px;
        color: #333333;
        line-height: 1.5;
    }

    /* Icon riêng cho từng bước */
    .icon-step-1 {
        background-image: url('https://cdn-icons-png.flaticon.com/512/295/295128.png');
    }

    .icon-step-2 {
        background-image: url('https://cdn-icons-png.flaticon.com/512/1250/1250615.png');
    }

    @@keyframes smoothBounce {
        0%

    {
        transform: translateY(40px) scale(0.95);
        opacity: 0;
    }

    60% {
        transform: translateY(-8px) scale(1.03);
        opacity: 1;
    }

    80% {
        transform: translateY(4px) scale(0.98);
    }

    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    }

    .bounce-col {
        animation: smoothBounce 0.9s ease-out forwards;
        opacity: 0; /* bắt đầu ẩn */
        will-change: transform, opacity;
    }

  
    .bounce-delay-1 {
        animation-delay: 0.1s;
    }

    .bounce-delay-2 {
        animation-delay: 0.2s;
    }

    .bounce-delay-3 {
        animation-delay: 0.3s;
    }

    .bounce-delay-4 {
        animation-delay: 0.4s;
    }

    .bounce-delay-5 {
        animation-delay: 0.5s;
    }

    .bounce-delay-6 {
        animation-delay: 0.6s;
    }

    .bounce-delay-7 {
        animation-delay: 0.7s;
    }

    .bounce-delay-8 {
        animation-delay: 0.8s;
    }

    .bottom-fade {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 80px; /* điều chỉnh chiều cao fade */
        background: linear-gradient(to top, rgba(255,255,255,0.9), rgba(255,255,255,0));
        pointer-events: none; /* để không che nút bấm phía trên */
        z-index: 2;
    }

</style>

<style>
    .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 2rem;
        margin-top: 4rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease-in-out;
    }

        .no-data-message .nodata-img {
            width: 300px;
            height: auto;
            max-width: 100%;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .no-data-message p {
            font-size: 1.1rem;
            font-style: italic;
            margin: 0;
        }
</style>
<div class="card cardview" style="border-radius: 0 !important;">
    <div >
        <div class="banner ">
            <div class="banner-overlay left-section" style="margin-left:14px">
                <h2>Quản lý giấy phép & liên kết tài khoản</h2>
                <p>Giải pháp tối ưu giúp người dùng kiểm soát chặt chẽ giấy phép và đồng bộ hóa dữ liệu tài khoản.</p>
                <a href="/nguoidung/thongtingiayphep" class="btn btn-primary mt-3">Khám phá ngay</a>
            </div>
            <div class="bottom-fade"></div>
        </div> 
    </div>

    <div class="container-xl  mt-5" style="margin-top: -3px !important;">
        <h2 class="ms-1 mb-0 ">Hiệu quả trên các dịch vụ của NTSOFT</h2>
        <div class="row g-4 " style="margin-top:1px">

            <!-- Bảo mật & kiểm soát -->
            <div class="col-md-4 left-section bounce-delay-1 bounce-col">
                <div class="card p-4 h-100 text-center">
                    <img src="/Images/Copilot_20250613_081329.png" alt="Bảo mật" width="100" class="mb-3 mx-auto">
                    <div class="text-primary mb-2">
                        <i class="ti ti-shield-check icon-lg"></i>
                    </div>
                    <h3 class="card-title">Bảo mật & kiểm soát</h3>
                    <p class="text-muted">Phần mềm giúp người dùng đảm bảo an toàn giấy phép và ngăn chặn truy cập trái phép.</p>
                </div>
            </div>

            <!-- Liên kết tài khoản linh hoạt -->
            <div class="col-md-4 left-section bounce-delay-2 bounce-col">
                <div class="card p-4 h-100 text-center">
                    <img src="/Images/Copilot_20250612_165210.png" alt="Liên kết tài khoản" width="97" class="mb-3 mx-auto">
                    <div class="text-primary mb-2">
                        <i class="ti ti-link icon-lg"></i>
                    </div>
                    <h3 class="card-title">Liên kết tài khoản linh hoạt</h3>
                    <p class="text-muted">Dễ dàng kết nối và quản lý nhiều tài khoản người dùng trong cùng hệ thống một cách đồng bộ.</p>
                </div>
            </div>

            <!-- Tăng tốc hiệu quả -->
            <div class="col-md-4 left-section bounce-delay-3 bounce-col">
                <div class="card p-4 h-100 text-center">
                    <img src="/Images/Copilot_20250612_165445.png" alt="Tăng tốc hiệu quả" width="97" class="mb-3 mx-auto">
                    <div class="text-primary mb-2">
                        <i class="ti ti-rocket icon-lg"></i>
                    </div>
                    <h3 class="card-title">Tăng tốc hiệu quả</h3>
                    <p class="text-muted">Tự động hóa quy trình quản lý, giúp nâng cao hiệu suất vận hành và tiết kiệm thời gian.</p>
                </div>
            </div>

        </div>
    </div>
    <div class="container-xl py-5 steps-wrapper">

        <h2 class="ms-1 mb-0">Các bước sử dụng dịch vụ của NTSOFT</h2>
        <div class="steps-container mt-4">
            <!-- Bước 1 -->
            <div class="step bounce-delay-4 bounce-col">
                <span class="step-index">1</span>
                <div class="step-content">
                    <div class="">
                        <img src="/Images/music_9607081.png" alt="Tăng tốc hiệu quả" width="45" class=" mx-auto">
                    </div>
                    <span>
                        Đăng ký tài khoản bằng email hoặc số điện thoại.
                    </span>
                </div>
            </div>

            <!-- Bước 2 -->
            <div class="step  bounce-delay-5 bounce-col">
                <span class="step-index">2</span>
                <div class="step-content">
                    <div class="">
                        <img src="/Images/bag_8022668.png" alt="Tăng tốc hiệu quả" width="45" class=" mx-auto">
                    </div>
                    <span>Chọn gói dịch vụ phù hợp và gửi yêu cầu.</span>
                </div>
            </div>

            <!-- Bước 3 -->
            <div class="step bounce-delay-6 bounce-col">
                <span class="step-index">3</span>
                <div class="step-content">
                    <div class="">
                        <img src="/Images/mail_6323118.png" alt="Tăng tốc hiệu quả" width="45" class=" mx-auto">
                    </div>
                    <span>Xác nhận và bắt đầu sử dụng dịch vụ.</span>
                </div>
            </div>


        </div>
    </div>

    <div class="container-xl mt-4" >
        <h2 class="ms-1 mb-0">Thông tin sản phẩm đã kích hoạt</h2>
        <div class="row row-cards  bounce-delay-7 bounce-col" style="margin-top:1px" id="DSSanPhamDaKichHoat">
         </div>

</div>

   


    <div class="container-xl mt-1" style="margin-bottom:8px">
        <h2 class="ms-1 mb-0">Giới thiệu sản phẩm NTSOFT</h2>
        <div class="row row-cards  bounce-delay-8 bounce-col" style="margin-top:1px" id="DSGioiThieuSP">
            
        </div>
    </div>












</div>

@* <div class="modal modal-blur fade" id="DangKyModal" tabindex="-1" aria-labelledby="DangKyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="DangKyModalForm">
                <div class="modal-header">
                    <h5 class="modal-title">
                        Đăng ký phần mềm <span class="text-primary" id="mdTenPhanMem"></span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted mb-3" id="mdMoTa">
                        
                    </p>

                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Họ và tên:</div>
                        <div class="col-7" id="mdHoTen">Nguyễn Văn A</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Số điện thoại:</div>
                        <div class="col-7" id="mdSDT">0912 345 678</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5 fw-bold">Email:</div>
                        <div class="col-7" id="mdEmail"><EMAIL></div>
                    </div>
              
                    <div class="mb-3">
                        <label class="form-label">Nội dung đăng ký</label>
                        <textarea class="form-control" rows="3" id="noidung" placeholder="Nhập nội dung hoặc yêu cầu thêm, nhu cầu sử dụng..."></textarea>
                    </div>
                    <div class="custom-alert" role="alert" >
                        Sau khi gửi yêu cầu, nhân viên của <strong style=" font-weight: 600;color: #0d6efd;">NTSOFT</strong> sẽ liên hệ lại với bạn để hỗ trợ.
                    </div>
                </div>
                <div class="modal-footer">  
                    <button type="submit" class="btn btn-success">
                        <i class="ti ti-device-floppy me-1"></i> Gửi yêu cầu
                    </button>
                </div>
            </form>

        </div>
    </div>
</div> *@


<div class="modal modal-blur fade" id="DangKyModal" tabindex="-1" aria-labelledby="userInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <form id="DangKyModalForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="userInfoModalLabel">Thông tin người dùng</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Security Alert -->
                    <div class="alert alert-warning d-flex align-items-start" role="alert">
                        <i class="fas fa-info-circle me-2 mt-1 text-warning" style="font-size: 20px;"></i>
                        <div>
                            <h4 class="alert-title mb-1 text-warning">Thông báo hỗ trợ khách hàng</h4>
                            <div class="text-secondary">
                                Bạn đang xem thông tin yêu cầu <strong>đăng ký</strong> hoặc <strong>nâng cấp gói phần mềm</strong> từ khách hàng.<br>
                                Hãy kiểm tra thông tin và <strong>liên hệ khách hàng</strong> để xác nhận và hỗ trợ theo đúng quy trình.
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Họ và tên</label>
                        <p id="mdHoTen" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <p id="mdEmail" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Số điện thoại</label>
                        <p id="mdSDT" class="text-muted"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đơn vị công tác</label>
                        <p id="donViCongTac" class="text-muted"></p>
                    </div>

                    <div class="mb-3 highlight-section">
                        <div class="">
                         
                            <div class="mb-3" style="margin-top:4px">
                                <label class="form-label">Bạn đang quan tâm đến giải pháp nào hoặc có nhu cầu cụ thể gì? </label>
                                <p id="mdSDT" class="text-muted">Vui lòng chia sẻ với chúng tôi!</p>
                                <textarea class="form-control" rows="3" id="noidung" placeholder="Nhập nội dung cần hỗ trợ" ></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="ti ti-device-floppy me-1"></i> Gửi yêu cầu
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="ti ti-x me-1"></i> Hủy
                    </button>
                </div>
            </form>

        </div>
    </div>
</div>
<script src="~/dungchung/three.min.js"></script>
<script src="~/dungchung/vanta.rings.min.js"></script>
<script>
    VANTA.RINGS({
        el: ".banner",
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200.00,
        minWidth: 200.00,
        scale: 1.00,
        scaleMobile: 1.00,
        backgroundColor: 0xcffff7
    });

</script>
<input type="hidden" id="hfPhanMemID" />

<script src="~/scripts/nguoidung/gioithieu.js"></script>
