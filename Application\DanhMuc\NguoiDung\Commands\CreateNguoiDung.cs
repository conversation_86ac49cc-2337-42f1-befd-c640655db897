﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.Common.Utilities;
using NHATTAMID2025.Application.DanhMuc.NguoiDung.DTOs;
using WEB_DLL;

namespace NHATTAMID2025.Application.DanhMuc.NguoiDung.Commands;
public record CreateNguoiDungCommand(
    string UserID,
    string TenDangNhap,
    string MatMa,
    string NhapLaiMatMa,
    string Avatar,
    string HoVaTen,
    string NgaySinh,
    string GioiTinh,
    string SoDienThoai,
    string Email,
    string DiaChi,
    string DonViCongTac,
    bool DangSD,
    string LoaiUsers
    ) : IRequest<Result<NguoiDungDto>?>;

public class CreateCommandValidator : AbstractValidator<CreateNguoiDungCommand>
{
    public CreateCommandValidator()
    {
    }
}

public class CreateNguoiDungCommandHandler : IRequestHandler<CreateNguoiDungCommand, Result<NguoiDungDto>?>
{
    private readonly IApplicationDbContext _context;

    public CreateNguoiDungCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<Result<NguoiDungDto>?> Handle(CreateNguoiDungCommand request, CancellationToken cancellationToken)
    {
        var id = Guid.NewGuid();
        string keyMaHoaMatKhau = "rateAnd2012";
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@TenDangNhap", request.TenDangNhap),
            new Microsoft.Data.SqlClient.SqlParameter("@MatMa",  ntsSecurity._mEncrypt( request.MatMa.Trim(), keyMaHoaMatKhau, true ) ),
            new Microsoft.Data.SqlClient.SqlParameter("@NhapLaiMatMa",  ntsSecurity._mEncrypt( request.NhapLaiMatMa.Trim(), keyMaHoaMatKhau, true )),
            new Microsoft.Data.SqlClient.SqlParameter("@HoVaTen", request.HoVaTen),
            new Microsoft.Data.SqlClient.SqlParameter("@NgaySinh", string.IsNullOrWhiteSpace(request.NgaySinh) ? DBNull.Value : request.NgaySinh),
            new Microsoft.Data.SqlClient.SqlParameter("@GioiTinh", request.GioiTinh),
            new Microsoft.Data.SqlClient.SqlParameter("@SoDienThoai", request.SoDienThoai),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email),
            new Microsoft.Data.SqlClient.SqlParameter("@DiaChi", request.DiaChi),
            new Microsoft.Data.SqlClient.SqlParameter("@DonViCongTac", request.DonViCongTac),
            new Microsoft.Data.SqlClient.SqlParameter("@LoaiUsers", request.LoaiUsers)
        };

        try
        {

            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_NguoiDung_Create",
                   MapFromReader,
                   true,
                   parameters
               );


            return Result<NguoiDungDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return null;
        }
    }

    private NguoiDungDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new NguoiDungDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID")),
        };
    }
}
