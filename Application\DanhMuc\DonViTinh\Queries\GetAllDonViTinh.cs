﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.DanhMuc.DonViTinh.DTOs;

namespace NHATTAMID2025.Application.DanhMuc.DonViTinh.Queries;
public record GetAllDonViTinhQuery : IRequest<List<DonViTinhDto>>;
public class GetAllDonViTinhQueryValidator : AbstractValidator<GetAllDonViTinhQuery>
{
    public GetAllDonViTinhQueryValidator()
    {
    }
}
public class GetAllDonViTinhQueryHandler : IRequestHandler<GetAllDonViTinhQuery, List<DonViTinhDto>>
{
    private readonly IApplicationDbContext _context;

    public GetAllDonViTinhQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<List<DonViTinhDto>> Handle(GetAllDonViTinhQuery request, CancellationToken cancellationToken)
    {
        const string spGetAll = "sp_DonViTinh_GetAll"; // tên store procedure

        return await _context.ExecuteSqlQueryRawAsync(
            spGetAll,
            MapFromReader,
            false,
            Array.Empty<System.Data.Common.DbParameter>()
        );
    }

    private DonViTinhDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new DonViTinhDto
        {
            DonViTinhID = reader.GetGuid(reader.GetOrdinal("DonViTinhID")),
            MaDonViTinh = reader["MaDonViTinh"] as string,
            TenDonViTinh = reader["TenDonViTinh"] as string,
            NgungTD = reader.IsDBNull(reader.GetOrdinal("NgungTD")) ? null : reader.GetBoolean(reader.GetOrdinal("NgungTD"))
        };
    }
}
