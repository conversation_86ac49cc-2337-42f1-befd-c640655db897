﻿using Microsoft.AspNetCore.Mvc;
using NHATTAMID2025.Application.DanhMuc.LinhVucs.DTOs;
using NHATTAMID2025.Application.LinhVucs.Queries.GetAllLinhVuc;

namespace NHATTAMID2025.Web.Endpoints.DanhMuc;

public class LinhVuc : EndpointGroupBase
{
    public override void Map(WebApplication app)
    {
        app.MapGroup(this)
            .RequireAuthorization()
            .MapGet(GetAllLinhVuc, "/getall");
    }

    public async Task<PaginatedList<LinhVucDto>> GetAllLinhVuc(
        ISender sender,
        [FromQuery] int? pageNumber,
        [FromQuery] int? pageSize)
    {
        var query = new GetAllLinhVucQuery(pageNumber, pageSize);
        return await sender.Send(query);
    }
}
