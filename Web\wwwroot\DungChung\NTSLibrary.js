﻿$(".cke").removeAttr("tabindex");
var TabulatorLangsVi = {
  "default": {
    "columns": {
      "name": "Name",
    },
    "ajax": {
      "loading": "Đang tải...",
      "error": "Lỗi tải dữ liệu",
    },
    "groups": {
      "item": "dòng",
      "items": "dòng",
    },
    "pagination": {
      "page_size": "Kích thước",
      "page_title": "Hiển thị",
      "first": '<i class="fa fa-step-backward" aria-hidden="true"></i>',
      "first_title": "Trang đầu",
      "last": '<i class="fa fa-step-forward" aria-hidden="true"></i>',
      "last_title": "Trang cuối",
      "prev": '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
      "prev_title": "<PERSON><PERSON><PERSON> lại",
      "next": '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
      "next_title": "<PERSON>ế tiếp",
      "all": "All",
      "counter": {
        "showing": "Hiển thị",
        "of": "của",
        "rows": "dòng",
        "pages": "trang",
      }
    },
    "headerFilters": {
      "default": "filter column...",
      "columns": {
        "name": "filter name...",
      }
    },
  }
}
function getUrl() {
    var vars = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for (var i = 0; i < hashes.length; i++) {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
    return vars;
}
//Xu ly khong xoa
//Ket thuc xu ly khong xoa
String.prototype.replaceAll = function (strTarget, strSubString) {
    var strText = this;
    if (strText.length > 0) {
        var intIndexOfMatch = strText.indexOf(strTarget);
        while (intIndexOfMatch != -1) {
            strText = strText.replace(strTarget, strSubString)
            intIndexOfMatch = strText.indexOf(strTarget);
        }
        return (strText);
    } else {
        return "";
    }
}
function checkDate(value, flag) {
    var re = /^\d{1,2}\/\d{1,2}\/\d{4}$/;
    //Cho phép rỗng
    if (flag == true) {
        if (!value.match(re)) {
            return false;
        } else
            return true;
    } else if (value != '' && !value.match(re)) {
        return false;
    }
}
//Kiểm tra value null hoặc bằng 0
function isEmtyValue(value) {
    if (value == "0" || value == "")
        return true;
    return false;
}

function bindData(comboName, dataSource) {
    comboName.options.clear();
    if (dataSource != null) {
        $.each(JSON.parse(dataSource.toJSON()).Rows, function (index, item) {
            comboName.options.add(item[1] + "", item[0] + "", comboName.options.length);
        })
    }
}
//custom filter function
function matchAny(data, filterParams) {
    //data - the data for the row being filtered
    //filterParams - params object passed to the filter
    var regex = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i;
    var match = false;
    for (var key in data) {
        try {
            if (data[key] != "" && !regex.test(data[key])) {
                if (data[key].toLowerCase().indexOf(filterParams.value.toLowerCase()) !== -1) {
                    match = true;
                    return match;
                }
            }
        }
        catch (e) { }
    }
    return match;
}
function checkEmtyValue(value) {
    if (value == "0" || value == "")
        return true;
    return false;
}
//Kiểm tra value null
function isEmty(value) {
    if (value == "")
        return true;
    return false;
}
//Định dạng window
function dinhDangWindow(winDowName) {
    var screenWidth = screen.width;
    var screenHeight = screen.height;
    var WindowSize = winDowName.getSize();
    winDowName.setPosition(parseFloat((parseFloat(screenWidth) - parseFloat(WindowSize.width)) / 2), window.scrollY + 100);
    winDowName.Open();
}

var keyEnter = 0;
function performSearch(grid, index, value) {
    if (keyEnter != "1") {
        return false
    }
    keyEnter = 0;
    for (var i = index; i < grid.ColumnsCollection.length - 1; i++) {
        var s = grid.ColumnsCollection[i].DataField;
        if (grid.ColumnsCollection[i].Visible == true) {
            grid.addFilterCriteria(s, OboutGridFilterCriteria.Contains, value);
        }
    }
    grid.executeFilter();
    searchTimeout = null;
    return false;
}
var searchTimeout = null;

function searchValue(grid, index, value) {
    if (keyEnter != "1") {
        return false
    }
    keyEnter = 0;
    if (searchTimeout != null) {
        return false;
    }
    if (searchTimeout != null) {
        return false;
    }
    if (jQuery.type(value) == "undefined")
        value = '';
    for (var i = index; i < grid.ColumnsCollection.length; i++) {
        if (grid.ColumnsCollection[i].HeaderText != "") {
            var s = grid.ColumnsCollection[i].DataField;
            if (grid.ColumnsCollection[i].Visible == true && s != "") {
                grid.addFilterCriteria(s, OboutGridFilterCriteria.Contains, value);
            }
        }
    }
    //if (jQuery.type(grid.executeFilter()) == "undefined") {
    //    alert("Looix");
    //    return false;
    //}
    searchTimeout = window.setTimeout(grid.executeFilter(), 2000);
    searchTimeout = null;
    return false;
}
// tìm kiếm trong grid
// Range là mảng index cột trong grid
function SearchGridByValue(gridID, Range, value) {
    if (keyEnter != "1") {
        return false
    }
    keyEnter = 0;
    if (searchTimeout != null) {
        return false;
    }
    (jQuery.type(value) == "undefined") && (value = '');
    (jQuery.isEmptyObject(Range)) && (Range.length = 0);
    if (Range.length > 0 && Range[0] == -1) {
        for (var i = index; i < grid.ColumnsCollection.length; i++) {
            if (grid.ColumnsCollection[i].HeaderText != "") {
                var s = grid.ColumnsCollection[i].DataField;
                if (grid.ColumnsCollection[i].Visible == true && s != "") {
                    grid.addFilterCriteria(s, OboutGridFilterCriteria.Contains, value);
                }
            }
        }
    }
    else {
        for (var i = 0; i < Range.length; i++) {
            if (gridID.ColumnsCollection[Range[i]].HeaderText != "") {
                var ColName = gridID.ColumnsCollection[Range[i]].DataField;
                (gridID.ColumnsCollection[Range[i]].Visible == true && ColName != "") && (gridID.addFilterCriteria(ColName, OboutGridFilterCriteria.Contains, value));
            }
        }
    }

    gridID.executeFilter();
    (value == '' || value == undefined) && (gridID.executeFilter());
    return false;
}

function bieuDoTron(result, tenBieuDo, DivID) {
    var dataTableGoogle = new google.visualization.DataTable();
    for (var i = 0; i < result.Columns.length; i++) {
        if (i == 0)
            dataTableGoogle.addColumn('string', result.Columns[i].Name);
        else
            dataTableGoogle.addColumn('number', result.Columns[i].Name);
    }
    dataTableGoogle.addRows(
        JSON.parse(result.toJSON()).Rows
    );
    // Instantiate and draw our chart, passing in some options
    var chart = new google.visualization.PieChart(document.getElementById(DivID));
    chart.draw(dataTableGoogle, {
        title: tenBieuDo,
        position: "top",
        fontsize: "14px",
        chartArea: {
            width: '100%',
            height: '100%'
        },
    });
}

function bieuDoTron3D(result, tenBieuDo, DivID) {
    var dataTableGoogle = new google.visualization.DataTable();
    for (var i = 0; i < result.Columns.length; i++) {
        if (i == 0)
            dataTableGoogle.addColumn('string', result.Columns[i].Name);
        else
            dataTableGoogle.addColumn('number', result.Columns[i].Name);
    }
    dataTableGoogle.addRows(
        JSON.parse(result.toJSON()).Rows
    );
    // Instantiate and draw our chart, passing in some options
    //var chart = new google.visualization.PieChart(document.getElementById(DivID));
    var options = {
        title: tenBieuDo,
        position: "top",
        fontsize: "12px",
        top: '20px',
        chartArea: {
            width: '100%',
            height: '80%'
        },
        is3D: true
    };

    var chart = new google.visualization.PieChart(document.getElementById(DivID));

    chart.draw(dataTableGoogle, options);

    //chart.draw(dataTableGoogle,
    //    {
    //        title: tenBieuDo,
    //        position: "top",
    //        fontsize: "14px",
    //        top:'20px',
    //        chartArea: { width: '300px', height: '100%' },
    //        is3D:true
    //    });
}

function veBieuDoCot(result, tenBieuDo, DivID) {

    var dataTableGoogle = new google.visualization.DataTable();
    for (var i = 0; i < result.Columns.length; i++) {
        if (i == 0)
            dataTableGoogle.addColumn('string', result.Columns[i].Name);
        else
            dataTableGoogle.addColumn('number', result.Columns[i].Name);
    }
    dataTableGoogle.addRows(
        JSON.parse(result.toJSON()).Rows
    );
    // Instantiate and draw our chart, passing in some options
    var chart = new google.visualization.ColumnChart(document.getElementById(DivID));
    chart.draw(dataTableGoogle, {
        title: tenBieuDo,
        position: "top",
        fontsize: "14px",
    });
}

function veBieuDoPhatTrien(result, tenBieuDo, DivID) {
    if (result == null) {
        $('#' + DivID).html("");

    } else {
        var dataTableGoogle = new google.visualization.DataTable();
        for (var i = 0; i < result.Columns.length; i++) {
            if (i == 0)
                dataTableGoogle.addColumn('string', result.Columns[i].Name);
            else
                dataTableGoogle.addColumn('number', result.Columns[i].Name);
        }
        dataTableGoogle.addRows(
            JSON.parse(result.toJSON()).Rows
        );
        var options = {
            title: tenBieuDo,
        };
        var chart = new google.visualization.LineChart(document.getElementById(DivID));
        chart.draw(dataTableGoogle, options);
    }

}
//var pickerFn = $.fn.datepicker,
//    placeholder = 'dd/MM/yyyy';
//$.fn.datepicker = function () {
//    var datePicker = pickerFn.apply(this, arguments);
//    var self = this;
//    self.attr('placeholder', placeholder);
//    return datePicker;
//};
// control boostrap
//Ngày bỗ sung: 18/12/2018
//Người bỗ sung: Vịnh
// Nội dung: các hàm sử dụng cho theme boostrap
function Loadding(class_) {
    $('.' + class_).append('<div class="message-loading-overlay"><i class="fa-spin ace-icon fa fa-spinner orange2 bigger-260"></i></div>');
}
function Loadding_Finish(class_) {
    $('.' + class_).find('.message-loading-overlay').remove();
}
function An_HienNut(idBotton, thaoTac) {
    $('#' + idBotton).css("visibility", '' + thaoTac + '');
    $('#' + idBotton).css("display", '' + thaoTac + '');
}
function HienThiControl(idBotton, visibe) {
    if (visibe) {
        $('#' + idBotton).css("visibility", "visible");
        $('#' + idBotton).css("display", "inline-block");
    } else {
        $('#' + idBotton).css("visibility", "hidden");
        $('#' + idBotton).css("display", "none");
    }
}
//Loại bỏ các khoảng trắng ở đầu, cuối và dư thừa của chuỗi.
function trimSpace(str) {
    if (str == "" || str == null)
        return "";
    else
        return str.replace(/(?:(?:^|\n)\s+|\s+(?:$|\n))/g, "").replace(/\s+/g, " ");
}
//kiem tra email
function KiemTraEmail(sEmail) {
    var filter = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
    if (filter.test(sEmail)) {
        return true;
    } else {
        return false;
    }
}
//kiem tra sdt
function KiemTraSDT(txtPhone) {
    var filter = /^[0-9]+$/;
    if (filter.test(txtPhone)) {
        return true;
    } else {
        return false;
    }
}
function kiemTraTenDangNhap(chuoi) {
    var usernameRegex = /^[a-zA-Z0-9-_]+$/;
    if (usernameRegex.test(chuoi)) {
        return true;
    } else {
        return false;
    }
}
$(document).keypress(function (e) {
    if (!e.shiftKey && e.keyCode == 13) {
        keyEnter = 1;
        return false;
    }
});

var now = new Date();
var dayArray = new Array(9);
function layThoiGianTheoKyBaoCao(idTuNgay, idDenNgay, idKyBaoCao) {
    $('#' + idTuNgay + '').prop('disabled', true);
    $('#' + idDenNgay + '').prop('disabled', true);
    var ngayHienTai = '';
    var thangHienTai = '';
    dayArray[0] = "01";
    dayArray[1] = "02";
    dayArray[2] = "03";
    dayArray[3] = "04";
    dayArray[4] = "05";
    dayArray[5] = "06";
    dayArray[6] = "07";
    dayArray[7] = "08";
    dayArray[8] = "09";
    if (now.getDate() < 10)
        ngayHienTai = dayArray[now.getDate() - 1];
    else
        ngayHienTai = now.getDate();
    if ($('#' + idKyBaoCao + '').val() < 10)
        thangHienTai = dayArray[$('#' + idKyBaoCao + '').val() - 1];
    else
        thangHienTai = $('#' + idKyBaoCao + '').val();
    if ($('#' + idKyBaoCao + '').val() == "") {
        $('#' + idTuNgay + '').prop('disabled', false);
        $('#' + idDenNgay + '').prop('disabled', false);
    } else if ($('#' + idKyBaoCao + '').val() < 13) {
        $('#' + idTuNgay + '').val('01/' + thangHienTai + '/' + now.getFullYear());
        $('#' + idDenNgay + '').val(getDaysOfMonth(new Date().getFullYear(), $('#' + idKyBaoCao + '').val()) + '/' + thangHienTai + '/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 13) {
        $('#' + idTuNgay + '').val('01/01/' + now.getFullYear());
        $('#' + idDenNgay + '').val('31/12/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 14) {
        $('#' + idTuNgay + '').val('01/01/' + now.getFullYear());
        $('#' + idDenNgay + '').val('31/03/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 15) {
        $('#' + idTuNgay + '').val('01/04/' + now.getFullYear());
        $('#' + idDenNgay + '').val('30/06/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 16) {
        $('#' + idTuNgay + '').val('01/07/' + now.getFullYear());
        $('#' + idDenNgay + '').val('30/09/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 17) {
        $('#' + idTuNgay + '').val('01/10/' + now.getFullYear());
        $('#' + idDenNgay + '').val('31/12/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 18) {
        $('#' + idTuNgay + '').val('01/01/' + now.getFullYear());
        $('#' + idDenNgay + '').val('30/06/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 19) {
        $('#' + idTuNgay + '').val('01/07/' + now.getFullYear());
        $('#' + idDenNgay + '').val('31/12/' + now.getFullYear());
    }
    else if ($('#' + idKyBaoCao + '').val() == 20) {
        $('#' + idTuNgay + '').prop('disabled', false);
        $('#' + idDenNgay + '').prop('disabled', false);
    }
}
function getDaysOfMonth(year, month) {
    return new Date(year, month, 0).getDate();
}

$(function () {
    $('.date-picker').datepicker({
        format: 'dd/mm/yyyy',//use this option to display seconds
        autoclose: true,
        todayHighlight: true,
        startDate: new Date('1900-01-01'),
        endDate: new Date('2079-12-31')
    });
    $('.date-picker').wrap('<div class="input-group"></div>');
    $('.date-picker').after(`<span class="input-group-text cursor-pointer"><i class="fa fa-calendar"></i></span>`);
    $('.date-picker').prop('placeholder', 'dd/MM/yyyy');
    $('.date-picker + .input-group-text').on('click', function () {
        $(this).prev().focus();
    });
    $('input[name="datepicker"]').wrap('<div class="input-group"></div>');
    $('input[name="datepicker"]').after(`<span class="input-group-text cursor-pointer" for="input[name='datepicker']"><i class="fa fa-calendar"></i></span>`);
    $('input[name="datepicker"]').prop('placeholder', 'dd/MM/yyyy');
    $('input[name="datepicker"] + .input-group-text').on('click', function () {
        $(this).prev().focus();
    });




    //    .next().on(ace.click_event, function () {
    //    $(this).prev().focus();
    //});
    $(document).on('keyup', '.number-format', function () {
        $(this).val(formatNumber($(this).val()));
    });
    $(document).on('change', '.number-format', function (evt) {
        $(this).val(formatNumber($(this).val()));
    });

    $(document).on('keyup', '.number-table-nts', function () {
        $(this).val(formatNumber_TableNTS($(this).val()));
    });
    $(document).on('change', '.number-table-nts', function (evt) {
        $(this).val(formatNumber_TableNTS($(this).val()));
    });

    //set
    $("button").on('dblclick', function (event) {
        event.preventDefault();
    });
});
function getAjax(kieuTraVe, duongDanAjax, duLieuGui) {
    var result = null;
    $.ajax({
        type: "POST",
        url: duongDanAjax,
        data: JSON.stringify(duLieuGui),
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (response) {
            result = response.d;
        },
        error: function (response) {
            result = null;
            console.log(response);
        }
    });
    if (result === null) return null;
    // xử lý kiểu trả về
    switch (kieuTraVe.toLowerCase()) {
        case "string":
            return result;
            break;
        case "boolean":
            try {
                return JSON.parse((result).toString().toLowerCase());
            } catch (e) {
                return false;
            }
            break;
        case "json":
            try {
                return JSON.parse(result.toString());
            } catch (e) {
                console.log(e);
            }
            break;
        default:
            return null;
            break;
    }
}
var quyenTruyCap;
function kiemTraPhanQuyen() {
    $.ajax({
        type: "POST",
        url: "../../Services/ServiceSystem.asmx/kiemTraPhanQuyen",
        data: "{}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (response) {
            quyenTruyCap = JSON.parse(response.d);
        },
        error: function () {
            quyenTruyCap = "";
        }
    });
}
function Loadding(class_) {
    $('' + class_ + '').append('<div class="message-loading-overlay"><i class="fa-spin ace-icon fa fa-spinner orange2 bigger-260"></i></div>');
}
function Loadding_Finish(class_) {
    $('' + class_ + '').find('.message-loading-overlay').remove();
}
function An_HienNut(idBotton, thaoTac) {
    $('' + idBotton + '').css("visibility", '' + thaoTac + '');
    $('' + idBotton + '').css("display", '' + thaoTac + '');
}
function HienThiControl(idBotton, visibe) {
    if (visibe) {
        $('' + idBotton + '').css("visibility", "visible");
        $('' + idBotton + '').css("display", "inline-block");
    }
    else {
        $('' + idBotton + '').css("visibility", "hidden");
        $('' + idBotton + '').css("display", "none");
    }
}

function HienThongBao(tieudeTB, noidungTB, loaiTB) {
    switch (loaiTB) {
        case "Loi":
            //lỗi
            $.gritter.add({
                title: tieudeTB,
                text: noidungTB,
                class_name: 'gritter-error' + (!$('#gritter-light').get(0).checked ? ' gritter-light' : '')
            });
            break;
        case "ThanhCong":
            //thành công
            $.gritter.add({
                title: tieudeTB,
                text: noidungTB,
                class_name: 'gritter-success' + (!$('#gritter-light').get(0).checked ? ' gritter-light' : '')
            });
            break;
        case "CanhBao":
            //cảnh báo
            $.gritter.add({
                title: tieudeTB,
                text: noidungTB,
                class_name: 'gritter-warning' + (!$('#gritter-light').get(0).checked ? ' gritter-light' : '')
            });
            break;
        case "ChiTiet":
            // hiện chi tiêt cảnh báo
            $.gritter.add({
                title: tieudeTB,
                text: noidungTB,
                class_name: 'gritter-info gritter-center' + (!$('#gritter-light').get(0).checked ? ' gritter-light' : '')
            });
            break;

    }
}
//Loại bỏ các khoảng trắng ở đầu, cuối và dư thừa của chuỗi.
function trimSpace(str) {
    if (str == "")
        return str;
    else
        return str.replace(/(?:(?:^|\n)\s+|\s+(?:$|\n))/g, "").replace(/\s+/g, " ");
}
//kiem tra email
function KiemTraEmail(sEmail) {
    var filter = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
    if (filter.test(sEmail)) {
        return true;
    }
    else {
        return false;
    }
}
//kiem tra sdt
function KiemTraSDT(txtPhone) {
    var filter = /^[0-9]+$/;
    if (filter.test(txtPhone)) {
        return true;
    }
    else {
        return false;
    }
}
//kiem tra ngay thang năm 
function KiemTraNgay(dtValue) {
    var dtRegex = new RegExp(/\b\d{1,2}[\/-]\d{1,2}[\/-]\d{4}\b/);
    return dtRegex.test(dtValue);
}
//cho input chỉ nhập số
function isNumberKey(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode
    if (charCode > 31 && (charCode < 48 || charCode > 57))
        return false;

    return true;
}

$(function () {
    $('.datetimepicker-input').datetimepicker({
        format: 'DD/MM/YYYY'
    });
    $('input[required], select[required], textarea[required]').prev('label').addClass('validation');
    $('.input-group > input[required], .input-group > select[required], .input-group > textarea[required]').parent().parent().find('label').addClass('validation');

});

var dinhDangSoLuoi = function (cell, formatterParams, onRendered) {
    return formatNumber(cell.getValue() != undefined && cell.getValue() != null ? cell.getValue() : '').toString();
}
function containsNonDigits(chuoi) {
    // Sử dụng biểu thức chính quy để kiểm tra chuỗi
    const regex = /^[0-9,.\-]+$/;

    // Kiểm tra xem chuỗi có khớp với biểu thức chính quy hay không
    return !regex.test(chuoi);
}
//Upload file tiny CME
function initTinyMCE(selector, height = 400) {
  tinymce.init({
    selector: 'textarea#' + selector,
    plugins: 'print preview paste importcss searchreplace autolink autosave save directionality code visualblocks visualchars fullscreen image link media template codesample table charmap pagebreak nonbreaking anchor toc insertdatetime advlist lists wordcount imagetools textpattern noneditable help charmap quickbars emoticons export',
    toolbar: 'undo redo | styles | bold italic underline | alignleft aligncenter alignright | bullist numlist outdent indent | link image media table | code | export pdf | print | customWordExport',
    height: height,
    automatic_uploads: true,
    // images_upload_url: 'https://localhost:5001/api/FileManagement/uploadTinyMCE',
    images_upload_url: '/Home/UploadImage',
    images_upload_credentials: true,
    quickbars_insert_toolbar: false,
    quickbars_selection_toolbar: false,
    // images_upload_method: 'POST', // <- Thêm dòng này
    setup: function (editor) {
      editor.on('init', function () {
        // Ẩn Upgrade và Branding nếu cần
        let style = document.createElement("style");
        style.innerHTML = `
            .tox-statusbar__branding,
            .tox-promotion {
                display: none !important;
            }`;
        document.head.appendChild(style);
      });

      // Thêm nút Export Word
      editor.ui.registry.addButton('customWordExport', {
        text: 'Export Word',
        tooltip: 'Export to Word',
        onAction: function () {
          const content = editor.getContent();
          const blob = new Blob(['\ufeff' + content], {
            type: 'application/msword'
          });

          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = 'Document.doc';
          link.click();
        }
      });
    }
  });
}