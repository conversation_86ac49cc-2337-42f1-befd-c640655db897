﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using NHATTAMID2025.Application.Common.Interfaces;
using NHATTAMID2025.Application.Common.Models;
using NHATTAMID2025.Application.HeThong.DangKyUser.DTOs;
using WEB_DLL;

namespace NHATTAMID2025.Application.HeThong.DangKyUser.Commands;
internal class TaoMatKhauMoi
{
}
public record TaoMatKhauMoiCommand(string Email, string MatMaMoi, string NhapLaiMatMaMoi, string MaXatNhan) : IRequest<Result<UsersDangKyDto>?>;
public class TaoMatKhauMoiValidator : AbstractValidator<TaoMatKhauMoiCommand>
{
    private readonly IApplicationDbContext _context;
    public TaoMatKhauMoiValidator(IApplicationDbContext context)
    {
        _context = context;
        RuleFor(cmd => cmd.MatMaMoi)
           .NotEmpty().WithMessage("Mật khẩu không được để trống.")
           .MinimumLength(8).WithMessage("Mật khẩu phải có ít nhất 8 ký tự.")
           .Matches("[A-Z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái in hoa.")
           .Matches("[a-z]").WithMessage("Mật khẩu phải có ít nhất một chữ cái thường.")
           .Matches("[0-9]").WithMessage("Mật khẩu phải có ít nhất một chữ số.")
           .Matches("[^a-zA-Z0-9]").WithMessage("Mật khẩu phải có ít nhất một ký tự đặc biệt.");
        RuleFor(cmd => cmd.NhapLaiMatMaMoi)
           .NotEmpty().WithMessage("Vui lòng nhập lại mật khẩu.")
           .Equal(cmd => cmd.MatMaMoi).WithMessage("Mật khẩu nhập lại không trùng khớp.");
    }
}
public class TaoMatKhauMoiCommandHandler : IRequestHandler<TaoMatKhauMoiCommand, Result<UsersDangKyDto>?>
{
    private readonly IApplicationDbContext _context;
    public TaoMatKhauMoiCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }
    public async Task<Result<UsersDangKyDto>?> Handle(TaoMatKhauMoiCommand request, CancellationToken cancellationToken)
    {
        var ma = await _context.MaXacThuc
                       .Where(x => x.Email == request.Email && x.MaXacThuc == request.MaXatNhan)
                       .OrderByDescending(x => x.ThoiGianTao)
                       .FirstOrDefaultAsync();

        if (ma == null)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực không đúng"]);

        if (ma.TrangThai == "Used")
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã qua sử dụng"]);

        if (ma.ThoiHan < DateTime.UtcNow)
            return Result<UsersDangKyDto>.Failure(["Mã xác thực đã hết hạn."]);
        ma.TrangThai = "Used";
        await _context.SaveChangesAsync(cancellationToken);

        string keyMaHoaMatKhau = "rateAnd2012";
        var parameters = new[]
        {
            new Microsoft.Data.SqlClient.SqlParameter("@MatMa",ntsSecurity._mEncrypt( request.MatMaMoi.Trim(), keyMaHoaMatKhau, true )),
            new Microsoft.Data.SqlClient.SqlParameter("@NhapLaiMatMa", ntsSecurity._mEncrypt( request.MatMaMoi.Trim(), request.NhapLaiMatMaMoi, true )),
            new Microsoft.Data.SqlClient.SqlParameter("@Email", request.Email)
        };
        try
        {
            var dto = await _context.ExecuteSqlQueryRawAsync(
                   "sp_TaoMatKhauMoi_Update",
                   MapFromReader,
                   true,
                   parameters
               );
            return Result<UsersDangKyDto>.Success(dto.FirstOrDefault());
        }
        catch (Exception)
        {
            return Result<UsersDangKyDto>.Failure(["Tạo mật khẩu mới thất bại."]);
        }
    }
    private UsersDangKyDto MapFromReader(System.Data.Common.DbDataReader reader)
    {
        return new UsersDangKyDto
        {
            UserID = reader.GetGuid(reader.GetOrdinal("UserID"))
        };
    }
}

